<template>
<div class="milestone-management-container">
  <el-card>
    <div slot="header">
      <span>📅 项目里程碑管理</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="addMilestone">
        添加里程碑
      </el-button>
    </div>

    <!-- 里程碑进度概览 -->
    <div class="milestone-overview" style="margin-bottom: 30px;">
      <!-- 里程碑步骤条 -->
      <div class="milestone-steps">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h4 style="margin: 0; color: #303133;">里程碑进度时间线</h4>
          <div style="font-size: 14px; color: #606266;">
            <span>当前时间：{{ currentDate }}</span>
            <span style="margin-left: 20px;" v-if="currentMilestone">
              当前进行：<strong style="color: #E6A23C;">{{ currentMilestone.name }}</strong>
            </span>
            <span style="margin-left: 20px;" v-if="nextMilestone">
              下一里程碑：<strong style="color: #409EFF;">{{ nextMilestone.name }}</strong>
              （还有 <strong style="color: #E6A23C;">{{ remainingDays }}</strong> 天）
            </span>
          </div>
        </div>

        <!-- 横向进度步骤条 -->
        <div class="horizontal-steps">
          <div class="steps-timeline">
            <div v-for="(milestone, index) in sortedMilestones" :key="milestone.id" class="timeline-item"
              :class="getStepClass(milestone)">

              <!-- 步骤节点和连接线 -->
              <div class="timeline-node-wrapper">
                <div class="timeline-node">
                  <div class="node-icon">
                    <i :class="getMilestoneStepIcon(milestone.status)"></i>
                  </div>
                </div>

                <!-- 连接线（进度条） -->
                <div v-if="index < sortedMilestones.length - 1" class="timeline-connector">
                  <div class="connector-line">
                    <div class="connector-progress" :style="getProgressStyle(milestone, index)">
                    </div>
                  </div>
                </div>
              </div>

              <!-- 步骤信息 -->
              <div class="timeline-content">
                <div class="milestone-title">{{ milestone.name }}</div>
                <div class="milestone-info">
                  <div class="info-row">
                    <span class="info-label">计划时间：</span>
                    <span class="info-value">{{ milestone.plannedDate }}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">负责人：</span>
                    <span class="info-value">{{ milestone.assignee }}</span>
                  </div>
                  <!-- <div class="info-row">
                    <el-tag :type="getMilestoneTagType(milestone.status)" size="mini">
                      {{ getMilestoneStatusText(milestone.status) }}
                    </el-tag>
                  </div> -->
                  <div v-if="milestone.startDate" class="info-row">
                    <span class="info-label">开始：</span>
                    <span class="info-value">{{ milestone.startDate }}</span>
                  </div>
                  <div v-if="milestone.actualDate" class="info-row">
                    <span class="info-label">完成：</span>
                    <span class="info-value">{{ milestone.actualDate }}</span>
                  </div>
                  <div v-if="milestone.status === 'pending'" class="info-row">
                    <span class="info-label">剩余：</span>
                    <span class="info-value" :class="getRemainingDaysClass(milestone)">
                      {{ getDaysFromNow(milestone.plannedDate) }} 天
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 详细统计卡片 -->
        <el-row :gutter="15" style="margin-top: 20px;">
          <el-col :span="6">
            <div class="stat-card completed">
              <div class="stat-icon">
                <i class="el-icon-circle-check"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ completedCount }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card in-progress">
              <div class="stat-icon">
                <i class="el-icon-loading"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ inProgressCount }}</div>
                <div class="stat-label">进行中</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card pending">
              <div class="stat-icon">
                <i class="el-icon-time"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ pendingCount }}</div>
                <div class="stat-label">待开始</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card delayed">
              <div class="stat-icon">
                <i class="el-icon-warning"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ delayedCount }}</div>
                <div class="stat-label">已延期</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 里程碑时间线 -->
    <div class="milestone-timeline" style="margin-bottom: 30px;">
      <h4 style="margin-bottom: 15px;">里程碑时间线</h4>
      <el-timeline>
        <el-timeline-item v-for="milestone in sortedMilestones" :key="milestone.id" :timestamp="milestone.plannedDate"
          :type="getMilestoneTimelineType(milestone.status)" :icon="getMilestoneIcon(milestone.status)">
          <el-card>
            <div class="milestone-card">
              <div class="milestone-header">
                <h4>{{ milestone.name }}</h4>
                <el-tag :type="getMilestoneTagType(milestone.status)">
                  {{ getMilestoneStatusText(milestone.status) }}
                </el-tag>
              </div>
              <p class="milestone-description">{{ milestone.description }}</p>
              <div class="milestone-meta">
                <span><strong>负责人：</strong>{{ milestone.assignee }}</span>
                <span><strong>优先级：</strong>{{ milestone.priority }}</span>
                <span v-if="milestone.startDate"><strong>开始时间：</strong>{{ milestone.startDate }}</span>
                <span v-if="milestone.actualDate"><strong>实际完成：</strong>{{ milestone.actualDate }}</span>
              </div>
              <div class="milestone-actions" style="margin-top: 10px;">
                <el-button size="mini" @click="editMilestone(milestone)">编辑</el-button>
                <el-button v-if="milestone.status === 'in-progress'" size="mini" type="success"
                  @click="completeMilestone(milestone)">
                  标记完成
                </el-button>
                <el-button v-if="milestone.status === 'pending' && isNextToStart(milestone)" size="mini" type="primary"
                  @click="startMilestone(milestone)">
                  开始
                </el-button>
                <el-button size="mini" type="danger" @click="deleteMilestone(milestone)">删除</el-button>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 空状态 -->
    <div v-if="milestones.length === 0" class="empty-state" style="text-align: center; padding: 40px;">
      <i class="el-icon-date" style="font-size: 64px; color: #C0C4CC;"></i>
      <p style="color: #909399; margin-top: 16px;">暂无里程碑</p>
      <el-button type="primary" @click="addMilestone">添加第一个里程碑</el-button>
    </div>
  </el-card>

  <!-- 添加/编辑里程碑对话框 -->
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false">

    <el-form :model="formData" :rules="formRules" ref="milestoneForm" label-width="120px">
      <el-form-item label="里程碑名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入里程碑名称"></el-input>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请描述里程碑的具体内容和目标">
        </el-input>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划日期" prop="plannedDate">
            <el-date-picker v-model="formData.plannedDate" type="date" placeholder="选择计划完成日期" style="width: 100%">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="assignee">
            <el-input v-model="formData.assignee" placeholder="请输入负责人"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
              <el-option label="低" value="low"></el-option>
              <el-option label="中" value="medium"></el-option>
              <el-option label="高" value="high"></el-option>
              <el-option label="紧急" value="urgent"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="待开始" value="pending"></el-option>
              <el-option label="进行中" value="in-progress"></el-option>
              <el-option label="已完成" value="completed"></el-option>
              <el-option label="已延期" value="delayed"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item v-if="formData.status === 'in-progress' || formData.status === 'completed'" label="开始日期"
        prop="startDate">
        <el-date-picker v-model="formData.startDate" type="date" placeholder="选择开始日期" style="width: 100%">
        </el-date-picker>
      </el-form-item>

      <el-form-item v-if="formData.status === 'completed'" label="实际完成日期" prop="actualDate">
        <el-date-picker v-model="formData.actualDate" type="date" placeholder="选择实际完成日期" style="width: 100%">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="交付物" prop="deliverables">
        <el-input v-model="formData.deliverables" type="textarea" :rows="2" placeholder="请描述该里程碑的交付物">
        </el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveMilestone">保存</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
export default {
  name: 'MilestoneManagement',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      editId: null,
      milestones: [],
      formData: {
        name: '',
        description: '',
        plannedDate: '',
        assignee: '',
        priority: 'medium',
        status: 'pending',
        startDate: '',
        actualDate: '',
        deliverables: ''
      },
      formRules: {
        name: [
          { required: true, message: '请输入里程碑名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入描述', trigger: 'blur' },
          { min: 10, message: '描述至少10个字符', trigger: 'blur' }
        ],
        plannedDate: [
          { required: true, message: '请选择计划日期', trigger: 'change' }
        ],
        assignee: [
          { required: true, message: '请输入负责人', trigger: 'blur' }
        ],
        priority: [
          { required: true, message: '请选择优先级', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    completedCount() {
      return this.milestones.filter(m => m.status === 'completed').length
    },
    inProgressCount() {
      return this.milestones.filter(m => m.status === 'in-progress').length
    },
    pendingCount() {
      return this.milestones.filter(m => m.status === 'pending').length
    },
    delayedCount() {
      return this.milestones.filter(m => m.status === 'delayed').length
    },
    completionRate() {
      if (this.milestones.length === 0) return 0
      return Math.round((this.completedCount / this.milestones.length) * 100)
    },
    sortedMilestones() {
      return [...this.milestones].sort((a, b) => new Date(a.plannedDate) - new Date(b.plannedDate))
    },
    // 当前日期
    currentDate() {
      return new Date().toISOString().split('T')[0]
    },
    // 当前进行中的里程碑
    currentMilestone() {
      return this.sortedMilestones.find(m => m.status === 'in-progress')
    },
    // 下一个里程碑（未完成的里程碑中最早的一个）
    nextMilestone() {
      return this.sortedMilestones.find(m => m.status === 'pending')
    },
    // 距离下一里程碑的剩余天数
    remainingDays() {
      if (!this.nextMilestone) return 0
      return this.getDaysFromNow(this.nextMilestone.plannedDate)
    }
  },
  created() {
    this.loadMilestones()
  },
  methods: {
    // 加载里程碑数据
    loadMilestones() {
      // 这里应该从API获取里程碑数据
      // 添加静态测试数据 - 基于当前时间2025年7月的项目进度
      this.milestones = [
        {
          id: '1',
          name: '项目启动',
          description: '完成项目立项、团队组建、需求分析等前期准备工作，确保项目顺利启动',
          plannedDate: '2025-01-15',
          assignee: '张三',
          priority: 'high',
          status: 'completed',
          actualDate: '2025-01-12',
          deliverables: '项目启动报告、需求分析文档、项目计划书'
        },
        {
          id: '2',
          name: '系统设计',
          description: '完成系统架构设计、数据库设计、接口设计等技术方案设计工作',
          plannedDate: '2025-03-15',
          assignee: '李四',
          priority: 'high',
          status: 'completed',
          actualDate: '2025-03-10',
          deliverables: '系统架构文档、数据库设计文档、接口设计文档'
        },
        {
          id: '3',
          name: '核心功能开发',
          description: '开发系统核心功能模块，包括用户管理、权限控制、数据处理等关键功能',
          plannedDate: '2025-05-30',
          assignee: '王五',
          priority: 'urgent',
          status: 'completed',
          actualDate: '2025-06-02',
          deliverables: '核心功能模块代码、单元测试用例、功能测试报告'
        },
        {
          id: '4',
          name: '系统集成测试',
          description: '进行系统集成测试，确保各模块间协调工作，发现并修复集成问题',
          plannedDate: '2025-07-15',
          assignee: '赵六',
          priority: 'medium',
          status: 'in-progress',
          startDate: '2025-07-10',
          actualDate: '',
          deliverables: '集成测试报告、缺陷修复记录、系统稳定性报告'
        },
        {
          id: '5',
          name: '用户验收测试',
          description: '邀请用户进行系统验收测试，收集用户反馈，完成系统优化',
          plannedDate: '2025-08-30',
          assignee: '孙七',
          priority: 'medium',
          status: 'pending',
          actualDate: '',
          deliverables: '用户验收测试报告、用户反馈汇总、系统优化方案'
        },
        {
          id: '6',
          name: '系统上线部署',
          description: '完成生产环境部署、数据迁移、系统上线等工作，确保系统正式投入使用',
          plannedDate: '2025-09-30',
          assignee: '周八',
          priority: 'high',
          status: 'pending',
          actualDate: '',
          deliverables: '部署文档、运维手册、上线报告'
        }
      ]

      // 可以调用API获取数据
      // this.fetchMilestones()
    },

    // 从API获取里程碑
    async fetchMilestones() {
      try {
        // const response = await this.$api.getMilestones(this.projectInfo.id)
        // this.milestones = response.data

        console.log('获取里程碑数据:', this.projectInfo.id)
      } catch (error) {
        console.error('获取里程碑失败:', error)
        this.$message.error('获取里程碑失败')
      }
    },

    // 添加里程碑
    addMilestone() {
      this.dialogTitle = '添加里程碑'
      this.isEdit = false
      this.editId = null
      this.formData = {
        name: '',
        description: '',
        plannedDate: '',
        assignee: '',
        priority: 'medium',
        status: 'pending',
        startDate: '',
        actualDate: '',
        deliverables: ''
      }
      this.dialogVisible = true
    },

    // 编辑里程碑
    editMilestone(milestone) {
      this.dialogTitle = '编辑里程碑'
      this.isEdit = true
      this.editId = milestone.id
      this.formData = { ...milestone }
      this.dialogVisible = true
    },

    // 保存里程碑
    saveMilestone() {
      this.$refs.milestoneForm.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            // 更新里程碑
            const index = this.milestones.findIndex(m => m.id === this.editId)
            if (index !== -1) {
              this.milestones.splice(index, 1, { ...this.formData, id: this.editId })
            }
            this.$message.success('里程碑更新成功')
          } else {
            // 添加新里程碑
            const newMilestone = {
              ...this.formData,
              id: Date.now().toString()
            }
            this.milestones.push(newMilestone)
            this.$message.success('里程碑添加成功')
          }
          this.dialogVisible = false
        }
      })
    },

    // 完成里程碑
    completeMilestone(milestone) {
      this.$confirm('确认标记该里程碑为已完成吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        milestone.status = 'completed'
        milestone.actualDate = new Date().toISOString().split('T')[0]
        this.$message.success('里程碑已标记为完成')

        // 检查是否有下一个里程碑需要自动开始
        this.checkAndUpdateNextMilestone()
      })
    },

    // 开始里程碑
    startMilestone(milestone) {
      this.$confirm('确认开始该里程碑吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        milestone.status = 'in-progress'
        milestone.startDate = new Date().toISOString().split('T')[0]
        this.$message.success('里程碑已开始')
      })
    },

    // 判断是否是下一个可开始的里程碑
    isNextToStart(milestone) {
      const sortedMilestones = this.sortedMilestones
      const currentIndex = sortedMilestones.findIndex(m => m.id === milestone.id)

      // 如果是第一个里程碑，且没有进行中的里程碑，则可以开始
      if (currentIndex === 0) {
        return !sortedMilestones.some(m => m.status === 'in-progress')
      }

      // 检查前面的里程碑是否都已完成，且没有其他进行中的里程碑
      const previousMilestones = sortedMilestones.slice(0, currentIndex)
      const allPreviousCompleted = previousMilestones.every(m => m.status === 'completed')
      const hasInProgress = sortedMilestones.some(m => m.status === 'in-progress')

      return allPreviousCompleted && !hasInProgress
    },

    // 检查并更新下一个里程碑
    checkAndUpdateNextMilestone() {
      // 这个方法在完成里程碑后调用，用于检查是否需要更新下一个里程碑的状态
      // 目前只是触发界面更新，具体逻辑可以根据需要扩展
    },

    // 删除里程碑
    deleteMilestone(milestone) {
      this.$confirm('确认删除该里程碑吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.milestones.findIndex(m => m.id === milestone.id)
        if (index !== -1) {
          this.milestones.splice(index, 1)
          this.$message.success('里程碑删除成功')
        }
      })
    },

    // 获取里程碑时间线类型
    getMilestoneTimelineType(status) {
      const typeMap = {
        'pending': 'info',
        'in-progress': 'warning',
        'completed': 'success',
        'delayed': 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取里程碑图标
    getMilestoneIcon(status) {
      const iconMap = {
        'pending': 'el-icon-time',
        'in-progress': 'el-icon-loading',
        'completed': 'el-icon-circle-check',
        'delayed': 'el-icon-warning'
      }
      return iconMap[status] || 'el-icon-time'
    },

    // 获取里程碑标签类型
    getMilestoneTagType(status) {
      const typeMap = {
        'pending': 'info',
        'in-progress': 'warning',
        'completed': 'success',
        'delayed': 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取里程碑状态文本
    getMilestoneStatusText(status) {
      const textMap = {
        'pending': '待开始',
        'in-progress': '进行中',
        'completed': '已完成',
        'delayed': '已延期'
      }
      return textMap[status] || '未知'
    },

    // 获取步骤条状态
    getStepStatus(status) {
      const statusMap = {
        'completed': 'finish',
        'in-progress': 'process',
        'delayed': 'error',
        'pending': 'wait'
      }
      return statusMap[status] || 'wait'
    },

    // 获取里程碑步骤图标
    getMilestoneStepIcon(status) {
      const iconMap = {
        'pending': 'el-icon-time',
        'in-progress': 'el-icon-loading',
        'completed': 'el-icon-circle-check',
        'delayed': 'el-icon-warning'
      }
      return iconMap[status] || 'el-icon-time'
    },

    // 计算距离指定日期的天数
    getDaysFromNow(dateString) {
      const targetDate = new Date(dateString)
      const currentDate = new Date()
      const diffTime = targetDate - currentDate
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays
    },

    // 获取步骤样式类
    getStepClass(milestone) {
      const classes = []
      if (milestone.status === 'completed') classes.push('step-completed')
      if (milestone.status === 'in-progress') classes.push('step-in-progress')
      if (milestone.status === 'delayed') classes.push('step-delayed')
      if (milestone.status === 'pending') classes.push('step-pending')
      return classes.join(' ')
    },

    // 获取进度条样式（横向）
    getProgressStyle(milestone, index) {
      const nextMilestone = this.sortedMilestones[index + 1]
      if (!nextMilestone) return { width: '0%' }

      const currentDate = new Date()
      const startDate = new Date(milestone.plannedDate)
      const endDate = new Date(nextMilestone.plannedDate)

      // 如果当前里程碑已完成，连接线完全填充
      if (milestone.status === 'completed') {
        return {
          width: '100%',
          background: 'linear-gradient(90deg, #67C23A 0%, #5daf34 100%)',
          boxShadow: '0 2px 4px rgba(103, 194, 58, 0.3)'
        }
      }

      // 如果当前里程碑进行中，根据时间进度计算
      if (milestone.status === 'in-progress') {
        const totalDays = (endDate - startDate) / (1000 * 60 * 60 * 24)
        const passedDays = (currentDate - startDate) / (1000 * 60 * 60 * 24)
        const progress = Math.max(0, Math.min(100, (passedDays / totalDays) * 100))
        return {
          width: `${progress}%`,
          background: 'linear-gradient(90deg, #E6A23C 0%, #d4941f 100%)',
          boxShadow: '0 2px 4px rgba(230, 162, 60, 0.3)',
          animation: 'progressFlow 2s ease-in-out infinite'
        }
      }

      // 如果当前里程碑延期
      if (milestone.status === 'delayed') {
        return {
          width: '100%',
          background: 'linear-gradient(90deg, #F56C6C 0%, #f04864 100%)',
          boxShadow: '0 2px 4px rgba(245, 108, 108, 0.3)'
        }
      }

      // 如果当前里程碑未开始
      return {
        width: '0%',
        backgroundColor: '#DCDFE6'
      }
    },

    // 获取剩余天数的样式类
    getRemainingDaysClass(milestone) {
      const days = this.getDaysFromNow(milestone.plannedDate)
      if (days < 0) return 'overdue'
      if (days <= 7) return 'urgent'
      if (days <= 30) return 'warning'
      return 'normal'
    }
  }
}
</script>

<style scoped>
.milestone-management-container {
  height: 100%;
}

.milestone-overview {
  margin-bottom: 30px;
}

.milestone-timeline {
  margin-bottom: 30px;
}

.milestone-card {
  padding: 10px;
}

.milestone-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.milestone-header h4 {
  margin: 0;
  color: #303133;
}

.milestone-description {
  color: #606266;
  margin-bottom: 10px;
}

.milestone-meta {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 10px;
}

.milestone-actions {
  margin-top: 10px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  text-align: right;
}

/* 横向步骤条样式 */
.horizontal-steps {
  width: 100%;
  overflow-x: auto;
  padding: 20px 0;
}

.steps-timeline {
  display: flex;
  align-items: flex-start;
  min-width: 100%;
  position: relative;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 150px;
  position: relative;
}

.timeline-node-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  justify-content: center;
  height: 50px;
}

.timeline-node {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  position: relative;
  width: 50px;
  height: 50px;
}

.node-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  border: 3px solid #DCDFE6;
  background: #fff;
  transition: all 0.3s ease;
  position: relative;
  z-index: 3;
  box-sizing: border-box;
}

.timeline-connector {
  position: absolute;
  left: calc(50% + 25px);
  top: 50%;
  transform: translateY(-50%);
  width: calc(100% - 50px);
  height: 6px;
  z-index: 1;
}

.connector-line {
  width: 100%;
  height: 100%;
  background: #DCDFE6;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.connector-progress {
  height: 100%;
  transition: all 0.5s ease;
  border-radius: 3px;
  position: relative;
}

.timeline-content {
  margin-top: 15px;
  text-align: center;
  width: 100%;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.milestone-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
}

.milestone-info {
  font-size: 12px;
  color: #606266;
}

.info-row {
  margin-bottom: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.info-label {
  color: #909399;
  margin-right: 4px;
  font-weight: 500;
}

.info-value {
  color: #606266;
}



/* 不同状态的节点样式 */
.timeline-item.step-completed .node-icon {
  background: #67C23A;
  border-color: #67C23A;
  color: #fff;
  box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.2);
  margin: 0;
  transform: none;
}

.timeline-item.step-in-progress .node-icon {
  background: #E6A23C;
  border-color: #E6A23C;
  color: #fff;
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 4px rgba(230, 162, 60, 0.2);
  margin: 0;
  transform: none;
}

.timeline-item.step-delayed .node-icon {
  background: #F56C6C;
  border-color: #F56C6C;
  color: #fff;
  box-shadow: 0 0 0 4px rgba(245, 108, 108, 0.2);
  margin: 0;
  transform: none;
}

.timeline-item.step-pending .node-icon {
  background: #fff;
  border-color: #DCDFE6;
  color: #909399;
  margin: 0;
  transform: none;
}

/* 剩余天数颜色 */
.overdue {
  color: #F56C6C;
  font-weight: bold;
}

.urgent {
  color: #E6A23C;
  font-weight: bold;
}

.warning {
  color: #E6A23C;
}

.normal {
  color: #67C23A;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0.7);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(230, 162, 60, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0);
  }
}

/* 统计卡片样式 */
.stat-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  line-height: 1;
}

/* 不同状态的卡片颜色 */
.stat-card.completed .stat-icon {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.stat-card.completed .stat-number {
  color: #67C23A;
}

.stat-card.in-progress .stat-icon {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.stat-card.in-progress .stat-number {
  color: #E6A23C;
}

.stat-card.pending .stat-icon {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.stat-card.pending .stat-number {
  color: #409EFF;
}

.stat-card.delayed .stat-icon {
  background: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.stat-card.delayed .stat-number {
  color: #F56C6C;
}
</style>
