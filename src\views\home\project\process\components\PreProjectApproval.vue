<template>
  <div class="pre-project-approval-container">
    <el-card>
      <div slot="header">
        <span>🔄 预立项审批管理</span>
        <el-button v-if="!hasSubmitted" style="float: right; padding: 3px 0" type="text" @click="startSubmission">
          提交预立项申请
        </el-button>
      </div>

      <!-- 审批状态显示 -->
      <div class="approval-status" style="margin-bottom: 20px;">
        <el-alert :title="getStatusTitle()" :type="getStatusType()" :description="getStatusDescription()" show-icon
          :closable="false">
        </el-alert>
      </div>

      <!-- 预立项信息显示 -->
      <div v-if="hasSubmitted" class="approval-content">
        <h4 style="margin-bottom: 15px; color: #606266;">预立项基本信息</h4>
        <el-descriptions :column="2" border style="margin-bottom: 20px;">
          <el-descriptions-item label="项目名称">{{ submissionData.projectName }}</el-descriptions-item>
          <el-descriptions-item label="委托单位">{{ submissionData.delegateUnit }}</el-descriptions-item>
          <el-descriptions-item label="项目编号">{{ submissionData.projectNumber }}</el-descriptions-item>
          <el-descriptions-item label="项目负责人">{{ submissionData.projectLeader }}</el-descriptions-item>
          <el-descriptions-item label="立项时间">{{ submissionData.projectStartTime }}</el-descriptions-item>
          <el-descriptions-item label="预估收入">{{ submissionData.estimatedIncome }}万元</el-descriptions-item>
          <el-descriptions-item label="项目描述" :span="2">
            <div style="white-space: pre-wrap;">{{ submissionData.projectDescription }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 审批流程进度 -->
        <div v-if="projectInfo.status === '1' || projectInfo.status === '2'">
          <h4 style="margin-bottom: 15px; color: #606266;">审批流程进度</h4>
          <el-steps :active="getActiveStep()" direction="vertical" :process-status="getProcessStatus()"
            :finish-status="getFinishStatus()">

            <el-step title="部门负责人审批">
              <div slot="description">
                <p>审批人：张三</p>
                <p v-if="projectInfo.status === '1'" style="color: #67C23A; font-size: 12px;">
                  ✓ 已通过 (2024-01-15 10:30)
                </p>
                <p v-else-if="projectInfo.status === '2'" style="color: #67C23A; font-size: 12px;">
                  ✓ 已通过 (2024-01-15 10:30)
                </p>
                <p v-else style="color: #E6A23C; font-size: 12px;">⏳ 审批中...</p>
                <p style="color: #909399; font-size: 12px;">
                  {{ projectInfo.status === '1' || projectInfo.status === '2' ? '审批意见：同意该课题立项，技术方案可行。' : '等待部门负责人审批' }}
                </p>
              </div>
            </el-step>

            <el-step title="技术总监审批">
              <div slot="description">
                <p>审批人：李四</p>
                <p v-if="projectInfo.status === '1'" style="color: #67C23A; font-size: 12px;">
                  ✓ 已通过 (2024-01-16 14:20)
                </p>
                <p v-else-if="projectInfo.status === '2'" style="color: #E6A23C; font-size: 12px;">
                  ⏳ 审批中...
                </p>
                <p v-else style="color: #909399; font-size: 12px;">⏸ 等待中...</p>
                <p style="color: #909399; font-size: 12px;">
                  {{ projectInfo.status === '1' ? '审批意见：技术路线清晰，预算合理，同意立项。' : '等待技术总监审批' }}
                </p>
              </div>
            </el-step>

            <el-step title="总经理审批">
              <div slot="description">
                <p>审批人：王五</p>
                <p v-if="projectInfo.status === '1'" style="color: #67C23A; font-size: 12px;">
                  ✓ 已通过 (2024-01-17 09:15)
                </p>
                <p v-else style="color: #909399; font-size: 12px;">⏸ 等待中...</p>
                <p style="color: #909399; font-size: 12px;">
                  {{ projectInfo.status === '1' ? '审批意见：项目具有良好的市场前景，批准立项。' : '等待总经理审批' }}
                </p>
              </div>
            </el-step>
          </el-steps>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state" style="text-align: center; padding: 40px;">
        <i class="el-icon-s-check" style="font-size: 64px; color: #C0C4CC;"></i>
        <p style="color: #909399; margin-top: 16px;">尚未提交预立项申请</p>
        <el-button type="primary" @click="startSubmission">提交预立项申请</el-button>
      </div>
    </el-card>

    <!-- 提交预立项申请对话框 -->
    <el-dialog title="提交预立项申请" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false">

      <el-form :model="formData" :rules="formRules" ref="submissionForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="formData.projectName" placeholder="请输入项目名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位" prop="delegateUnit">
              <el-input v-model="formData.delegateUnit" placeholder="请输入委托单位"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目编号" prop="projectNumber">
              <el-input v-model="formData.projectNumber" placeholder="请输入项目编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人" prop="projectLeader">
              <el-input v-model="formData.projectLeader" placeholder="请输入项目负责人"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="立项时间" prop="projectStartTime">
              <el-date-picker v-model="formData.projectStartTime" type="date" placeholder="选择立项时间" style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估收入" prop="estimatedIncome">
              <el-input v-model="formData.estimatedIncome" placeholder="请输入预估收入">
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="项目描述" prop="projectDescription">
          <el-input v-model="formData.projectDescription" type="textarea" :rows="4" placeholder="请详细描述项目内容、目标和意义">
          </el-input>
        </el-form-item>

        <el-form-item label="技术要求" prop="technicalRequirements">
          <el-input v-model="formData.technicalRequirements" type="textarea" :rows="3" placeholder="请描述项目的技术要求和难点">
          </el-input>
        </el-form-item>

        <el-form-item label="交付成果" prop="deliverables">
          <el-input v-model="formData.deliverables" type="textarea" :rows="3" placeholder="请描述项目的交付成果和验收标准">
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitApplication">提交申请</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PreProjectApproval',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      hasSubmitted: false,
      submissionData: {},
      formData: {
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        projectStartTime: '',
        estimatedIncome: '',
        projectDescription: '',
        technicalRequirements: '',
        deliverables: ''
      },
      formRules: {
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        delegateUnit: [
          { required: true, message: '请输入委托单位', trigger: 'blur' }
        ],
        projectNumber: [
          { required: true, message: '请输入项目编号', trigger: 'blur' }
        ],
        projectLeader: [
          { required: true, message: '请输入项目负责人', trigger: 'blur' }
        ],
        projectStartTime: [
          { required: true, message: '请选择立项时间', trigger: 'change' }
        ],
        estimatedIncome: [
          { required: true, message: '请输入预估收入', trigger: 'blur' },
          { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
        ],
        projectDescription: [
          { required: true, message: '请输入项目描述', trigger: 'blur' },
          { min: 20, message: '项目描述至少20个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loadSubmissionData()
  },
  methods: {
    // 加载提交数据
    loadSubmissionData() {
      // 根据项目状态判断是否已提交
      this.hasSubmitted = ['1', '2'].includes(this.projectInfo.status)

      if (this.hasSubmitted) {
        // 从项目信息中获取已提交的数据
        this.submissionData = {
          projectName: this.projectInfo.subjectName || '未知项目',
          delegateUnit: this.projectInfo.delegateUnit || '未知单位',
          projectNumber: this.projectInfo.projectNumber || '未知编号',
          projectLeader: this.projectInfo.leader || '未知负责人',
          projectStartTime: this.projectInfo.startTime || '未知时间',
          estimatedIncome: this.projectInfo.estimatedIncome || '0',
          projectDescription: this.projectInfo.mainContent || '暂无描述'
        }
      }

      // 可以在这里调用API获取更详细的预立项数据
      // this.fetchPreProjectData()
    },

    // 可选：从API获取预立项数据
    async fetchPreProjectData() {
      try {
        // 这里应该调用实际的API
        // const response = await this.$api.getPreProjectData(this.projectInfo.id)
        // this.submissionData = response.data

        console.log('获取预立项数据:', this.projectInfo.id)
      } catch (error) {
        console.error('获取预立项数据失败:', error)
        this.$message.error('获取预立项数据失败')
      }
    },

    // 开始提交申请
    startSubmission() {
      this.dialogVisible = true
      // 预填充一些基础信息
      this.formData = {
        projectName: this.projectInfo.subjectName || '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: this.projectInfo.leader || '',
        projectStartTime: '',
        estimatedIncome: '',
        projectDescription: this.projectInfo.mainContent || '',
        technicalRequirements: '',
        deliverables: ''
      }
    },

    // 提交申请
    submitApplication() {
      this.$refs.submissionForm.validate((valid) => {
        if (valid) {
          this.submissionData = { ...this.formData }
          this.hasSubmitted = true
          this.dialogVisible = false

          // 更新项目状态为审批中
          this.$emit('update-status', '2')

          this.$message.success('预立项申请提交成功，等待审批')
        }
      })
    },

    // 获取激活步骤
    getActiveStep() {
      if (this.projectInfo.status === '1') return 3 // 全部完成
      if (this.projectInfo.status === '2') return 1 // 审批中
      return 0
    },

    // 获取进程状态
    getProcessStatus() {
      return this.projectInfo.status === '2' ? 'process' : 'finish'
    },

    // 获取完成状态
    getFinishStatus() {
      return this.projectInfo.status === '1' ? 'success' : 'process'
    },

    // 获取状态标题
    getStatusTitle() {
      if (!this.hasSubmitted) return '未提交预立项申请'
      if (this.projectInfo.status === '1') return '预立项审批已通过'
      if (this.projectInfo.status === '2') return '预立项审批进行中'
      return '预立项申请已提交'
    },

    // 获取状态类型
    getStatusType() {
      if (!this.hasSubmitted) return 'info'
      if (this.projectInfo.status === '1') return 'success'
      if (this.projectInfo.status === '2') return 'warning'
      return 'info'
    },

    // 获取状态描述
    getStatusDescription() {
      if (!this.hasSubmitted) return '请提交预立项申请以启动审批流程'
      if (this.projectInfo.status === '1') return '恭喜！预立项申请已通过所有审批环节'
      if (this.projectInfo.status === '2') return '预立项申请正在审批中，请耐心等待'
      return '预立项申请已提交，等待审批'
    }
  }
}
</script>

<style scoped>
.pre-project-approval-container {
  height: 100%;
}

.approval-status {
  margin-bottom: 20px;
}

.approval-content {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  text-align: right;
}
</style>
