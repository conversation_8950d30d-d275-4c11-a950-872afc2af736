<template>
  <div>
    <el-table
      :data="data"
      :stripe="config.stripe"
      :border="config.border"
      :style="config.style"
      :height="config.height"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-for="(item, index) in columns"
        :key="item.prop || index"
        :label="item.label"
        :type="item.type === 'selection' ? 'selection' : undefined"
        :prop="item.prop"
        :width="item.width"
        :show-overflow-tooltip="item.showOverflowTooltip"
      >

        <template slot-scope="scope">
          <!-- 状态列特殊处理 -->
          <el-tag
            v-if="item.type === 'tag'"
            :type="getStatusTagType(scope.row.status)"
            size="small"
          >
            {{ getStatusTagText(scope.row.status) }}
          </el-tag>
          <!-- selctction列特殊处理 -->
          <el-checkbox
            v-else-if="item.type === 'selection'"
            @change="(val) => handleSelectionChange(val, scope.row)"
            v-model="scope.row.isSelected"
          />
          <!-- 普通列显示 -->
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <slot name="operation"></slot>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'QTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      default: () => []
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    // 处理选择变化
    handleSelectionChange(_, row) {
      this.$emit('selection-change', row);
    },
    getStatusTagType(status) {
      const statusConfig = ['info', 'success', 'warning']
      return statusConfig[status] || 'info'
    },
    getStatusTagText(status) {
      const textConfig = ['草稿', '审批通过', '审批中']
      return textConfig[status] || '未知状态'
    }
  },
}
</script>

<style scoped lang=''>

</style>
