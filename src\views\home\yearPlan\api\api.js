const tableData = [{
  id: 1,
  subjectName: '盆地低渗透油气田含油污水回用处理技术及工程应用',
  subjectSource: '省国资委',
  leader: '田野',
  researchMethod: '自主',
  mainContent: '针对鄂尔多斯盆地低渗透油气田含油污水处理与循环利用、环境保护与储层保护等技术难题进行了多学科联合攻关，融合了化学化工、环境工程、石油工程等学科理论与技术，取得了以下重大创新与突破',
  expectedResults: '发明了含油、含甲醇采气污水预处理方法，形成了甲醇回收处理工艺技术。发明了以氮气气浮除油方法为主的含油污水综合处理技术，提出了一种全新的含油污水处理、回注工艺。自主研发了车载式作业废液密闭处理装置，形成了作业废液就地处理后直接进入油水集输系统的新技术。创新了煅烧污泥用作高强度混凝土添加剂技术；并应用自主研发的高温动态腐蚀检测仪，系统研究了油气田各种水系统的腐蚀结垢机理，开发出了系列、高效、低成本水处理药剂，形成了防腐阻垢成套技术。',
  startTime: '2025-07-17',
  endTime: '2025-12-17',
  researchPeriod: '2025/7/17----2025/12/17',
  status: '1', // 0 草稿 1 审批通过 2 审批中
  year: '2024',
  isMajorProject: '1',
},
{
  id: 2,
  subjectName: '智能制造关键技术研究与应用',
  subjectSource: '科技部',
  leader: '李明',
  researchMethod: '自主',
  mainContent: '围绕智能制造核心技术，开展工业互联网、人工智能、大数据等关键技术研究，构建智能制造技术体系，推动制造业数字化转型升级。',
  expectedResults: '形成一套完整的智能制造技术解决方案，建立智能制造示范生产线，提升生产效率30%以上，降低生产成本20%以上。',
  startTime: '2024-01-01',
  endTime: '2026-12-31',
  researchPeriod: '2024/1/1----2026/12/31',
  status: '0', // 0 草稿 1 审批通过 2 审批中
  year: '2024',
  isMajorProject: '1',
},
{
  id: 3,
  subjectName: '新能源汽车动力电池回收利用技术',
  subjectSource: '国家自然科学基金',
  leader: '王芳',
  researchMethod: '自主',
  mainContent: '研究新能源汽车动力电池的回收处理技术，开发高效的电池材料回收工艺，实现电池材料的循环利用，减少环境污染。',
  expectedResults: '建立完整的动力电池回收技术体系，实现锂、钴、镍等关键材料回收率达到95%以上，形成产业化应用示范。',
  startTime: '2024-03-01',
  endTime: '2027-02-28',
  researchPeriod: '2024/3/1----2027/2/28',
  status: '2', // 0 草稿 1 审批通过 2 审批中
  year: '2024',
  isMajorProject: '1',
},
{
  id: 4,
  subjectName: '5G通信网络优化算法研究',
  subjectSource: '教育部',
  leader: '张伟',
  researchMethod: '联合',
  mainContent: '针对5G网络的复杂性和多样性，研究网络优化算法，提升网络性能和用户体验，降低网络运营成本。',
  expectedResults: '提出新的5G网络优化算法，网络容量提升40%，延迟降低50%，能耗降低30%。',
  startTime: '2024-06-01',
  endTime: '2026-05-31',
  researchPeriod: '2024/6/1----2026/5/31',
  status: '1', // 0 草稿 1 审批通过 2 审批中
  year: '2024',
  isMajorProject: '0',
},
{
  id: 5,
  subjectName: '生物医学材料表面改性技术',
  subjectSource: '企业自主',
  leader: '陈红',
  researchMethod: '联合',
  mainContent: '开发新型生物医学材料表面改性技术，提高材料的生物相容性和功能性，应用于医疗器械和植入材料。',
  expectedResults: '开发出具有优异生物相容性的表面改性技术，材料植入成功率提升至98%以上。',
  startTime: '2024-09-01',
  endTime: '2025-08-31',
  researchPeriod: '2024/9/1----2025/8/31',
  status: '0', // 0 草稿 1 审批通过 2 审批中
  year: '2024',
  isMajorProject: '1',
}]

function getTableData() {
  return new Promise((resolve, reject) => {
    // 模拟网络延迟
    setTimeout(() => {
      try {
        // 模拟成功响应
        resolve({
          code: 200,
          message: '获取数据成功',
          data: tableData,
          total: tableData.length
        })
      } catch (error) {
        // 模拟错误响应
        reject({
          code: 500,
          message: '获取数据失败',
          error: error.message
        })
      }
    }, 500) // 模拟500ms的网络延迟
  })
}

// 模拟分页查询
function getTableDataByPage(page = 1, pageSize = 10, queryParams) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        let filteredData = [...tableData]

        // 根据查询参数过滤数据
        if (queryParams && queryParams.subjectName && queryParams.subjectName.trim()) {
          const keyword = queryParams.subjectName.trim()
          filteredData = filteredData.filter(item =>
            item.subjectName.toLowerCase().includes(keyword.toLowerCase())
          )
        }

        // 其他查询条件
        if (queryParams && queryParams.subjectSource && queryParams.subjectSource.trim()) {
          filteredData = filteredData.filter(item =>
            item.subjectSource.toLowerCase().includes(queryParams.subjectSource.toLowerCase())
          )
        }

        if (queryParams && queryParams.leader && queryParams.leader.trim()) {
          filteredData = filteredData.filter(item =>
            item.leader === queryParams.leader
          )
        }

        if (queryParams && queryParams.status && queryParams.status.trim()) {
          filteredData = filteredData.filter(item =>
            item.status === queryParams.status
          )
        }

        // 分页处理
        const total = filteredData.length
        const start = (page - 1) * pageSize
        const end = start + pageSize
        const pageData = filteredData.slice(start, end)

        resolve({
          code: 200,
          message: '获取数据成功',
          data: pageData,
          total: total,
          page: page,
          pageSize: pageSize
        })
      } catch (error) {
        reject({
          code: 500,
          message: '获取数据失败',
          error: error.message
        })
      }
    }, Math.random() * 1000 + 300) // 随机300-1300ms延迟，模拟真实网络环境
  })
}

export {
  getTableData,
  getTableDataByPage
}
