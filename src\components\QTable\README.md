# QTable 组件使用说明

## 📋 组件概述

QTable 是基于 Element UI 的 el-table 封装的通用表格组件，提供了更简洁的配置方式和常用功能的快速实现。

## ✨ 主要特性

- 🎯 **简化配置**：通过 columns 数组快速定义表格列
- 🏷️ **状态标签**：内置状态标签显示功能
- ☑️ **选择功能**：支持行选择和多选
- 🎨 **灵活样式**：支持条纹、边框等样式配置
- 🔧 **插槽扩展**：支持操作列等自定义内容

## 📦 Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| data | Array | [] | 表格数据 |
| columns | Array | [] | 列配置数组 |
| config | Object | {} | 表格配置对象 |
| tagConfig | Array | [] | 状态标签文本配置 |
| statusConfig | Array | [] | 状态标签类型配置 |

### columns 配置项

每个列对象支持以下属性：

| 属性 | 类型 | 说明 | 示例 |
|------|------|------|------|
| label | String | 列标题 | '姓名' |
| prop | String | 对应数据字段 | 'name' |
| type | String | 列类型：'selection'、'tag' | 'tag' |
| width | String/Number | 列宽度 | '120px' 或 120 |
| showOverflowTooltip | Boolean | 超出显示省略号 | true |

### config 配置项

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| stripe | Boolean | false | 是否显示斑马纹 |
| border | Boolean | false | 是否显示边框 |
| height | String/Number | - | 表格高度 |
| style | Object | {} | 自定义样式 |

## 🎯 Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| selection-change | 选择项发生变化时触发 | 当前选中的行数据 |

## 🔧 Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| operation | 操作列插槽，用于自定义操作按钮 |

## 💡 使用示例

### 基础用法

```vue
<template>
  <QTable
    :data="tableData"
    :columns="columns"
    :config="tableConfig"
  />
</template>

<script>
import QTable from '@/components/QTable'

export default {
  components: {
    QTable
  },
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', age: 25, status: 0 },
        { id: 2, name: '李四', age: 30, status: 1 }
      ],
      columns: [
        { label: 'ID', prop: 'id', width: '80' },
        { label: '姓名', prop: 'name', width: '120' },
        { label: '年龄', prop: 'age', width: '80' }
      ],
      tableConfig: {
        stripe: true,
        border: true,
        height: '400px'
      }
    }
  }
}
</script>
```

### 带状态标签的表格

```vue
<template>
  <QTable
    :data="tableData"
    :columns="columns"
    :config="tableConfig"
    :status-config="statusConfig"
    :tag-config="tagConfig"
  />
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { id: 1, name: '项目A', status: 0 },
        { id: 2, name: '项目B', status: 1 },
        { id: 3, name: '项目C', status: 2 }
      ],
      columns: [
        { label: 'ID', prop: 'id', width: '80' },
        { label: '项目名称', prop: 'name', width: '200' },
        { label: '状态', type: 'tag', width: '100' }
      ],
      tableConfig: {
        stripe: true,
        border: true
      },
      // 状态标签类型配置（对应 Element UI 的 tag type）
      statusConfig: ['info', 'success', 'warning'],
      // 状态标签文本配置
      tagConfig: ['草稿', '已完成', '进行中']
    }
  }
}
</script>
```

### 带选择功能的表格

```vue
<template>
  <QTable
    :data="tableData"
    :columns="columns"
    @selection-change="handleSelectionChange"
  />
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', age: 25, isSelected: false },
        { id: 2, name: '李四', age: 30, isSelected: false }
      ],
      columns: [
        { type: 'selection' }, // 选择列
        { label: 'ID', prop: 'id', width: '80' },
        { label: '姓名', prop: 'name', width: '120' },
        { label: '年龄', prop: 'age', width: '80' }
      ]
    }
  },
  methods: {
    handleSelectionChange(row) {
      console.log('选中的行:', row)
    }
  }
}
</script>
```

### 带操作列的表格

```vue
<template>
  <QTable
    :data="tableData"
    :columns="columns"
    :config="tableConfig"
  >
    <template #operation>
      <el-table-column label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </template>
  </QTable>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', age: 25 },
        { id: 2, name: '李四', age: 30 }
      ],
      columns: [
        { label: 'ID', prop: 'id', width: '80' },
        { label: '姓名', prop: 'name', width: '120' },
        { label: '年龄', prop: 'age', width: '80' }
      ],
      tableConfig: {
        stripe: true,
        border: true
      }
    }
  },
  methods: {
    handleEdit(row) {
      console.log('编辑:', row)
    },
    handleDelete(row) {
      console.log('删除:', row)
    }
  }
}
</script>
```

## 🎨 样式定制

组件支持通过 config.style 传入自定义样式：

```javascript
tableConfig: {
  stripe: true,
  border: true,
  style: {
    width: '100%',
    marginTop: '20px'
  }
}
```

## ⚠️ 注意事项

1. **状态标签配置**：使用状态标签时，statusConfig 和 tagConfig 数组的索引要对应数据中的 status 值
2. **选择功能**：使用选择功能时，数据对象需要包含 isSelected 字段
3. **列类型**：目前支持 'selection' 和 'tag' 两种特殊类型
4. **插槽使用**：操作列建议使用 operation 插槽，保持组件的灵活性

## 🔄 版本更新

- v1.0.0：基础表格功能
- v1.1.0：新增状态标签支持
- v1.2.0：新增选择功能支持
- v1.3.0：新增操作列插槽支持

## 🚀 高级用法

### 动态列配置

```javascript
// 根据权限动态显示列
computed: {
  dynamicColumns() {
    const baseColumns = [
      { label: 'ID', prop: 'id', width: '80' },
      { label: '姓名', prop: 'name', width: '120' }
    ]

    if (this.hasPermission('view_age')) {
      baseColumns.push({ label: '年龄', prop: 'age', width: '80' })
    }

    if (this.hasPermission('view_status')) {
      baseColumns.push({ label: '状态', type: 'tag', width: '100' })
    }

    return baseColumns
  }
}
```

### 响应式表格配置

```javascript
// 根据屏幕尺寸调整表格配置
computed: {
  responsiveConfig() {
    return {
      stripe: true,
      border: this.$store.state.isMobile ? false : true,
      height: this.$store.state.isMobile ? '300px' : '500px'
    }
  }
}
```

## 🛠️ 最佳实践

### 1. 数据结构规范

```javascript
// 推荐的数据结构
tableData: [
  {
    id: 1,                    // 唯一标识
    name: '张三',             // 业务数据
    status: 0,               // 状态值（数字）
    isSelected: false,       // 选择状态
    createTime: '2024-01-01' // 时间字段
  }
]
```

### 2. 列配置最佳实践

```javascript
// 推荐的列配置方式
columns: [
  // 选择列放在最前面
  { type: 'selection' },

  // ID列通常较窄
  { label: 'ID', prop: 'id', width: '80' },

  // 文本列设置合适宽度和溢出提示
  {
    label: '描述',
    prop: 'description',
    width: '200',
    showOverflowTooltip: true
  },

  // 状态列使用标签类型
  { label: '状态', type: 'tag', width: '100' }
]
```

### 3. 性能优化建议

```javascript
// 大数据量时使用虚拟滚动
tableConfig: {
  height: '400px', // 固定高度启用虚拟滚动
  stripe: true,
  border: true
}

// 避免在模板中使用复杂计算
// ❌ 不推荐
{{ formatDate(scope.row.createTime) }}

// ✅ 推荐：在数据处理时格式化
computed: {
  formattedTableData() {
    return this.rawTableData.map(item => ({
      ...item,
      formattedCreateTime: this.formatDate(item.createTime)
    }))
  }
}
```

## 🐛 常见问题

### Q1: 状态标签不显示或显示错误？
**A:** 检查 statusConfig 和 tagConfig 数组长度是否匹配，索引是否对应数据中的 status 值。

```javascript
// 确保配置数组长度匹配
statusConfig: ['info', 'success', 'warning'], // 索引 0,1,2
tagConfig: ['草稿', '已完成', '进行中'],        // 索引 0,1,2
// 数据中 status: 0 对应 'info' 和 '草稿'
```

### Q2: 选择功能不工作？
**A:** 确保数据对象包含 isSelected 字段，并且初始值为 false。

```javascript
// 正确的数据结构
tableData: [
  { id: 1, name: '张三', isSelected: false }
]
```

### Q3: 操作列不显示？
**A:** 检查是否正确使用了 operation 插槽。

```vue
<!-- 正确用法 -->
<QTable>
  <template #operation>
    <el-table-column label="操作">
      <!-- 操作按钮 -->
    </el-table-column>
  </template>
</QTable>
```

## 🔧 扩展开发

如需为组件添加新功能，可以参考以下扩展点：

### 1. 新增列类型

```javascript
// 在 template 中添加新的条件判断
<template slot-scope="scope">
  <!-- 新增图片类型 -->
  <img
    v-if="item.type === 'image'"
    :src="scope.row[item.prop]"
    style="width: 50px; height: 50px;"
  />
  <!-- 其他类型... -->
</template>
```

### 2. 新增配置选项

```javascript
// 在 props 中添加新配置
props: {
  // 现有配置...
  customConfig: {
    type: Object,
    default: () => ({})
  }
}
```

## 🤝 贡献指南

如需扩展组件功能，请遵循以下原则：
1. 保持 API 的向后兼容性
2. 添加适当的类型检查和默认值
3. 更新使用文档和示例
4. 确保代码风格一致
5. 添加相应的测试用例
