<template>
<div style="background-color: white; padding: 20px">
  <!-- 查询工具栏 -->
  <el-form ref="queryForm" :model="queryForm" :rules="formRules" label-width="150px">
    <!-- 第一行查询条件 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="课题名称" prop="subjectName">
          <el-input v-model="queryForm.subjectName" clearable placeholder="请输入课题名称关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="课题来源" prop="subjectSource">
          <el-input v-model="queryForm.subjectSource" clearable placeholder="请输入课题来源关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="负责人" prop="leader">
          <el-select v-model="queryForm.leader" clearable placeholder="请选择负责人" filterable>
            <el-option v-for="person in oaPersonnelList" :key="person.id"
              :label="`${person.name} (${person.department})`" :value="person.name">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="研究方式" prop="researchMethod">
          <el-select v-model="queryForm.researchMethod" clearable placeholder="请选择研究方式">
            <el-option label="自主" value="自主"></el-option>
            <el-option label="联合" value="联合"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 第二行查询条件 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="主要内容及预期成果" prop="contentAndResults">
          <el-input v-model="queryForm.contentAndResults" clearable placeholder="请输入内容或成果关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="审批状态" prop="approvalStatus">
          <el-select v-model="queryForm.approvalStatus" clearable placeholder="请选择审批状态">
            <el-option label="审批通过" value="1"></el-option>
            <el-option label="审批中" value="2"></el-option>
            <el-option label="未提交审批" value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <!-- 操作工具栏 -->
  <div style="margin-bottom: 20px">
    <el-button type="primary" @click="handleAdd">新增</el-button>
  </div>

  <!-- 表格 -->
  <el-table :data="currentPageData" border stripe style="width: 100%" height="400px"
    @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="55"></el-table-column>
    <el-table-column prop="id" label="序号" width="50" align="center"></el-table-column>
    <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip width="auto"></el-table-column>
    <el-table-column prop="delegateUnit" label="委托单位" show-overflow-tooltip width="120"
      align="center"></el-table-column>
    <el-table-column prop="projectNumber" label="项目编码" show-overflow-tooltip width="120"></el-table-column>
    <el-table-column prop="projectLeader" label="项目负责人" show-overflow-tooltip width="100"
      align="center"></el-table-column>
    <el-table-column prop="expectedContent" label="研究内容" show-overflow-tooltip width="auto"></el-table-column>
    <el-table-column prop="expectedResults" label="预期成果" show-overflow-tooltip width="auto"></el-table-column>
    <el-table-column prop="status" label="审批状态" width="100" align="center">
      <template slot-scope="scope">
        <el-tag :type="getStatusType(scope.row.status)">
          {{ getStatusText(scope.row.status) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="240" class-name="operation-column">
      <template slot-scope="scope">
        <div class="operation-buttons">
          <el-button size="mini" type="primary" @click="handleProjectApproval(scope.row)">立项审批</el-button>
          <el-button size="mini" type="primary" @click="handleDetail(scope.row)">详情</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </div>
      </template>
    </el-table-column>
  </el-table>

  <!-- 分页 -->
  <div style="margin-top: 20px;float:right">
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
      :total="totalCount">
    </el-pagination>
  </div>

  <!-- 新增/编辑对话框 -->
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" @close="resetForm">
    <el-form ref="dataForm" :model="formData" :rules="formRules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="选择预立项课题">
            <el-select v-model="value" placeholder="请选择" @change="changeSelect">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 第一部分：项目基础信息（不可编辑，从接口获取） -->
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 15px; color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 5px;">
          📋 项目基础信息（系统自动获取）
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称">
              <el-input v-model="formData.projectName" :disabled="true" placeholder="从接口获取"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位">
              <el-input v-model="formData.delegateUnit" :disabled="true" placeholder="从接口获取"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目编号">
              <el-input v-model="formData.projectNumber" :disabled="true" placeholder="从接口获取"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人">
              <el-input v-model="formData.projectLeader" :disabled="true" placeholder="从接口获取"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="研究内容">
          <el-input v-model="formData.expectedContent" type="textarea" :rows="3" :disabled="true"
            placeholder="从接口获取"></el-input>
        </el-form-item>
        <el-form-item label="预期结果">
          <el-input v-model="formData.expectedResults" type="textarea" :rows="3" :disabled="true"
            placeholder="从接口获取"></el-input>
        </el-form-item>
      </div>


    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>

  <!-- 查看详情对话框 -->
  <el-dialog title="课题详情" :visible.sync="viewDialogVisible" width="1000px">
    <div v-if="viewData">
      <!-- 课题需求详情 -->
      <div style="margin-bottom: 30px;">
        <h3 style="margin-bottom: 15px; color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 5px;">
          📋 课题需求详情
        </h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="课题名称" :span="2">{{ viewData.subjectName }}</el-descriptions-item>
          <el-descriptions-item label="课题来源">{{ viewData.subjectSource }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ viewData.leader }}</el-descriptions-item>
          <el-descriptions-item label="研究方式">{{ viewData.researchMethod }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusType(viewData.status)">
              {{ getStatusText(viewData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="研究期限">{{ viewData.researchPeriod }}</el-descriptions-item>
          <el-descriptions-item label="主要内容" :span="2">
            <div style="white-space: pre-wrap;">{{ viewData.mainContent }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="预期成果" :span="2">
            <div style="white-space: pre-wrap;">{{ viewData.expectedResults }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 预立项审批流程详情（仅在审批中或审批完成时显示） -->
      <div v-if="viewData.status === '1' || viewData.status === '2'" style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 15px; color: #67C23A; border-bottom: 2px solid #67C23A; padding-bottom: 5px;">
          🔄 预立项审批流程详情
        </h3>

        <!-- 预立项基本信息 -->
        <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
          <h4 style="margin: 0 0 10px 0; color: #606266;">预立项信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <p><strong>项目名称：</strong>{{ getPreProjectInfo(viewData.id).projectName }}</p>
            </el-col>
            <el-col :span="8">
              <p><strong>委托单位：</strong>{{ getPreProjectInfo(viewData.id).delegateUnit }}</p>
            </el-col>
            <el-col :span="8">
              <p><strong>项目编号：</strong>{{ getPreProjectInfo(viewData.id).projectNumber }}</p>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <p><strong>项目负责人：</strong>{{ getPreProjectInfo(viewData.id).projectLeader }}</p>
            </el-col>
            <el-col :span="8">
              <p><strong>立项时间：</strong>{{ getPreProjectInfo(viewData.id).projectStartTime }}</p>
            </el-col>
            <el-col :span="8">
              <p><strong>预估收入：</strong>{{ getPreProjectInfo(viewData.id).estimatedIncome }}万元</p>
            </el-col>
          </el-row>
        </div>

        <!-- 审批流程进度 -->
        <div>
          <h4 style="margin-bottom: 15px; color: #606266;">审批流程进度</h4>
          <div v-if="viewData.status === '1'">
            <!-- 审批通过的完整流程 -->
            <el-steps :active="3" direction="vertical" finish-status="success">
              <el-step title="部门负责人审批">
                <div slot="description">
                  <p>审批人：张三</p>
                  <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
                  <p style="color: #909399; font-size: 12px;">审批意见：同意该课题立项，技术方案可行。</p>
                </div>
              </el-step>
              <el-step title="技术总监审批">
                <div slot="description">
                  <p>审批人：李四</p>
                  <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-16 14:20)</p>
                  <p style="color: #909399; font-size: 12px;">审批意见：技术路线合理，预算合适，同意立项。</p>
                </div>
              </el-step>
              <el-step title="总经理审批">
                <div slot="description">
                  <p>审批人：王五</p>
                  <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-17 09:15)</p>
                  <p style="color: #909399; font-size: 12px;">审批意见：项目具有重要意义，批准立项执行。</p>
                </div>
              </el-step>
            </el-steps>
            <div style="margin-top: 20px; padding: 10px; background-color: #f0f9ff; border-left: 4px solid #67C23A;">
              <p style="margin: 0; color: #67C23A; font-weight: bold;">🎉 审批已完成</p>
            </div>
          </div>

          <div v-else-if="viewData.status === '2'">
            <!-- 审批中的流程 -->
            <el-steps :active="1" direction="vertical" process-status="process">
              <el-step title="部门负责人审批">
                <div slot="description">
                  <p>审批人：张三</p>
                  <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
                  <p style="color: #909399; font-size: 12px;">审批意见：同意该课题立项，技术方案可行。</p>
                </div>
              </el-step>
              <el-step title="技术总监审批">
                <div slot="description">
                  <p>审批人：李四</p>
                  <p style="color: #E6A23C; font-size: 12px;">⏳ 审批中...</p>
                  <p style="color: #909399; font-size: 12px;">提交时间：2024-01-16 09:00</p>
                </div>
              </el-step>
              <el-step title="总经理审批">
                <div slot="description">
                  <p>审批人：王五</p>
                  <p style="color: #909399; font-size: 12px;">⏸ 等待中</p>
                </div>
              </el-step>
            </el-steps>
            <div style="margin-top: 20px; padding: 10px; background-color: #fdf6ec; border-left: 4px solid #E6A23C;">
              <p style="margin: 0; color: #E6A23C; font-weight: bold;">⏳ 当前正在技术总监审批环节，请耐心等待...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 草稿状态提示 -->
      <div v-else-if="viewData.status === '0'" style="margin-bottom: 20px;">
        <div style="padding: 15px; background-color: #f4f4f5; border-left: 4px solid #909399; border-radius: 4px;">
          <p style="margin: 0; color: #606266;">
            📝 该课题需求当前为草稿状态，尚未提交至预立项流程。
            <br>
            如需启动预立项流程，请点击表格中的"提交预立项"按钮。
          </p>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="viewDialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>

  <!-- 提交预立项对话框 -->
  <el-dialog title="提交预立项" :visible.sync="submitDialogVisible" width="900px" @close="resetSubmitForm">
    <el-form ref="submitForm" :model="submitFormData" :rules="submitFormRules" label-width="120px">
      <!-- 第一部分：预立项审批信息 -->
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 15px; color: #409EFF;">预立项审批信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="submitFormData.projectName" placeholder="请输入项目名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位" prop="delegateUnit">
              <el-input v-model="submitFormData.delegateUnit" placeholder="请输入委托单位"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目编号" prop="projectNumber">
              <el-input v-model="submitFormData.projectNumber" placeholder="请输入项目编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人" prop="projectLeader">
              <el-select v-model="submitFormData.projectLeader" placeholder="请选择项目负责人" style="width: 100%" filterable>
                <el-option v-for="person in oaPersonnelList" :key="person.id"
                  :label="`${person.name} (${person.department} - ${person.position})`" :value="person.name">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目立项时间" prop="projectStartTime">
              <el-date-picker v-model="submitFormData.projectStartTime" type="date" placeholder="选择项目立项时间"
                style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估收入" prop="estimatedIncome">
              <el-input v-model="submitFormData.estimatedIncome" placeholder="请输入预估收入（万元）">
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="研究内容" prop="researchContent">
          <el-input v-model="submitFormData.researchContent" type="textarea" :rows="4" placeholder="请输入研究内容">
          </el-input>
        </el-form-item>
      </div>

      <!-- 第二部分：选择流程名称 -->
      <div>
        <h3 style="margin-bottom: 15px; color: #409EFF;">选择审批流程</h3>
        <el-form-item label="流程名称" prop="processName">
          <el-select v-model="submitFormData.processName" placeholder="请选择流程名称" style="width: 100%"
            @change="handleProcessChange">
            <el-option v-for="process in processOptions" :key="process.name" :label="process.name"
              :value="process.name">
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 显示选择的流程步骤 -->
        <div v-if="selectedProcess" style="margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="margin: 0; color: #606266;">审批流程及审批人员</h4>
            <el-button v-if="!isEditingProcess" size="small" type="primary" icon="el-icon-edit"
              @click="startEditProcess">
              编辑流程
            </el-button>
            <div v-else>
              <el-button size="small" @click="cancelEditProcess">取消</el-button>
              <el-button size="small" type="primary" @click="saveEditProcess">保存</el-button>
            </div>
          </div>

          <!-- 非编辑模式 - 显示流程步骤 -->
          <div v-if="!isEditingProcess">
            <el-steps :active="0" direction="vertical" style="margin-left: 20px;">
              <el-step v-for="(step, index) in selectedProcess.steps" :key="index" :title="step.name"
                :description="`审批人：${step.approver}`">
              </el-step>
            </el-steps>
          </div>

          <!-- 编辑模式 - 可编辑的流程步骤 -->
          <div v-else-if="editableProcess">
            <div style="margin-bottom: 15px;">
              <el-button size="small" type="success" icon="el-icon-plus" @click="addProcessStep">
                添加审批步骤
              </el-button>
            </div>

            <div v-for="(step, index) in editableProcess.steps" :key="index"
              style="margin-bottom: 15px; padding: 15px; border: 1px solid #DCDFE6; border-radius: 4px; background-color: #FAFAFA;">
              <div style="display: flex; align-items: center; gap: 10px;">
                <span style="min-width: 80px; font-weight: bold;">步骤 {{ index + 1 }}:</span>
                <el-input v-model="step.name" placeholder="请输入审批步骤名称" style="flex: 1;">
                </el-input>
                <el-select v-model="step.approver" placeholder="选择审批人" style="width: 200px;" filterable>
                  <el-option v-for="person in oaPersonnelList" :key="person.id"
                    :label="`${person.name} (${person.position})`" :value="person.name">
                  </el-option>
                </el-select>
                <el-button size="small" type="danger" icon="el-icon-delete" @click="removeProcessStep(index)"
                  :disabled="editableProcess.steps.length <= 1">
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="submitDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirmSubmit">确认提交</el-button>
    </div>
  </el-dialog>

  <!-- 审批进度对话框 -->
  <el-dialog title="审批进度" :visible.sync="progressDialogVisible" width="700px">
    <div v-if="currentProjectData">
      <!-- 项目基本信息 -->
      <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <h3 style="margin: 0 0 10px 0; color: #409EFF;">项目信息</h3>
        <p><strong>项目名称：</strong>{{ currentProjectData.subjectName }}</p>
        <p><strong>负责人：</strong>{{ currentProjectData.leader }}</p>
        <p><strong>当前状态：</strong>
          <el-tag :type="getStatusType(currentProjectData.status)">
            {{ getStatusText(currentProjectData.status) }}
          </el-tag>
        </p>
      </div>

      <!-- 审批流程进度 -->
      <div>
        <h3 style="margin-bottom: 15px; color: #409EFF;">审批流程进度</h3>
        <div v-if="currentProjectData.status === '1'">
          <!-- 审批通过的完整流程 -->
          <el-steps :active="3" direction="vertical" finish-status="success">
            <el-step title="部门负责人审批" description="审批人：张三">
              <div slot="description">
                <p>审批人：张三</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
              </div>
            </el-step>
            <el-step title="技术总监审批" description="审批人：李四">
              <div slot="description">
                <p>审批人：李四</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-16 14:20)</p>
              </div>
            </el-step>
            <el-step title="总经理审批" description="审批人：王五">
              <div slot="description">
                <p>审批人：王五</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-17 09:15)</p>
              </div>
            </el-step>
          </el-steps>
          <div style="margin-top: 20px; padding: 10px; background-color: #f0f9ff; border-left: 4px solid #67C23A;">
            <p style="margin: 0; color: #67C23A; font-weight: bold;">🎉 审批已完成</p>
          </div>
        </div>

        <div v-else-if="currentProjectData.status === '2'">
          <!-- 审批中的流程 -->
          <el-steps :active="1" direction="vertical" process-status="process">
            <el-step title="部门负责人审批" description="审批人：张三">
              <div slot="description">
                <p>审批人：张三</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
              </div>
            </el-step>
            <el-step title="技术总监审批" description="审批人：李四">
              <div slot="description">
                <p>审批人：李四</p>
                <p style="color: #E6A23C; font-size: 12px;">⏳ 审批中...</p>
              </div>
            </el-step>
            <el-step title="总经理审批" description="审批人：王五">
              <div slot="description">
                <p>审批人：王五</p>
                <p style="color: #909399; font-size: 12px;">⏸ 等待中</p>
              </div>
            </el-step>
          </el-steps>
          <div style="margin-top: 20px; padding: 10px; background-color: #fdf6ec; border-left: 4px solid #E6A23C;">
            <p style="margin: 0; color: #E6A23C; font-weight: bold;">⏳ 当前正在技术总监审批环节，请耐心等待...</p>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="progressDialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>

  <!-- 研究计划对话框 -->
  <el-dialog :title="isEditingResearchPlan ? '制定研究计划' : '查看研究计划'" :visible.sync="researchPlanDialogVisible"
    width="1000px" @close="isEditingResearchPlan = false">
    <div v-if="researchPlanData">
      <!-- 课题基本信息 -->
      <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <h3 style="margin: 0 0 10px 0; color: #409EFF;">课题基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <p><strong>课题名称：</strong>{{ researchPlanFormData.subjectName }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>课题来源：</strong>{{ researchPlanFormData.subjectSource }}</p>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <p><strong>负责人：</strong>{{ researchPlanFormData.leader }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>研究方式：</strong>{{ researchPlanFormData.researchMethod }}</p>
          </el-col>
        </el-row>
        <div v-if="!isEditingResearchPlan" style="margin-top: 10px;">
          <p><strong>计划状态：</strong>
            <el-tag
              :type="researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 3 ? 'success' : researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 2 ? 'warning' : 'info'">
              {{ researchPlanData && researchPlanData.id ?
                getResearchPlanStatusText(researchPlanStatus[researchPlanData.id]) : '未知状态' }}
            </el-tag>
          </p>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div
        v-if="!isEditingResearchPlan && researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 3"
        style="margin-bottom: 20px; text-align: right;">
        <el-button type="primary" @click="startEditResearchPlan">修改研究计划</el-button>
      </div>

      <!-- 研究计划表单 -->
      <el-form :model="researchPlanFormData" label-width="120px" :disabled="!isEditingResearchPlan">
        <h3 style="margin-bottom: 15px; color: #409EFF;">研究计划详情</h3>

        <el-form-item label="研究目标" required>
          <el-input v-model="researchPlanFormData.researchObjective" type="textarea" :rows="3" placeholder="请输入研究目标">
          </el-input>
        </el-form-item>

        <el-form-item label="研究内容" required>
          <el-input v-model="researchPlanFormData.researchContent" type="textarea" :rows="4" placeholder="请输入详细的研究内容">
          </el-input>
        </el-form-item>

        <el-form-item label="技术路线" required>
          <el-input v-model="researchPlanFormData.technicalRoute" type="textarea" :rows="3" placeholder="请输入技术路线">
          </el-input>
        </el-form-item>

        <el-form-item label="预期成果" required>
          <el-input v-model="researchPlanFormData.expectedResults" type="textarea" :rows="3" placeholder="请输入预期成果">
          </el-input>
        </el-form-item>

        <el-form-item label="风险评估">
          <el-input v-model="researchPlanFormData.riskAssessment" type="textarea" :rows="2" placeholder="请输入风险评估">
          </el-input>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预算计划" required>
              <el-input v-model="researchPlanFormData.budgetPlan" type="textarea" :rows="2" placeholder="请输入预算计划">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时间安排" required>
              <el-input v-model="researchPlanFormData.timeline" type="textarea" :rows="2" placeholder="请输入时间安排">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队组成">
              <el-input v-model="researchPlanFormData.teamComposition" type="textarea" :rows="2" placeholder="请输入团队组成">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备需求">
              <el-input v-model="researchPlanFormData.equipmentRequirement" type="textarea" :rows="2"
                placeholder="请输入设备需求">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 修改原因（仅修改时显示） -->
        <el-form-item
          v-if="isEditingResearchPlan && researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 3"
          label="调整原因" required>
          <el-input v-model="researchPlanFormData.modifyReason" type="textarea" :rows="2" placeholder="请输入调整原因">
          </el-input>
        </el-form-item>

        <!-- 审批流程选择（仅编辑时显示） -->
        <div v-if="isEditingResearchPlan">
          <h3 style="margin: 20px 0 15px 0; color: #409EFF;">选择审批流程</h3>
          <el-form-item label="流程名称" required>
            <el-select v-model="researchPlanFormData.processName" placeholder="请选择审批流程" style="width: 100%">
              <el-option v-for="process in processOptions" :key="process.name" :label="process.name"
                :value="process.name">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="researchPlanDialogVisible = false">
        {{ isEditingResearchPlan ? '取消' : '关闭' }}
      </el-button>
      <el-button v-if="isEditingResearchPlan" type="primary" @click="submitResearchPlan">
        提交审批
      </el-button>
      <el-button
        v-if="!isEditingResearchPlan && researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 3"
        @click="cancelEditResearchPlan">
        取消修改
      </el-button>
    </div>
  </el-dialog>

  <!-- 立项审批对话框 -->
  <el-dialog title="立项审批" :visible.sync="projectApprovalDialogVisible" width="1200px" @close="resetProjectApprovalForm">
    <el-form ref="projectApprovalForm" :model="projectApprovalFormData" :rules="projectApprovalRules"
      label-width="120px">
      <!-- 第一部分：项目基础信息（不可编辑，从接口获取） -->
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 15px; color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 5px;">
          📋 项目基础信息（系统自动获取）
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称">
              <el-input v-model="projectApprovalFormData.projectName" :disabled="true" placeholder="从接口获取"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位">
              <el-input v-model="projectApprovalFormData.delegateUnit" :disabled="true" placeholder="从接口获取"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目编号">
              <el-input v-model="projectApprovalFormData.projectNumber" :disabled="true" placeholder="从接口获取"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人">
              <el-input v-model="projectApprovalFormData.projectLeader" :disabled="true" placeholder="从接口获取"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="研究内容">
          <el-input v-model="projectApprovalFormData.researchContent" type="textarea" :rows="3" :disabled="true"
            placeholder="从接口获取"></el-input>
        </el-form-item>
        <el-form-item label="预期结果">
          <el-input v-model="projectApprovalFormData.expectedResults" type="textarea" :rows="3" :disabled="true"
            placeholder="从接口获取"></el-input>
        </el-form-item>
      </div>

      <!-- 第二部分：项目执行信息（可编辑，仅草稿状态可编辑） -->
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 15px; color: #67C23A; border-bottom: 2px solid #67C23A; padding-bottom: 5px;">
          ✏️ 项目执行信息（{{ isDraftStatus ? '可编辑' : '只读' }}）
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="委托时间" prop="projectStartTime">
              <el-date-picker v-model="projectApprovalFormData.projectStartTime" type="date" placeholder="选择项目立项时间"
                style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :disabled="!isDraftStatus">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="受托人" prop="trustee">
              <el-select v-model="projectApprovalFormData.trustee" placeholder="请选择受托人" style="width: 100%"
                :disabled="!isDraftStatus">
                <el-option v-for="person in trusteeOptions" :key="person.value" :label="person.label"
                  :value="person.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队成员" prop="teamMembers">
              <el-select v-model="projectApprovalFormData.teamMembers" multiple placeholder="请选择团队成员"
                style="width: 100%" filterable :disabled="!isDraftStatus">
                <el-option v-for="person in oaPersonnelList" :key="person.id"
                  :label="`${person.name} (${person.department} - ${person.position})`" :value="person.name">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经费来源" prop="fundingSource">
              <el-input v-model="projectApprovalFormData.fundingSource" placeholder="请输入经费来源"
                :disabled="!isDraftStatus"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经费预算" prop="budgetAmount">
              <el-input v-model="projectApprovalFormData.budgetAmount" placeholder="请输入经费预算"
                :disabled="!isDraftStatus"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 预留位置，可以添加其他字段 -->
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="时间节点">
              <div class="time-nodes-container">
                <div class="time-nodes-header">
                  <span class="time-nodes-title">项目时间节点</span>
                  <div class="time-nodes-actions" v-if="isDraftStatus">
                    <el-dropdown @command="handleTimeNodeTemplate" size="small">
                      <el-button size="small" type="text">
                        模板 <i class="el-icon-arrow-down el-icon--right"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="research">研究项目模板</el-dropdown-item>
                        <el-dropdown-item command="development">开发项目模板</el-dropdown-item>
                        <el-dropdown-item command="custom">自定义模板</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                    <el-button size="small" type="primary" icon="el-icon-plus" @click="addTimeNode">
                      添加节点
                    </el-button>
                  </div>
                </div>

                <div v-if="projectApprovalFormData.timeNodes.length === 0" class="empty-time-nodes">
                  <i class="el-icon-time"></i>
                  <p>暂无时间节点{{ isDraftStatus ? '，点击上方按钮添加' : '' }}</p>
                </div>

                <div v-else class="time-nodes-list">
                  <div v-for="(node, index) in projectApprovalFormData.timeNodes" :key="index" class="time-node-item"
                    :class="{ 'completed': node.status === 'completed', 'current': node.status === 'current' }">

                    <div class="time-node-content">
                      <div class="time-node-header">
                        <div class="time-node-index">{{ index + 1 }}</div>
                        <div class="time-node-info">
                          <el-input v-model="node.name" placeholder="请输入节点名称" size="small" class="node-name-input"
                            :disabled="!isDraftStatus">
                          </el-input>
                          <div class="time-node-meta">
                            <el-date-picker v-model="node.date" type="date" placeholder="选择时间" size="small"
                              format="yyyy-MM-dd" value-format="yyyy-MM-dd" class="node-date-picker"
                              :disabled="!isDraftStatus">
                            </el-date-picker>
                            <el-select v-model="node.status" placeholder="状态" size="small" class="node-status-select"
                              :disabled="!isDraftStatus">
                              <el-option label="未开始" value="pending"></el-option>
                              <el-option label="进行中" value="current"></el-option>
                              <el-option label="已完成" value="completed"></el-option>
                              <el-option label="已延期" value="delayed"></el-option>
                            </el-select>
                          </div>
                        </div>
                        <div class="time-node-actions" v-if="isDraftStatus">
                          <el-button size="mini" type="danger" icon="el-icon-delete" @click="removeTimeNode(index)"
                            circle>
                          </el-button>
                        </div>
                      </div>

                      <div class="time-node-description">
                        <el-input v-model="node.description" type="textarea" :rows="2" placeholder="请输入节点描述（可选）"
                          size="small" :disabled="!isDraftStatus">
                        </el-input>
                      </div>
                    </div>

                    <!-- 连接线 -->
                    <div v-if="index < projectApprovalFormData.timeNodes.length - 1" class="time-node-connector"></div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="研究方式" prop="riskLevel">
              <el-select v-model="projectApprovalFormData.riskLevel" placeholder="请选择研究方式" style="width: 100%"
                :disabled="!isDraftStatus">
                <el-option label="自主" value="自主"></el-option>
                <el-option label="联合" value="联合"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="projectApprovalFormData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息（可选）"
            :disabled="!isDraftStatus"></el-input>
        </el-form-item>
      </div>

      <!-- 第三部分：经费预算信息（可编辑，仅草稿状态可编辑） -->
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 15px; color: #E6A23C; border-bottom: 2px solid #E6A23C; padding-bottom: 5px;">
          💰 经费预算信息（{{ isDraftStatus ? '可编辑' : '只读' }}）
        </h3>

        <!-- 项目基本信息区域 -->
        <div style="margin-bottom: 20px; padding: 15px; background-color: #fafafa; border-radius: 4px;">
          <h4 style="margin-bottom: 15px; color: #606266;">项目基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="项目收入" prop="projectIncome">
                <el-input v-model="projectApprovalFormData.projectIncome" placeholder="请输入项目收入"
                  :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="经费预算总额" prop="economicBudgetTotal">
                <el-input v-model="projectApprovalFormData.economicBudgetTotal" placeholder="请输入经费预算总额"
                  :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目负责人" prop="projectManager">
                <el-input v-model="projectApprovalFormData.projectManager" placeholder="请输入项目负责人"
                  :disabled="!isDraftStatus"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 费用明细区域 -->
        <div style="padding: 15px; background-color: #fafafa; border-radius: 4px;">
          <h4 style="margin-bottom: 15px; color: #606266;">费用明细</h4>

          <!-- 第一行费用 -->
          <el-row :gutter="20" style="margin-bottom: 15px;">
            <el-col :span="6">
              <el-form-item label="会议费/差旅费" prop="meetingTravelFee">
                <el-input v-model="projectApprovalFormData.meetingTravelFee" placeholder="请输入"
                  :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="数据采集费" prop="dataCollectionFee">
                <el-input v-model="projectApprovalFormData.dataCollectionFee" placeholder="请输入"
                  :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="资料费" prop="materialsFee">
                <el-input v-model="projectApprovalFormData.materialsFee" placeholder="请输入" :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="劳务费" prop="laborFee">
                <el-input v-model="projectApprovalFormData.laborFee" placeholder="请输入" :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行费用 -->
          <el-row :gutter="20" style="margin-bottom: 15px;">
            <el-col :span="6">
              <el-form-item label="专家费" prop="expertFee">
                <el-input v-model="projectApprovalFormData.expertFee" placeholder="请输入" :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="中介机构费" prop="intermediaryFee">
                <el-input v-model="projectApprovalFormData.intermediaryFee" placeholder="请输入"
                  :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="印刷出版费" prop="printingFee">
                <el-input v-model="projectApprovalFormData.printingFee" placeholder="请输入" :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="税费" prop="taxFee">
                <el-input v-model="projectApprovalFormData.taxFee" placeholder="请输入" :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行：其他费用和合计 -->
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="其他费用" prop="otherFee">
                <el-input v-model="projectApprovalFormData.otherFee" placeholder="请输入其他费用" :disabled="!isDraftStatus">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6"></el-col>
            <el-col :span="6"></el-col>
            <el-col :span="6">
              <el-form-item label="费用合计">
                <el-input :value="calculateProjectApprovalTotalFee()" :disabled="true" style="font-weight: bold;">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 第四部分：选择流程名称 -->
      <div>
        <h3 style="margin-bottom: 15px; color: #409EFF;">选择审批流程</h3>
        <el-form-item label="流程名称" prop="processName">
          <el-select v-model="projectApprovalFormData.processName" placeholder="请选择流程名称" style="width: 100%"
            @change="handleProjectProcessChange">
            <el-option v-for="process in processOptions" :key="process.name" :label="process.name"
              :value="process.name">
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 显示选择的流程步骤 -->
        <div v-if="selectedProcess" style="margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="margin: 0; color: #606266;">审批流程及审批人员</h4>
            <el-button v-if="!isEditingProcess" size="small" type="primary" icon="el-icon-edit"
              @click="startEditProjectProcess">
              编辑流程
            </el-button>
            <div v-else>
              <el-button size="small" @click="cancelEditProjectProcess">取消</el-button>
              <el-button size="small" type="primary" @click="saveEditProjectProcess">保存</el-button>
            </div>
          </div>

          <!-- 非编辑模式 - 显示流程步骤 -->
          <div v-if="!isEditingProcess">
            <el-steps :active="0" direction="vertical" style="margin-left: 20px;">
              <el-step v-for="(step, index) in selectedProcess.steps" :key="index" :title="step.name"
                :description="`审批人：${step.approver}`">
              </el-step>
            </el-steps>
          </div>

          <!-- 编辑模式 - 可编辑的流程步骤 -->
          <div v-else-if="editableProcess">
            <div style="margin-bottom: 15px;">
              <el-button size="small" type="success" icon="el-icon-plus" @click="addProjectProcessStep">
                添加审批步骤
              </el-button>
            </div>

            <div v-for="(step, index) in editableProcess.steps" :key="index"
              style="margin-bottom: 15px; padding: 15px; border: 1px solid #DCDFE6; border-radius: 4px; background-color: #FAFAFA;">
              <div style="display: flex; align-items: center; gap: 10px;">
                <span style="min-width: 80px; font-weight: bold;">步骤 {{ index + 1 }}:</span>
                <el-input v-model="step.name" placeholder="请输入审批步骤名称" style="flex: 1;">
                </el-input>
                <el-select v-model="step.approver" placeholder="选择审批人" style="width: 200px;" filterable>
                  <el-option v-for="person in oaPersonnelList" :key="person.id"
                    :label="`${person.name} (${person.position})`" :value="person.name">
                  </el-option>
                </el-select>
                <el-button size="small" type="danger" icon="el-icon-delete" @click="removeProjectProcessStep(index)"
                  :disabled="editableProcess.steps.length <= 1">
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="projectApprovalDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirmProjectApproval">确认提交</el-button>
    </div>
  </el-dialog>

  <!-- 审批进度对话框 -->
  <el-dialog title="审批进度" :visible.sync="progressDialogVisible" width="700px">
    <div v-if="currentProjectData">
      <!-- 项目基本信息 -->
      <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <h3 style="margin: 0 0 10px 0; color: #409EFF;">项目信息</h3>
        <p><strong>项目名称：</strong>{{ currentProjectData.projectName }}</p>
        <p><strong>委托单位：</strong>{{ currentProjectData.delegateUnit }}</p>
        <p><strong>项目编码：</strong>{{ currentProjectData.projectNumber }}</p>
        <p><strong>负责人：</strong>{{ currentProjectData.projectLeader }}</p>
        <p><strong>当前状态：</strong>
          <el-tag :type="getStatusType(currentProjectData.status)">
            {{ getStatusText(currentProjectData.status) }}
          </el-tag>
        </p>
      </div>

      <!-- 审批流程进度 -->
      <div>
        <h3 style="margin-bottom: 15px; color: #409EFF;">审批流程进度</h3>
        <div v-if="currentProjectData.status === '1'">
          <!-- 审批通过的完整流程 -->
          <el-steps :active="3" direction="vertical" finish-status="success">
            <el-step title="部门负责人审批" description="审批人：张三">
              <div slot="description">
                <p>审批人：张三</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
                <p style="color: #909399; font-size: 12px;">审批意见：同意该项目立项，技术方案可行。</p>
              </div>
            </el-step>
            <el-step title="技术总监审批" description="审批人：李四">
              <div slot="description">
                <p>审批人：李四</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-16 14:20)</p>
                <p style="color: #909399; font-size: 12px;">审批意见：技术路线合理，预算合适，同意立项。</p>
              </div>
            </el-step>
            <el-step title="总经理审批" description="审批人：王五">
              <div slot="description">
                <p>审批人：王五</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-17 09:15)</p>
                <p style="color: #909399; font-size: 12px;">审批意见：项目具有重要意义，批准立项执行。</p>
              </div>
            </el-step>
          </el-steps>
          <div style="margin-top: 20px; padding: 10px; background-color: #f0f9ff; border-left: 4px solid #67C23A;">
            <p style="margin: 0; color: #67C23A; font-weight: bold;">🎉 审批已完成</p>
          </div>
        </div>

        <div v-else-if="currentProjectData.status === '2'">
          <!-- 审批中的流程 -->
          <el-steps :active="1" direction="vertical" process-status="process">
            <el-step title="部门负责人审批" description="审批人：张三">
              <div slot="description">
                <p>审批人：张三</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
                <p style="color: #909399; font-size: 12px;">审批意见：同意该项目立项，技术方案可行。</p>
              </div>
            </el-step>
            <el-step title="技术总监审批" description="审批人：李四">
              <div slot="description">
                <p>审批人：李四</p>
                <p style="color: #E6A23C; font-size: 12px;">⏳ 审批中...</p>
                <p style="color: #909399; font-size: 12px;">提交时间：2024-01-16 09:00</p>
              </div>
            </el-step>
            <el-step title="总经理审批" description="审批人：王五">
              <div slot="description">
                <p>审批人：王五</p>
                <p style="color: #909399; font-size: 12px;">⏸ 等待中</p>
              </div>
            </el-step>
          </el-steps>
          <div style="margin-top: 20px; padding: 10px; background-color: #fdf6ec; border-left: 4px solid #E6A23C;">
            <p style="margin: 0; color: #E6A23C; font-weight: bold;">⏳ 当前正在技术总监审批环节，请耐心等待...</p>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="progressDialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>


</div>
</template>

<script>
import { getApprovedData, getProjectBaseInfoByName, getProjectData } from '../api/api'
export default {
  name: 'process',
  components: {},
  data() {
    return {
      // 预立项课题选项
      options: [],
      value: '',
      // 立项审批相关
      projectApprovalDialogVisible: false,

      projectApprovalData: null, // 当前审批的项目数据
      projectApprovalFormData: {
        // 项目基本信息（只读，从接口获取）
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        researchContent: '',
        expectedResults: '',

        // 项目执行信息（可编辑，仅草稿状态可编辑）
        projectStartTime: '',
        trustee: '',
        teamMembers: [],
        fundingSource: '',
        budgetAmount: '',
        timeNodes: [],
        riskLevel: '',
        remarks: '',

        // 经费预算信息（可编辑，仅草稿状态可编辑）
        projectIncome: '',
        economicBudgetTotal: '',
        projectManager: '',
        meetingTravelFee: '',
        dataCollectionFee: '',
        materialsFee: '',
        laborFee: '',
        expertFee: '',
        intermediaryFee: '',
        printingFee: '',
        taxFee: '',
        otherFee: '',

        // 审批相关
        processName: ''
      },
      // 立项审批表单验证规则
      projectApprovalRules: {
        // 项目执行信息验证（仅草稿状态需要验证）
        projectStartTime: [
          { required: true, message: '请选择委托时间', trigger: 'change' }
        ],
        trustee: [
          { required: true, message: '请选择受托人', trigger: 'change' }
        ],
        teamMembers: [
          { required: true, message: '请选择团队成员', trigger: 'change' }
        ],
        fundingSource: [
          { required: true, message: '请输入经费来源', trigger: 'blur' }
        ],
        budgetAmount: [
          { required: true, message: '请输入经费预算', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        riskLevel: [
          { required: true, message: '请选择研究方式', trigger: 'change' }
        ],
        // 经费预算信息验证
        projectIncome: [
          { required: true, message: '请输入项目收入', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        economicBudgetTotal: [
          { required: true, message: '请输入经费预算总额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        projectManager: [
          { required: true, message: '请输入项目负责人', trigger: 'blur' }
        ],
        processName: [
          { required: true, message: '请选择流程名称', trigger: 'change' }
        ]
      },

      // 静态数据
      tableData: [],
      selectedRows: [],
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      // 对话框相关
      dialogVisible: false,
      viewDialogVisible: false,
      dialogTitle: '新增课题',
      isEdit: false,
      editId: null,
      viewData: null,
      // 表单数据
      formData: {
        // 不可编辑字段（从接口获取）
        projectName: '',        // 项目名称
        delegateUnit: '',       // 委托单位
        projectNumber: '',      // 项目编号
        projectLeader: '',      // 项目负责人
        expectedContent: '',    // 预期内容
        expectedResults: '',    // 预期结果

        // 基本状态字段
        status: '0'             // 默认为草稿状态
      },
      // 表单验证规则（新增时只需要基础验证）
      formRules: {
        // 基础字段验证（主要是选择预立项课题时的验证）
      },
      queryForm: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        contentAndResults: '',
        approvalStatus: ''
      },
      // 原始数据备份，用于查询重置
      originalTableData: [],
      // 提交预立项相关
      submitDialogVisible: false,
      progressDialogVisible: false, // 审批进度对话框
      currentProjectData: null, // 当前操作的项目数据

      loading: false, // 加载状态

      // 经费预算验证规则
      budgetRules: {
        projectIncome: [
          { required: true, message: '请输入项目收入', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        economicBudgetTotal: [
          { required: true, message: '请输入经费预算总额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        projectManager: [
          { required: true, message: '请输入项目负责人', trigger: 'blur' }
        ]
      },
      submitFormData: {
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        projectStartTime: '',
        estimatedIncome: '',
        teamMembers: [],
        actualStartTime: '',
        fundingSource: '',
        plannedEndTime: '',
        budgetAmount: '',
        projectStatus: '',
        riskLevel: '',
        teamSize: '',
        remarks: '',
        researchContent: '',
        processName: ''
      },
      // 流程选项
      processOptions: [
        {
          name: '标准审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '技术总监审批', approver: '李四' },
            { name: '总经理审批', approver: '王五' }
          ]
        },
        {
          name: '快速审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '总经理审批', approver: '王五' }
          ]
        },
        {
          name: '重大项目审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '技术总监审批', approver: '李四' },
            { name: '财务总监审批', approver: '赵六' },
            { name: '总经理审批', approver: '王五' },
            { name: '董事长审批', approver: '孙七' }
          ]
        }
      ],

      // 受托人选项
      trusteeOptions: [
        { value: 'zhang_san', label: '张三 - 高级工程师' },
        { value: 'li_si', label: '李四 - 项目经理' },
        { value: 'wang_wu', label: '王五 - 技术总监' },
        { value: 'zhao_liu', label: '赵六 - 资深顾问' },
        { value: 'sun_qi', label: '孙七 - 首席专家' },
        { value: 'zhou_ba', label: '周八 - 业务主管' }
      ],

      // OA人员名单（模拟数据）
      oaPersonnelList: [
        { id: 1, name: '张三', department: '技术部', position: '部门负责人' },
        { id: 2, name: '李四', department: '技术部', position: '技术总监' },
        { id: 3, name: '王五', department: '管理层', position: '总经理' },
        { id: 4, name: '赵六', department: '财务部', position: '财务总监' },
        { id: 5, name: '孙七', department: '管理层', position: '董事长' },
        { id: 6, name: '周八', department: '技术部', position: '高级工程师' },
        { id: 7, name: '吴九', department: '技术部', position: '项目经理' },
        { id: 8, name: '郑十', department: '研发部', position: '研发经理' }
      ],
      // 当前选择的流程
      selectedProcess: null,
      // 编辑模式相关
      isEditingProcess: false,
      editableProcess: null,
      // 研究计划相关
      researchPlanDialogVisible: false,
      researchPlanData: null, // 当前查看/编辑的研究计划
      isEditingResearchPlan: false,
      researchPlanFormData: {
        // 课题基本信息（只读）
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        // 研究计划字段
        researchObjective: '', // 研究目标
        researchContent: '', // 研究内容
        technicalRoute: '', // 技术路线
        expectedResults: '', // 预期成果
        riskAssessment: '', // 风险评估
        budgetPlan: '', // 预算计划
        timeline: '', // 时间安排
        teamComposition: '', // 团队组成
        equipmentRequirement: '', // 设备需求
        // 审批相关
        processName: '',
        // 修改相关
        modifyReason: '' // 调整原因（仅修改时需要）
      },
      // 研究计划状态：0-未填写，1-已填写未提交，2-审批中，3-审批通过
      researchPlanStatus: {},
      // 表单验证规则
      submitFormRules: {
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        delegateUnit: [
          { required: true, message: '请输入委托单位', trigger: 'blur' }
        ],
        projectNumber: [
          { required: true, message: '请输入项目编号', trigger: 'blur' }
        ],
        projectLeader: [
          { required: true, message: '请选择项目负责人', trigger: 'change' }
        ],
        projectStartTime: [
          { required: true, message: '请选择项目立项时间', trigger: 'change' }
        ],
        estimatedIncome: [
          { required: true, message: '请输入预估收入', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        teamMembers: [
          { required: true, type: 'array', min: 1, message: '请至少选择一个团队成员', trigger: 'change' }
        ],
        actualStartTime: [
          { required: true, message: '请选择实际开始时间', trigger: 'change' }
        ],
        fundingSource: [
          { required: true, message: '请输入经费来源', trigger: 'blur' }
        ],
        plannedEndTime: [
          { required: true, message: '请选择计划结束时间', trigger: 'change' }
        ],
        budgetAmount: [
          { required: true, message: '请输入经费预算', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        projectStatus: [
          { required: true, message: '请选择项目状态', trigger: 'change' }
        ],
        riskLevel: [
          { required: true, message: '请选择项目类型', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              const validOptions = ['自主', '联合']
              if (value && !validOptions.includes(value)) {
                callback(new Error('请选择有效的项目类型'))
              } else {
                callback()
              }
            }, trigger: 'change'
          }
        ],
        teamSize: [
          { required: true, message: '请输入团队规模', trigger: 'blur' },
          { pattern: /^\d+$/, message: '请输入正确的人数', trigger: 'blur' }
        ],
        researchContent: [
          { required: true, message: '请输入研究内容', trigger: 'blur' }
        ],
        processName: [
          { required: true, message: '请选择流程名称', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    // 当前页数据
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.tableData.slice(start, end)
    },

    // 判断是否为草稿状态（可编辑）
    isDraftStatus() {
      return this.projectApprovalData && this.projectApprovalData.status === '0'
    }
  },
  created() {
    this.getTableData()
    this.getSubjectOptions({})
    // 备份原始数据
    this.originalTableData = JSON.parse(JSON.stringify(this.tableData))
    this.totalCount = this.tableData.length

    // 初始化研究计划状态（模拟数据）
    this.researchPlanStatus = {
      1: 3, // 审批通过
      2: 0, // 未填写
      3: 2, // 审批中
      4: 1, // 已填写未提交
      5: 0  // 未填写
    }
  },
  methods: {
    // 获取预立项课题选项
    async getSubjectOptions(params = {}) {
      try {
        const response = await getApprovedData(params)

        if (response.code === 200) {
          // 构建选项数据，包含更多信息用于回显
          this.options = response.data.map(item => ({
            value: item.subjectName,
            label: item.subjectName,
            // 保存完整的数据用于回显
            data: item
          }))

        } else {
          this.$message.error(response.message || '获取课题选项失败')
        }
      } catch (error) {
        this.$message.error('获取课题选项失败')
      }
    },
    async changeSelect(val) {

      if (!val) {
        // 如果没有选中值，清空表单相关字段
        this.clearSubjectFields()
        return
      }

      try {
        // 方法1：优先从已加载的选项中获取数据（更高效）
        this.loadProjectBaseInfo(val)
      } catch (error) {
        this.$message.error('获取课题信息失败')
        this.clearSubjectFields(val)
      }
    },

    // 清空课题相关字段
    clearSubjectFields(subjectName = '') {
      this.formData = {
        ...this.formData,
        subjectName: subjectName,
        subjectSource: '',
        leader: '',
        researchMethod: '',
        mainContent: '',
        expectedResults: '',
        startTime: '',
        endTime: '',
        researchPeriod: '',
        status: ''
      }
    },

    // 立项审批
    handleProjectApproval(row) {
      this.currentProjectData = row

      if (row.status === '1') {
        // 审批通过状态 - 显示审批进度
        this.progressDialogVisible = true
      } else if (row.status === '2') {
        // 审批中状态 - 显示审批进度
        this.progressDialogVisible = true
      } else {
        // 草稿状态 - 显示立项审批表单
        this.projectApprovalFormData = {
          // 项目基本信息（只读，从接口获取）
          subjectName: row.projectName,
          subjectSource: row.subjectSource,
          leader: row.leader,
          researchMethod: row.researchMethod,
          projectName: row.projectName,
          delegateUnit: row.delegateUnit || '',
          projectNumber: row.projectNumber || '',
          projectLeader: row.leader || '',
          researchContent: row.mainContent || '',
          expectedResults: row.expectedResults || '',

          // 项目执行信息（可编辑，仅草稿状态可编辑）
          projectStartTime: row.startTime || '',
          trustee: row.trustee || '',
          teamMembers: row.teamMembers || [],
          fundingSource: row.fundingSource || '',
          budgetAmount: row.budgetAmount || '',
          timeNodes: row.timeNodes || [],
          riskLevel: row.researchMethod || '',
          remarks: row.remarks || '',

          // 经费预算信息（可编辑，仅草稿状态可编辑）
          projectIncome: row.projectIncome || '',
          economicBudgetTotal: row.economicBudgetTotal || '',
          projectManager: row.projectManager || row.leader || '',
          meetingTravelFee: row.meetingTravelFee || '',
          dataCollectionFee: row.dataCollectionFee || '',
          materialsFee: row.materialsFee || '',
          laborFee: row.laborFee || '',
          expertFee: row.expertFee || '',
          intermediaryFee: row.intermediaryFee || '',
          printingFee: row.printingFee || '',
          taxFee: row.taxFee || '',
          otherFee: row.otherFee || '',

          // 审批相关
          processName: ''
        }
        this.projectApprovalData = row
        this.selectedProcess = null
        this.projectApprovalDialogVisible = true
      }
    },

    // 立项审批流程选择变化
    handleProjectProcessChange(processName) {
      this.selectedProcess = this.processOptions.find(p => p.name === processName)
      // 创建可编辑的流程副本
      if (this.selectedProcess) {
        this.editableProcess = {
          name: this.selectedProcess.name,
          steps: this.selectedProcess.steps.map(step => ({
            name: step.name,
            approver: step.approver
          }))
        }
      }
    },

    // 开启立项审批流程编辑模式
    startEditProjectProcess() {
      this.isEditingProcess = true
    },

    // 取消立项审批流程编辑
    cancelEditProjectProcess() {
      this.isEditingProcess = false
      // 恢复原始数据
      if (this.selectedProcess) {
        this.editableProcess = {
          name: this.selectedProcess.name,
          steps: this.selectedProcess.steps.map(step => ({
            name: step.name,
            approver: step.approver
          }))
        }
      }
    },

    // 保存立项审批流程编辑
    saveEditProjectProcess() {
      if (this.editableProcess && this.editableProcess.steps.length > 0) {
        // 验证所有步骤都有审批人
        const hasEmptyApprover = this.editableProcess.steps.some(step => !step.approver || !step.approver.trim())
        if (hasEmptyApprover) {
          this.$message.warning('请为所有审批步骤指定审批人')
          return
        }

        // 更新选中的流程
        this.selectedProcess = {
          name: this.editableProcess.name,
          steps: [...this.editableProcess.steps]
        }

        this.isEditingProcess = false
        this.$message.success('审批流程已更新')
      }
    },

    // 添加立项审批步骤
    addProjectProcessStep() {
      if (this.editableProcess) {
        this.editableProcess.steps.push({
          name: '',
          approver: ''
        })
      }
    },

    // 删除立项审批步骤
    removeProjectProcessStep(index) {
      if (this.editableProcess && this.editableProcess.steps.length > 1) {
        this.editableProcess.steps.splice(index, 1)
      } else {
        this.$message.warning('至少需要保留一个审批步骤')
      }
    },

    // 确认提交立项审批
    handleConfirmProjectApproval() {
      this.$refs.projectApprovalForm.validate((valid) => {
        if (valid) {
          // 这里可以调用API提交数据
          this.$message.success('立项审批提交成功！')
          this.projectApprovalDialogVisible = false
          this.resetProjectApprovalForm()
        }
      })
    },

    // 重置立项审批表单
    resetProjectApprovalForm() {
      this.projectApprovalFormData = {
        // 项目基本信息（只读，从接口获取）
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        researchContent: '',
        expectedResults: '',

        // 项目执行信息（可编辑，仅草稿状态可编辑）
        projectStartTime: '',
        trustee: '',
        teamMembers: [],
        fundingSource: '',
        budgetAmount: '',
        timeNodes: [],
        riskLevel: '',
        remarks: '',

        // 经费预算信息（可编辑，仅草稿状态可编辑）
        projectIncome: '',
        economicBudgetTotal: '',
        projectManager: '',
        meetingTravelFee: '',
        dataCollectionFee: '',
        materialsFee: '',
        laborFee: '',
        expertFee: '',
        intermediaryFee: '',
        printingFee: '',
        taxFee: '',
        otherFee: '',

        // 审批相关
        processName: ''
      }
      this.selectedProcess = null
      this.isEditingProcess = false
      this.editableProcess = null
      if (this.$refs.projectApprovalForm) {
        this.$refs.projectApprovalForm.resetFields()
      }
    },





    // 计算立项审批经费预算总费用
    calculateProjectApprovalTotalFee() {
      const fees = [
        this.projectApprovalFormData.meetingTravelFee,
        this.projectApprovalFormData.dataCollectionFee,
        this.projectApprovalFormData.materialsFee,
        this.projectApprovalFormData.expertFee,
        this.projectApprovalFormData.intermediaryFee,
        this.projectApprovalFormData.laborFee,
        this.projectApprovalFormData.printingFee,
        this.projectApprovalFormData.taxFee,
        this.projectApprovalFormData.otherFee
      ]

      const total = fees.reduce((sum, fee) => {
        const num = parseFloat(fee) || 0
        return sum + num
      }, 0)

      return total.toFixed(2)
    },



    // 添加时间节点（支持立项审批和普通表单）
    addTimeNode() {
      const newNode = {
        name: '',
        date: '',
        status: 'pending',
        description: ''
      }

      // 判断当前是在立项审批对话框还是普通表单
      if (this.projectApprovalDialogVisible) {
        this.projectApprovalFormData.timeNodes.push(newNode)
      } else {
        this.formData.timeNodes.push(newNode)
      }
    },



    // 删除时间节点（支持立项审批和普通表单）
    removeTimeNode(index) {
      // 判断当前是在立项审批对话框还是普通表单
      const isProjectApproval = this.projectApprovalDialogVisible
      const timeNodes = isProjectApproval ? this.projectApprovalFormData.timeNodes : this.formData.timeNodes

      if (isProjectApproval) {
        // 立项审批中，如果是草稿状态且只有一个节点，给出提示
        if (timeNodes.length <= 1) {
          this.$message.warning('至少需要保留一个时间节点')
          return
        }
        timeNodes.splice(index, 1)
        this.$message.success('删除成功')
      } else {
        // 普通表单，需要确认
        this.$confirm('确定要删除这个时间节点吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          timeNodes.splice(index, 1)
          this.$message.success('删除成功')
        }).catch(() => {
          // 用户取消删除
        })
      }
    },

    // 获取时间节点状态文本
    getNodeStatusText(status) {
      const statusMap = {
        'pending': '未开始',
        'current': '进行中',
        'completed': '已完成',
        'delayed': '已延期'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取时间节点状态颜色
    getNodeStatusColor(status) {
      const colorMap = {
        'pending': '#909399',
        'current': '#409EFF',
        'completed': '#67C23A',
        'delayed': '#F56C6C'
      }
      return colorMap[status] || '#909399'
    },

    // 处理时间节点模板选择
    handleTimeNodeTemplate(command) {
      let templateNodes = []

      switch (command) {
        case 'research':
          templateNodes = [
            { name: '项目立项', date: '', status: 'pending', description: '项目正式立项，确定研究目标和范围' },
            { name: '文献调研', date: '', status: 'pending', description: '完成相关领域文献调研和技术分析' },
            { name: '方案设计', date: '', status: 'pending', description: '制定详细的研究方案和技术路线' },
            { name: '实验准备', date: '', status: 'pending', description: '准备实验设备和材料，搭建实验环境' },
            { name: '实验实施', date: '', status: 'pending', description: '按计划开展实验研究工作' },
            { name: '数据分析', date: '', status: 'pending', description: '对实验数据进行分析和处理' },
            { name: '中期检查', date: '', status: 'pending', description: '项目中期进度检查和阶段性成果评估' },
            { name: '论文撰写', date: '', status: 'pending', description: '撰写研究论文和技术报告' },
            { name: '成果验收', date: '', status: 'pending', description: '项目成果验收和结题答辩' }
          ]
          break
        case 'development':
          templateNodes = [
            { name: '需求分析', date: '', status: 'pending', description: '分析项目需求，确定功能规格' },
            { name: '系统设计', date: '', status: 'pending', description: '完成系统架构和详细设计' },
            { name: '开发环境搭建', date: '', status: 'pending', description: '搭建开发、测试和部署环境' },
            { name: '核心功能开发', date: '', status: 'pending', description: '开发系统核心功能模块' },
            { name: '功能测试', date: '', status: 'pending', description: '进行功能测试和bug修复' },
            { name: '集成测试', date: '', status: 'pending', description: '系统集成测试和性能优化' },
            { name: '用户验收测试', date: '', status: 'pending', description: '用户验收测试和反馈收集' },
            { name: '系统部署', date: '', status: 'pending', description: '系统正式部署和上线' },
            { name: '项目交付', date: '', status: 'pending', description: '项目最终交付和文档移交' }
          ]
          break
        case 'custom':
          templateNodes = [
            { name: '阶段一', date: '', status: 'pending', description: '请自定义阶段描述' },
            { name: '阶段二', date: '', status: 'pending', description: '请自定义阶段描述' },
            { name: '阶段三', date: '', status: 'pending', description: '请自定义阶段描述' },
            { name: '项目完成', date: '', status: 'pending', description: '项目最终完成' }
          ]
          break
      }

      // 判断当前是在立项审批对话框还是普通表单
      const isProjectApproval = this.projectApprovalDialogVisible
      const currentTimeNodes = isProjectApproval ? this.projectApprovalFormData.timeNodes : this.formData.timeNodes

      if (currentTimeNodes.length > 0) {
        this.$confirm('当前已有时间节点，是否要替换为模板节点？', '提示', {
          confirmButtonText: '替换',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (isProjectApproval) {
            this.projectApprovalFormData.timeNodes = templateNodes
          } else {
            this.formData.timeNodes = templateNodes
          }
          this.$message.success('时间节点模板应用成功')
        })
      } else {
        if (isProjectApproval) {
          this.projectApprovalFormData.timeNodes = templateNodes
        } else {
          this.formData.timeNodes = templateNodes
        }
        this.$message.success('时间节点模板应用成功')
      }
    },

    // 跳转到详情页面
    handleDetail(row) {
      // 跳转到详情页面，传递完整的项目数据
      this.$router.push({
        path: `/project/process/detail/${row.id}`,
        query: {
          // 传递所有项目数据
          ...row
        }
      })
    },

    // 根据状态获取标签类型
    getStatusType(status) {
      const statusMap = {
        '0': 'info',    // 草稿 - 灰色
        '1': 'success', // 审批通过 - 绿色
        '2': 'warning'  // 审批中 - 橙色
      }
      return statusMap[status] || 'info'
    },

    // 根据状态获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '草稿',
        '1': '审批通过',
        '2': '审批中'
      }
      return statusMap[status] || '未知状态'
    },

    // 表格选择变化
    handleSelectionChange(val) {
      this.selectedRows = val
    },

    // 获取表格数据
    async getTableData() {
      const response = await getProjectData()
      this.tableData = response.data
      console.log(response);

      // this.originalTableData = JSON.parse(JSON.stringify(response.data))
      // this.totalCount = response.data.length
    },
    // 查询功能
    handleQuery() {
      let filteredData = JSON.parse(JSON.stringify(this.originalTableData))

      // 根据课题名称筛选（关键字查询）
      if (this.queryForm.subjectName && this.queryForm.subjectName.trim()) {
        filteredData = filteredData.filter(item =>
          item.subjectName.toLowerCase().includes(this.queryForm.subjectName.trim().toLowerCase())
        )
      }

      // 根据课题来源筛选（关键字查询）
      if (this.queryForm.subjectSource && this.queryForm.subjectSource.trim()) {
        filteredData = filteredData.filter(item =>
          item.subjectSource.toLowerCase().includes(this.queryForm.subjectSource.trim().toLowerCase())
        )
      }

      // 根据负责人筛选（精确匹配）
      if (this.queryForm.leader && this.queryForm.leader.trim()) {
        filteredData = filteredData.filter(item =>
          item.leader === this.queryForm.leader
        )
      }

      // 根据研究方式筛选（精确匹配）
      if (this.queryForm.researchMethod && this.queryForm.researchMethod.trim()) {
        filteredData = filteredData.filter(item =>
          item.researchMethod === this.queryForm.researchMethod
        )
      }

      // 根据主要内容及预期成果筛选（关键字查询）
      if (this.queryForm.contentAndResults && this.queryForm.contentAndResults.trim()) {
        const keyword = this.queryForm.contentAndResults.trim().toLowerCase()
        filteredData = filteredData.filter(item =>
          item.mainContent.toLowerCase().includes(keyword) ||
          item.expectedResults.toLowerCase().includes(keyword)
        )
      }

      // 根据审批状态筛选（精确匹配）
      if (this.queryForm.approvalStatus && this.queryForm.approvalStatus.trim()) {
        filteredData = filteredData.filter(item =>
          item.status === this.queryForm.approvalStatus
        )
      }

      this.tableData = filteredData
      this.totalCount = filteredData.length
      this.currentPage = 1 // 重置到第一页

      this.$message.success(`查询完成，共找到 ${filteredData.length} 条记录`)
    },

    // 重置功能
    handleReset() {
      // 重置查询表单
      this.queryForm = {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        contentAndResults: '',
        approvalStatus: ''
      }

      // 恢复原始数据
      this.tableData = JSON.parse(JSON.stringify(this.originalTableData))
      this.totalCount = this.originalTableData.length
      this.currentPage = 1

      // 重置表单验证
      if (this.$refs.queryForm) {
        this.$refs.queryForm.resetFields()
      }

      this.$message.success('重置成功')
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    // 当前页变化
    handleCurrentChange(val) {
      this.currentPage = val
    },
    // 新增
    async handleAdd() {
      this.value = ''
      this.dialogTitle = '新增项目'
      this.isEdit = false
      this.editId = null
      this.resetForm()

      // 获取项目基础信息
      await this.loadProjectBaseInfo()

      this.dialogVisible = true
    },

    // 加载项目基础信息
    async loadProjectBaseInfo(val = '') {
      try {
        this.loading = true
        const response = await getProjectBaseInfoByName(val)

        if (response.code === 200) {
          // 填充不可编辑的基础信息字段
          this.formData.projectName = response.data.projectName
          this.formData.delegateUnit = response.data.delegateUnit
          this.formData.projectNumber = response.data.projectNumber
          this.formData.projectLeader = response.data.projectLeader
          this.formData.expectedContent = response.data.expectedContent
          this.formData.expectedResults = response.data.expectedResults

          this.$message.success('项目基础信息加载成功')
        } else {
          this.$message.error(response.message || '加载项目基础信息失败')
        }

        // 返回响应数据
        return response
      } catch (error) {
        console.error('加载项目基础信息失败:', error)
        this.$message.error('加载项目基础信息失败')
        // 返回错误响应
        return {
          code: 500,
          message: '加载项目基础信息失败',
          error: error.message
        }
      } finally {
        this.loading = false
      }
    },
    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑课题'
      this.isEdit = true
      this.editId = row.id
      this.formData = {
        subjectName: row.subjectName,
        subjectSource: row.subjectSource,
        leader: row.leader,
        researchMethod: row.researchMethod,
        status: row.status,
        mainContent: row.mainContent,
        expectedResults: row.expectedResults,
        startTime: row.startTime,
        endTime: row.endTime
      }
      this.dialogVisible = true
    },

    // 提交预立项
    handleSubmitPreProject(row) {
      this.currentProjectData = row

      if (row.status === '1') {
        // 审批通过状态 - 显示审批进度
        this.progressDialogVisible = true
      } else if (row.status === '2') {
        // 审批中状态 - 显示审批进度
        this.progressDialogVisible = true
      } else {
        // 草稿状态 - 显示填写表单
        this.submitFormData = {
          projectName: row.subjectName,
          delegateUnit: '',
          projectNumber: '',
          projectLeader: '',
          projectStartTime: '',
          estimatedIncome: '',
          researchContent: row.mainContent,
          processName: ''
        }
        this.selectedProcess = null
        this.submitDialogVisible = true
      }
    },

    // 流程选择变化
    handleProcessChange(processName) {
      this.selectedProcess = this.processOptions.find(p => p.name === processName)
      // 创建可编辑的流程副本
      if (this.selectedProcess) {
        this.editableProcess = {
          name: this.selectedProcess.name,
          steps: this.selectedProcess.steps.map(step => ({
            name: step.name,
            approver: step.approver
          }))
        }
      }
    },

    // 开启编辑模式
    startEditProcess() {
      this.isEditingProcess = true
    },

    // 取消编辑
    cancelEditProcess() {
      this.isEditingProcess = false
      // 恢复原始数据
      if (this.selectedProcess) {
        this.editableProcess = {
          name: this.selectedProcess.name,
          steps: this.selectedProcess.steps.map(step => ({
            name: step.name,
            approver: step.approver
          }))
        }
      }
    },

    // 保存编辑
    saveEditProcess() {
      if (this.editableProcess && this.editableProcess.steps.length > 0) {
        // 验证所有步骤都有审批人
        const hasEmptyApprover = this.editableProcess.steps.some(step => !step.approver || !step.approver.trim())
        if (hasEmptyApprover) {
          this.$message.warning('请为所有审批步骤指定审批人')
          return
        }

        // 更新选中的流程
        this.selectedProcess = {
          name: this.editableProcess.name,
          steps: [...this.editableProcess.steps]
        }

        this.isEditingProcess = false
        this.$message.success('审批流程已更新')
      }
    },

    // 添加审批步骤
    addProcessStep() {
      if (this.editableProcess) {
        this.editableProcess.steps.push({
          name: '',
          approver: ''
        })
      }
    },

    // 删除审批步骤
    removeProcessStep(index) {
      if (this.editableProcess && this.editableProcess.steps.length > 1) {
        this.editableProcess.steps.splice(index, 1)
      } else {
        this.$message.warning('至少需要保留一个审批步骤')
      }
    },

    // 处理研究计划
    handleResearchPlan(row) {
      if (!row || !row.id) {
        this.$message.error('数据异常，请刷新页面重试')
        return
      }

      const planStatus = this.researchPlanStatus[row.id] || 0

      if (planStatus === 0) {
        // 未填写 - 新建研究计划
        this.startNewResearchPlan(row)
      } else {
        // 已有研究计划 - 查看或编辑
        this.viewResearchPlan(row)
      }
    },

    // 新建研究计划
    startNewResearchPlan(row) {
      this.researchPlanFormData = {
        // 课题基本信息
        subjectName: row.subjectName,
        subjectSource: row.subjectSource,
        leader: row.leader,
        researchMethod: row.researchMethod,
        // 研究计划字段
        researchObjective: '',
        researchContent: '',
        technicalRoute: '',
        expectedResults: '',
        riskAssessment: '',
        budgetPlan: '',
        timeline: '',
        teamComposition: '',
        equipmentRequirement: '',
        processName: '',
        modifyReason: ''
      }
      this.researchPlanData = row
      this.isEditingResearchPlan = true
      this.researchPlanDialogVisible = true
    },

    // 查看研究计划
    viewResearchPlan(row) {
      // 获取已保存的研究计划数据（模拟数据）
      const savedPlan = this.getSavedResearchPlan(row.id)
      this.researchPlanFormData = savedPlan
      this.researchPlanData = row

      // 只有审批通过的计划才能编辑
      this.isEditingResearchPlan = false
      this.researchPlanDialogVisible = true
    },

    // 获取已保存的研究计划（模拟数据）
    getSavedResearchPlan(projectId) {
      const savedPlans = {
        1: {
          subjectName: '盆地低渗透油气田含油污水回用处理技术及工程应用',
          subjectSource: '省国资委',
          leader: '田野',
          researchMethod: '自主',
          researchObjective: '解决鄂尔多斯盆地低渗透油气田含油污水处理与循环利用技术难题',
          researchContent: '开展含油污水预处理、氮气气浮除油、作业废液处理等关键技术研究',
          technicalRoute: '实验室研究 → 中试试验 → 工程示范 → 产业化应用',
          expectedResults: '形成完整的含油污水处理技术体系和装备',
          riskAssessment: '技术风险：中等；市场风险：低；资金风险：低',
          budgetPlan: '总预算150万元，其中设备费60万，人员费50万，材料费40万',
          timeline: '第一阶段：理论研究（1-6月）；第二阶段：实验验证（7-12月）',
          teamComposition: '项目负责人1名，高级工程师2名，工程师3名，研究生2名',
          equipmentRequirement: '高温动态腐蚀检测仪、气浮设备、水质分析仪器等',
          processName: '标准审批流程',
          modifyReason: ''
        },
        3: {
          subjectName: '新能源汽车动力电池回收利用技术',
          subjectSource: '国家自然科学基金',
          leader: '王芳',
          researchMethod: '自主',
          researchObjective: '建立完整的动力电池回收技术体系',
          researchContent: '研究电池拆解、材料分离、有价金属回收等关键技术',
          technicalRoute: '电池拆解 → 材料分离 → 金属提取 → 材料再生',
          expectedResults: '实现锂、钴、镍等关键材料回收率达到95%以上',
          riskAssessment: '技术风险：高；环保风险：中等；经济风险：中等',
          budgetPlan: '总预算200万元，设备投入占60%，人员成本占30%',
          timeline: '研发周期24个月，分为4个阶段实施',
          teamComposition: '博士2名，硕士4名，技术员6名',
          equipmentRequirement: '拆解设备、分离设备、检测仪器等',
          processName: '重大项目审批流程',
          modifyReason: ''
        },
        4: {
          subjectName: '5G通信网络优化算法研究',
          subjectSource: '教育部',
          leader: '张伟',
          researchMethod: '联合',
          researchObjective: '提升5G网络性能和用户体验',
          researchContent: '研究网络优化算法，降低延迟，提高容量',
          technicalRoute: '算法设计 → 仿真验证 → 现网测试',
          expectedResults: '网络容量提升40%，延迟降低50%',
          riskAssessment: '技术难度大，需要与运营商密切合作',
          budgetPlan: '120万元，主要用于算法开发和测试',
          timeline: '18个月完成，分3个阶段',
          teamComposition: '算法工程师4名，测试工程师2名',
          equipmentRequirement: '高性能计算服务器、网络测试设备',
          processName: '快速审批流程',
          modifyReason: ''
        }
      }

      return savedPlans[projectId] || {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        researchObjective: '',
        researchContent: '',
        technicalRoute: '',
        expectedResults: '',
        riskAssessment: '',
        budgetPlan: '',
        timeline: '',
        teamComposition: '',
        equipmentRequirement: '',
        processName: '',
        modifyReason: ''
      }
    },

    // 开始编辑研究计划（仅审批通过的可编辑）
    startEditResearchPlan() {
      this.isEditingResearchPlan = true
    },

    // 取消编辑研究计划
    cancelEditResearchPlan() {
      this.isEditingResearchPlan = false
      // 恢复原始数据
      const savedPlan = this.getSavedResearchPlan(this.researchPlanData.id)
      this.researchPlanFormData = savedPlan
    },

    // 提交研究计划
    submitResearchPlan() {
      // 验证表单
      if (!this.validateResearchPlan()) {
        return
      }

      // 这里应该调用API提交数据
      this.$message.success('研究计划提交成功！')

      // 更新状态为审批中
      this.researchPlanStatus[this.researchPlanData.id] = 2
      this.researchPlanDialogVisible = false
    },

    // 验证研究计划表单
    validateResearchPlan() {
      const requiredFields = [
        'researchObjective', 'researchContent', 'technicalRoute',
        'expectedResults', 'budgetPlan', 'timeline', 'processName'
      ]

      for (let field of requiredFields) {
        if (!this.researchPlanFormData[field] || !this.researchPlanFormData[field].trim()) {
          this.$message.warning('请填写完整的研究计划信息')
          return false
        }
      }

      // 如果是修改模式，检查调整原因
      if (this.researchPlanStatus[this.researchPlanData.id] === 3 && this.isEditingResearchPlan) {
        if (!this.researchPlanFormData.modifyReason || !this.researchPlanFormData.modifyReason.trim()) {
          this.$message.warning('请填写调整原因')
          return false
        }
      }

      return true
    },

    // 获取研究计划状态文本
    getResearchPlanStatusText(status) {
      const statusMap = {
        0: '未填写',
        1: '已填写未提交',
        2: '审批中',
        3: '审批通过'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取预立项信息（模拟数据）
    getPreProjectInfo(projectId) {
      // 根据项目ID返回预立项信息，这里使用模拟数据
      const preProjectData = {
        1: {
          projectName: '盆地低渗透油气田含油污水回用处理技术及工程应用',
          delegateUnit: '中石油勘探开发研究院',
          projectNumber: 'PRJ-2024-001',
          projectLeader: '田野',
          projectStartTime: '2024-01-15',
          estimatedIncome: '150.5'
        },
        2: {
          projectName: '智能制造关键技术研究与应用',
          delegateUnit: '工信部智能制造研究院',
          projectNumber: 'PRJ-2024-002',
          projectLeader: '李明',
          projectStartTime: '2024-01-20',
          estimatedIncome: '200.0'
        },
        3: {
          projectName: '新能源汽车动力电池回收利用技术',
          delegateUnit: '国家新能源汽车技术创新中心',
          projectNumber: 'PRJ-2024-003',
          projectLeader: '王芳',
          projectStartTime: '2024-01-25',
          estimatedIncome: '180.8'
        },
        4: {
          projectName: '5G通信网络优化算法研究',
          delegateUnit: '中国移动研究院',
          projectNumber: 'PRJ-2024-004',
          projectLeader: '张伟',
          projectStartTime: '2024-01-30',
          estimatedIncome: '120.0'
        },
        5: {
          projectName: '生物医学材料表面改性技术',
          delegateUnit: '国家生物医学材料工程技术研究中心',
          projectNumber: 'PRJ-2024-005',
          projectLeader: '陈红',
          projectStartTime: '2024-02-01',
          estimatedIncome: '95.5'
        }
      }

      return preProjectData[projectId] || {
        projectName: '未知项目',
        delegateUnit: '未知单位',
        projectNumber: '未知编号',
        projectLeader: '未知负责人',
        projectStartTime: '未知时间',
        estimatedIncome: '0'
      }
    },

    // 获取项目审批进度信息（模拟数据）
    getProjectProgress(status) {
      // 根据状态返回不同的审批进度
      const baseSteps = [
        { name: '部门负责人审批', approver: '张三', status: 'finish', time: '2024-01-15 10:30' },
        { name: '技术总监审批', approver: '李四', status: 'finish', time: '2024-01-16 14:20' },
        { name: '总经理审批', approver: '王五', status: 'finish', time: '2024-01-17 09:15' }
      ]

      if (status === '1') {
        // 审批通过 - 所有步骤完成
        return {
          processName: '标准审批流程',
          currentStep: 3,
          steps: baseSteps,
          finalStatus: '审批通过',
          finalTime: '2024-01-17 09:15'
        }
      } else if (status === '2') {
        // 审批中 - 部分步骤完成
        return {
          processName: '标准审批流程',
          currentStep: 1,
          steps: [
            { name: '部门负责人审批', approver: '张三', status: 'finish', time: '2024-01-15 10:30' },
            { name: '技术总监审批', approver: '李四', status: 'process', time: '' },
            { name: '总经理审批', approver: '王五', status: 'wait', time: '' }
          ],
          finalStatus: '审批中',
          finalTime: ''
        }
      }

      return null
    },

    // 确认提交预立项
    handleConfirmSubmit() {
      this.$refs.submitForm.validate((valid) => {
        if (valid) {
          // 这里可以调用API提交数据
          this.$message.success('预立项提交成功！')
          this.submitDialogVisible = false
          this.resetSubmitForm()
        }
      })
    },

    // 重置提交表单
    resetSubmitForm() {
      this.submitFormData = {
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        projectStartTime: '',
        estimatedIncome: '',
        teamMembers: [],
        actualStartTime: '',
        fundingSource: '',
        plannedEndTime: '',
        budgetAmount: '',
        projectStatus: '',
        riskLevel: '',
        teamSize: '',
        remarks: '',
        researchContent: '',
        processName: ''
      }
      this.selectedProcess = null
      this.isEditingProcess = false
      this.editableProcess = null
      if (this.$refs.submitForm) {
        this.$refs.submitForm.resetFields()
      }
    },
    // 查看详情
    handleView(row) {
      this.viewData = row
      this.viewDialogVisible = true
    },
    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该课题吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从当前显示数据中删除
        const index = this.tableData.findIndex(item => item.id === row.id)
        if (index > -1) {
          this.tableData.splice(index, 1)
        }

        // 从原始数据中删除
        const originalIndex = this.originalTableData.findIndex(item => item.id === row.id)
        if (originalIndex > -1) {
          this.originalTableData.splice(originalIndex, 1)
        }

        this.totalCount = this.tableData.length
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 提交表单
    handleSubmit() {
      // 简单验证：确保选择了预立项课题
      if (!this.value) {
        this.$message.warning('请选择预立项课题')
        return
      }

      if (!this.formData.projectName) {
        this.$message.warning('请先选择预立项课题以获取项目基础信息')
        return
      }

      if (this.isEdit) {
        // 编辑逻辑保持不变
        const index = this.tableData.findIndex(item => item.id === this.editId)
        if (index > -1) {
          this.tableData[index] = {
            ...this.tableData[index],
            ...this.formData
          }
        }

        const originalIndex = this.originalTableData.findIndex(item => item.id === this.editId)
        if (originalIndex > -1) {
          this.originalTableData[originalIndex] = {
            ...this.originalTableData[originalIndex],
            ...this.formData
          }
        }

        this.$message.success('编辑成功')
      } else {
        // 新增：创建草稿状态的项目
        const newId = Math.max(...this.originalTableData.map(item => item.id)) + 1
        const newItem = {
          id: newId,
          subjectName: this.formData.projectName,
          projectName: this.formData.projectName,
          delegateUnit: this.formData.delegateUnit,
          projectNumber: this.formData.projectNumber,
          leader: this.formData.projectLeader,
          mainContent: this.formData.expectedContent,
          expectedResults: this.formData.expectedResults,
          status: '0', // 草稿状态
          researchMethod: '自主', // 默认值
          subjectSource: '系统创建', // 默认值
          startTime: '',
          endTime: '',
          researchPeriod: ''
        }

        // 同时添加到原始数据和当前显示数据
        this.originalTableData.push(newItem)
        this.tableData.push(newItem)
        this.totalCount = this.tableData.length
        this.$message.success('项目创建成功！现在可以进行立项审批了。')
      }
      this.dialogVisible = false
    },
    // 重置表单
    resetForm() {
      this.formData = {
        // 不可编辑字段（从接口获取）
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        expectedContent: '',
        expectedResults: '',

        // 基本状态字段
        status: '0'
      }
      if (this.$refs.dataForm) {
        this.$refs.dataForm.resetFields()
      }
    },

    // 获取受托人姓名
    getTrusteeName(value) {
      const trustee = this.trusteeOptions.find(option => option.value === value)
      return trustee ? trustee.label : value || '未设置'
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}

// 查询工具栏样式
.el-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;

  .el-row {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-form-item {
    margin-bottom: 10px;
  }
}

// 查询按钮组样式
.el-button+.el-button {
  margin-left: 10px;
}

// 展开/收起按钮样式
.el-button--text {
  padding: 0;
  margin-left: 15px;

  i {
    margin-left: 5px;
  }
}

/* 经费预算表单样式 */
.budget-form-container {
  padding: 20px;
  background-color: #fafafa;
}

.budget-section {
  margin-bottom: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e8f4fd;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-left: 8px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-end;
}

.form-item {
  flex: 1;
  min-width: 200px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.form-item label.required::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.total-item {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 2px solid #e9ecef;
}

.total-label {
  font-weight: bold !important;
  color: #495057 !important;
  font-size: 15px !important;
}

.total-input {
  font-weight: bold;
}

.total-input .el-input__inner {
  background-color: #e9ecef;
  font-weight: bold;
  color: #495057;
  text-align: center;
}

/* 输入框样式优化 */
.budget-form-container .el-input__inner {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.budget-form-container .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.budget-form-container .el-input-group__append {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #909399;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-row {
    flex-wrap: wrap;
  }

  .form-item {
    min-width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .form-item {
    min-width: 100%;
  }

  .budget-form-container {
    padding: 15px;
  }

  .budget-section {
    padding: 15px;
  }
}

/* 时间节点样式 */
.time-nodes-container {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  background-color: #f9fafc;
}

.time-nodes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.time-nodes-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.time-nodes-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.empty-time-nodes {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
}

.empty-time-nodes i {
  font-size: 48px;
  margin-bottom: 10px;
}

.time-nodes-list {
  position: relative;
}

.time-node-item {
  position: relative;
  margin-bottom: 30px;
  padding-left: 40px;
}

.time-node-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border-left: 4px solid #909399;
  transition: all 0.3s;
}

.time-node-item.completed .time-node-content {
  border-left-color: #67C23A;
}

.time-node-item.current .time-node-content {
  border-left-color: #409EFF;
}

.time-node-item.delayed .time-node-content {
  border-left-color: #F56C6C;
}

.time-node-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.time-node-index {
  position: absolute;
  left: 0;
  top: 15px;
  width: 30px;
  height: 30px;
  background-color: #909399;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  z-index: 2;
}

.time-node-item.completed .time-node-index {
  background-color: #67C23A;
}

.time-node-item.current .time-node-index {
  background-color: #409EFF;
}

.time-node-item.delayed .time-node-index {
  background-color: #F56C6C;
}

.time-node-info {
  flex: 1;
  margin-right: 15px;
}

.node-name-input {
  margin-bottom: 10px;
}

.time-node-meta {
  display: flex;
  gap: 10px;
}

.node-date-picker {
  width: 140px;
}

.node-status-select {
  width: 100px;
}

.time-node-actions {
  display: flex;
  align-items: center;
}

.time-node-description {
  margin-top: 10px;
}

.time-node-connector {
  position: absolute;
  left: 15px;
  top: 45px;
  bottom: -15px;
  width: 2px;
  background-color: #dcdfe6;
  z-index: 1;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .time-node-meta {
    flex-direction: column;
    gap: 5px;
  }

  .node-date-picker,
  .node-status-select {
    width: 100%;
  }
}

// 表头居中样式
:deep(.el-table th) {
  text-align: center;
}

:deep(.el-table th .cell) {
  text-align: center;
}

// 操作按钮样式 - 防止换行
.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.operation-buttons .el-button {
  margin: 0;
  padding: 5px 8px;
  font-size: 12px;
}

// 操作列样式
:deep(.operation-column .cell) {
  padding: 0 4px;
  overflow: visible;
}
</style>
