<div>

简体中文 | [English](./README.en.md)

<!-- <div align="center"><img width="200" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/logo/vab.png"/>
<h1> vue-admin-better</h1>
<p>瑞雪兆丰年，红梅报新春，愿您新的一年平安喜乐，万事顺意，所得皆所愿！</p>
</div> -->

[![stars](https://img.shields.io/github/stars/zxwk1998/vue-admin-better?style=flat-square&logo=GitHub)](https://github.com/zxwk1998/vue-admin-better)
[![star](https://gitee.com/chu1204505056/vue-admin-better/badge/star.svg?theme=gray)](https://gitee.com/chu1204505056/vue-admin-better)
[![license](https://img.shields.io/github/license/zxwk1998/vue-admin-better?style=flat-square)](https://en.wikipedia.org/wiki/MIT_License)

---

<!-- ## 🎉 全新版本

基于 vite5.x + vue3.x + arco-design2.x 全新的前端框架 vue-admin-arco， 欢迎点击查看或试用 👏🏻👏🏻👏🏻

[开源地址](https://github.com/zxwk1998/vue-admin-arco) | [演示地址](https://vuejs-core.cn/vue-admin-arco) -->

## 🎉 特性

- 💪 40+高质量单页
- 💅 RBAC 模型 + JWT 权限控制
- 🌍 10 万+ 项目实际应用
- 👏 良好的类型定义
- 🥳 开源版本支持免费商用
- 🚀 跨平台 PC、手机端、平板
- 📦️ 后端路由动态渲染

## 💪 找工作

- [🎉 找好工作就到好工作网查看就业避坑指南](https://job.vuejs-core.cn/)

## 🌐 付费版演示地址

- [🚀 Vue Admin Pro 演示地址（vue2.x + element-ui 2.x 付费版本，支持 PC、平板、手机）](https://vuejs-core.cn/admin-pro/)

- [🚀 Vue Admin Plus 演示地址（vue3.x + element-plus 2.x 付费版本，支持 PC、平板、手机）](https://vuejs-core.cn/admin-plus/)

- [🚀 Vue Shop Vite 演示地址（vue3.x + vite 5.x + element-plus 2.x 付费版本，支持 PC、平板、手机）](https://vuejs-core.cn/shop-vite/)

- [📌 Vue Admin Pro 及 Vue Admin Plus 购买地址](https://vuejs-core.cn/authorization/)

- [📌 Vue Shop Vite 购买地址](https://vuejs-core.cn/authorization/shop-vite.html)

<!-- ## 🔊 温馨提示

近期，我们发现少数不法互联网用户通过 IDC 云服务器、代理 ip、本地计算机等方式对我们的演示地址，发起了大规模攻击，包括但不限于 DDOS 攻击、SQL 注入、大文件上传、端口扫描、目录扫描等方式，我们已第一时间通过技术手段追踪到其攻击服务器部署的网站内容，并由此推断出其所入职公司或相关联的公司，望其迷途知返，切勿再做损人不利己的事情。我们不清楚其这样做的原因，也不愿意通过以牙还牙，以眼还眼的方式，将事态扩大，为保障正常用户的访问演示地址的体验，提升访问速度，目前，演示地址已采取以下防范措施：

- 1、演示地址服务器已购买 DDOS 高防包，有效防止了大多数恶意攻击。
- 2、演示地址服务器已禁止.git、.svn、.env、.php 等后缀文件访问，单个 ip 累计访问以上文件超过 20 次，将被永久封禁，并无法再次打开演示地址。
- 3、演示地址每晚 3 点进行备份恢复、重启服务器操作，预计将有 2 到 3 分钟访问超时。
- 4、演示地址已加入恶意 ip 共享计划，对于存在恶意扫描服务器端口、服务器文件的问题 ip，我们将立即封禁，并同步至云服务器厂商恶意 ip 名单。

以上措施真实有效，正常用户切勿因好奇前去尝试 DDOS 高防包的准确性，采取以上措施，实属迫不得已，地球这么大，容得下每一个前端框架，希望大家在各自的人生里各自发光，把余下的时间多用来陪陪家人孩子。（温馨提示：如果您发现自己的 ip 被误封，可联系微信客服申请解除）。 -->

## 🌐 免费版演示地址

- [🎉 Vue Admin Better （vue2.x + element-ui 免费商用，支持 PC、平板、手机）](https://vuejs-core.cn/vue-admin-better)

<!-- - [⚡️ vue3.x + element-plus（alpha 版本，免费商用，支持 PC、平板、手机）](https://vuejs-core.cn/vue-admin-better-plus/) -->

<!-- - [⚡️ Vue Admin Ant （vue3.x + ant-design-vue 免费商用，支持 PC、平板、手机）](https://vuejs-core.cn/vue-admin-better-antdv/) -->

- [⚡️ Vue Admin Arco （vue3.x + vite5.x + arco2.x 免费商用，支持 PC）](https://vuejs-core.cn/vue-admin-arco/)

## 🌐 仓库地址

- [🌐 vue2.x github 仓库地址](https://github.com/zxwk1998/vue-admin-better/)

- [🌐 vue3.x github 仓库地址](https://github.com/zxwk1998/vue-admin-arco/)

- [🌐 vue2.x 码云仓库地址](https://gitee.com/chu1204505056/vue-admin-better/)

- [🌐 vue3.x 码云仓库地址](https://gitee.com/chu1204505056/vue-admin-arco/)

## 🍻 前端讨论 QQ 群

- 请我们喝杯咖啡，打赏后联系 QQ 783963206 邀请您进入讨论群（由于用户数较多，如果您打赏后未通过好友请求，可以尝试多加几次），不管您请还是不请，您都可以享受到开源的代码，感谢您的支持和信任，群内提供
  vue-admin-better 基础版本、开发工具自动配置教程及项目开发文档。

<table>
<tr>
<td>
<img width="200px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/zfb_kf.jpg">
</td>
<td>
<img width="200px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/qq_group/vab-2.jpg">
</td>
<td>
<img width="200px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/qq_group/vab-3.jpg">
</td>
</tr>
</table>

## 📦️ 桌面应用程序

- [Admin Pro](https://gitee.com/chu1204505056/microsoft-store/raw/master/AdminPlus.zip)
- [Admin Plus](https://gitee.com/chu1204505056/microsoft-store/raw/master/AdminPlus.zip)

<!-- ## 🌱 vue3.x vue3.0-antdv 分支（ant-design-vue）[点击切换分支](https://github.com/zxwk1998/vue-admin-better/tree/vue3.0-antdv)

```bash
# 克隆项目
git clone -b vue3.0-antdv https://github.com/zxwk1998/vue-admin-better.git
# 安装依赖
npm i --registry=http://mirrors.cloud.tencent.com/npm/
# 本地开发 启动项目
npm run serve
``` -->

## 🌱 vue3.x arco-design [点击切换仓库](https://github.com/zxwk1998/vue-admin-arco)

```bash
# 克隆项目
git clonehttps://github.com/zxwk1998/vue-admin-arco.git
# 安装依赖
npm i --registry=http://mirrors.cloud.tencent.com/npm/
# 本地开发 启动项目
npm run dev
```

## 🌱vue2.x master 分支（element-ui）[点击切换分支](https://github.com/zxwk1998/vue-admin-better/tree/master)

```bash
# 克隆项目
git clone -b master https://github.com/zxwk1998/vue-admin-better.git
# 安装依赖
npm i --registry=http://mirrors.cloud.tencent.com/npm/
# 本地开发 启动项目
npm run serve
```

## 🔊 友情链接

- [OPSLI 基于 vue-admin-better 开源版的最佳实践](https://github.com/hiparker/opsli-boot)

- [uView uni-app 生态最优秀的 UI 框架](https://github.com/YanxinNet/uView/)

- [form-generator Element 表单设计代码生成器](https://github.com/JakHuang/form-generator/)

- [wangEditor 国产最强开源富文本编辑](https://github.com/wangeditor-team/wangEditor)

## 🙈 赞助

- 如果您觉得 vue admin better 帮到了您 ，如果情况允许，您可以选择赞助以下项目

<a title="vue" href="https://opencollective.com/vuejs" target="_blank">
<img width="64px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/vue.png"/>
</a>
<a title="element-plus" href="https://opencollective.com/element-plus" target="_blank">
<img width="64px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/element-plus.png"/>
</a>
<a title="ant-design-vue" href="https://opencollective.com/ant-design-vue" target="_blank">
<img width="64px" src="https://images.opencollective.com/ant-design-vue/2ec179b/logo/256.png"/>
</a>

## 👷 框架杰出贡献者

<a href="https://github.com/fwfmiao" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/29328241?s=50"/>
</a>
<a href="https://github.com/buuing" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/36689704?s=50"/>
</a>
<a href="https://github.com/hipi" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/22478003?s=50"/>
</a>
<a href="https://github.com/hdtopku" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/14859466?s=50"/>
</a>
<a href="https://github.com/shaonialife" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/16135960?s=50"/>
</a>
<a href="https://github.com/1511578084" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/24790218?s=50"/>
</a>
<a href="https://github.com/Arooba-git" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/56495631?s=50"/>
</a>

## 📌 优势及注意事项

```
对比其他开源 admin 框架有如下优势:
1. 支持前端控制路由权限 intelligence、后端控制路由权限 all 模式
2. 已知开源 vue admin 框架中首家支持 mock 自动生成自动导出功能
3. 提供 50 余项全局精细化配置
4. 支持 scss 自动排序，eslint 自动修复
5. axios 精细化封装，支持多数据源、多成功 code 数组，支持 application/json;charset=UTF-8、application/x-www-form-urlencoded;charset=UTF-8 多种传参方式
6. 支持登录RSA加密
7. 支持打包自动生成7Z压缩包
8. 支持errorlog错误拦截
9. 支持多主题、多布局切换

使用注意事项:
1. 项目默认使用lf换行符而非crlf换行符，新建文件时请注意选择文件换行符
2. 项目默认使用的最严格的eslint校验规范（plugin:vue/recommended），使用之前建议配置开发工具实现自动修复（建议使用vscode开发）
3. 项目使用的是要求最宽泛的MIT开源协议，保留MIT开源协议即可免费商用

```

## 💚 适合人群

- 正在以及想使用 element-ui/element-plus 开发，前端开发经验 1 年+。
- 熟悉 Vue.js 技术栈，使用它开发过几个实际项目。
- 对原理技术感兴趣，想进阶和提升的同学。

## 🎨 Star

[![Stargazers for vue-admin-better](https://reporoster.com/stars/zxwk1998/vue-admin-better)](https://github.com/zxwk1998/vue-admin-better/stargazers)

## ✨ Fork

[![Forkers repo roster for vue-admin-better](https://reporoster.com/forks/zxwk1998/vue-admin-better)](https://github.com/zxwk1998/vue-admin-better/network/members)

## 🎉 功能地图

![img](https://gcore.jsdelivr.net/gh/zxwk1998/image/vip/flow.drawio.png)

## 🗃️ 效果图

以下是截取的是 pro 版的效果图展示：

<table>
<tr>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/2.png">
</td>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/6.png">
</td>
</tr>
<tr>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/8.png">
</td>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/9.png">
</td>
</tr>
<tr>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/3.png">
</td>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/5.png">
</td>
</tr>
</table>

以下是截取的是 shop 版的效果图展示：

<table>
<tr>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/16.png">
</td>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/17.png">
</td>
</tr>
<tr>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/18.png">
</td>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/19.png">
</td>
</tr>
<tr>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/20.png">
</td>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/21.png">
</td>
</tr>
</table>

## 📄 商用注意事项

开源版本可免费用于商业用途，如果方便就留个 Star 吧

<!-- ，请遵守 MIT 协议并保留作者技术支持声明，当然如果不愿意保留可以删掉，毕竟我也拿您没办法，能帮到您也当是给自己积德了，至于[Admin](https://vuejs-core.cn/admin-plus/)、[Shop](https://vuejs-core.cn/shop-vite/) 的付费版本的相关说明如下：
本人只参与了前期小部分的开发，所以不必跟开源版做对比，同事的代码功底比我好太多，我自愧不如，关于买这件事，没有强买强卖，您愿意买就买，不愿意买就忽略。我们不高尚，写代码就是为了养家糊口，不是为了用爱发电。这几年看到那么多开源项目借鉴了我们付费版本的布局、主题配置的灵感和创意，一开始我是鄙视的，现在还好状态调整过来了，能够被借鉴，被讨论恰好说明了我们的产品有价值，为了产品卖的更好我们也必须更加用心的去维护付费版本以保持我们产品的竞争力。
当然，最后还有几句话不得不说，身处互联网由盛转衰的大变革的洪流中，能活下来就已经是千难万难了，希望所有的程序员哥哥姐姐们，早日实现自己的梦想，完成自己的心愿，也想对刚要毕业准备做一名程序员的学弟学妹们说几句，互联网行业没有你们想象的那么高大上，如果想成为一名程序员那就做好加班的准备，如果有更好的选择那就别选这个行业了。 -->

</div>
