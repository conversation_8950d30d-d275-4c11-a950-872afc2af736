/**
 * <AUTHOR> （不想保留author可删除）
 * @description router全局配置，如有必要可分文件抽离，其中asyncRoutes只有在intelligence模式下才会用到，vip文档中已提供路由的基础图标与小清新图标的配置方案，请仔细阅读
 */

import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layouts'
// import EmptyLayout from '@/layouts/EmptyLayout'
import { publicPath, routerMode } from '@/config'

Vue.use(VueRouter)
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true,
  },
  {
    path: '/register',
    component: () => import('@/views/register/index'),
    hidden: true,
  },
  {
    path: '/401',
    name: '401',
    component: () => import('@/views/401'),
    hidden: true,
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404'),
    hidden: true,
  },
]

export const asyncRoutes = [
  {
    path: '/',
    component: Layout,
    redirect: '/index',
    meta: {
      title: '课题研究管理',
      affix: true,
    },
    children: [
      {
        path: 'index',
        name: 'Index',
        component: () => import('@/views/home/<USER>/index'),
        meta: {
          title: '首页',
          affix: true,
        },
      },
      {
        path: 'yearPlan',
        name: 'YearPlan',
        component: () => import('@/views/home/<USER>/index'),
        meta: {
          title: '年度计划管理',
        },
      },
      {
        path: 'project',
        name: 'Project',
        component: () => import('@/views/home/<USER>/index'),
        redirect: '/project/process',
        alwaysShow: true,
        meta: {
          title: '课题管理'
        },
        children: [
          {
            path: 'preProjectInitiation',
            name: 'PreProjectInitiation',
            component: () => import('@/views/home/<USER>/preProjectInitiation/index'),
            meta: { title: '课题需求管理' },
          },
          {
            path: 'process',
            name: 'Process',
            component: () => import('@/views/home/<USER>/process/index'),
            meta: { title: '内部课题管理' },
          },
          {
            path: 'process/detail/:id',
            name: 'ProcessDetail',
            component: () => import('@/views/home/<USER>/process/detail'),
            meta: { title: '课题详情', hidden: true },
          },
          {
            path: 'askProcess',
            name: 'AskProcess',
            component: () => import('@/views/home/<USER>/askProcess/index'),
            meta: { title: '委托课题管理' },
          },
        ]
      },

      {
        path: 'expertBase',
        name: 'ExpertBase',
        component: () => import('@/views/home/<USER>/index'),
        redirect: '/expertBase/expert',
        alwaysShow: true,
        meta: { title: '专家库管理' },
        children: [
          {
            path: 'expert',
            name: 'Expert',
            component: () => import('@/views/home/<USER>/expert/index'),
            meta: { title: '专家管理' },
          },
          {
            path: 'exeProfile',
            name: 'exeProfile',
            component: () => import('@/views/home/<USER>/exeProfile/index'),
            meta: { title: '专家画像' },
          },


        ]
      },

    ],
  },

  // #region
  /* {
    path: "/test",
    component: Layout,
    redirect: "noRedirect",
    children: [
      {
        path: "test",
        name: "Test",
        component: () => import("@/views/test/index"),
        meta: {
          title: "test",
          icon: "marker",
          permissions: ["admin"],
        },
      },
    ],
  }, */

  // {
  //   path: '/vab',
  //   component: Layout,
  //   redirect: 'noRedirect',
  //   name: 'Vab',
  //   alwaysShow: true,
  //   meta: { title: '组件', icon: 'box-open' },
  //   children: [
  //     {
  //       path: 'permissions',
  //       name: 'Permission',
  //       component: () => import('@/views/vab/permissions/index'),
  //       meta: {
  //         title: '角色权限',
  //         permissions: ['admin', 'editor'],
  //       },
  //     },
  //     // {
  //     //   path: 'icon',
  //     //   component: EmptyLayout,
  //     //   redirect: 'noRedirect',
  //     //   name: 'Icon',
  //     //   meta: {
  //     //     title: '图标',
  //     //     permissions: ['admin'],
  //     //   },
  //     //   children: [
  //     //     {
  //     //       path: 'awesomeIcon',
  //     //       name: 'AwesomeIcon',
  //     //       component: () => import('@/views/vab/icon/index'),
  //     //       meta: { title: '常规图标' },
  //     //     },
  //     //     {
  //     //       path: 'colorfulIcon',
  //     //       name: 'ColorfulIcon',
  //     //       component: () => import('@/views/vab/icon/colorfulIcon'),
  //     //       meta: { title: '多彩图标' },
  //     //     },
  //     //   ],
  //     // },
  //     // {
  //     //   path: 'table',
  //     //   component: () => import('@/views/vab/table/index'),
  //     //   name: 'Table',
  //     //   meta: {
  //     //     title: '表格',
  //     //     permissions: ['admin'],
  //     //   },
  //     // },

  //     // {
  //     //   path: 'webSocket',
  //     //   name: 'WebSocket',
  //     //   component: () => import('@/views/vab/webSocket/index'),
  //     //   meta: { title: 'webSocket', permissions: ['admin'] },
  //     // },
  //     // {
  //     //   path: 'form',
  //     //   name: 'Form',
  //     //   component: () => import('@/views/vab/form/index'),
  //     //   meta: { title: '表单', permissions: ['admin'] },
  //     // },
  //     // {
  //     //   path: 'element',
  //     //   name: 'Element',
  //     //   component: () => import('@/views/vab/element/index'),
  //     //   meta: { title: '常用组件', permissions: ['admin'] },
  //     // },
  //     // {
  //     //   path: 'tree',
  //     //   name: 'Tree',
  //     //   component: () => import('@/views/vab/tree/index'),
  //     //   meta: { title: '树', permissions: ['admin'] },
  //     // },
  //     // {
  //     //   path: 'menu1',
  //     //   component: () => import('@/views/vab/nested/menu1/index'),
  //     //   name: 'Menu1',
  //     //   alwaysShow: true,
  //     //   meta: {
  //     //     title: '嵌套路由 1',
  //     //     permissions: ['admin'],
  //     //   },
  //     //   children: [
  //     //     {
  //     //       path: 'menu1-1',
  //     //       name: 'Menu1-1',
  //     //       alwaysShow: true,
  //     //       meta: { title: '嵌套路由 1-1' },
  //     //       component: () => import('@/views/vab/nested/menu1/menu1-1/index'),

  //     //       children: [
  //     //         {
  //     //           path: 'menu1-1-1',
  //     //           name: 'Menu1-1-1',
  //     //           meta: { title: '嵌套路由 1-1-1' },
  //     //           component: () => import('@/views/vab/nested/menu1/menu1-1/menu1-1-1/index'),
  //     //         },
  //     //       ],
  //     //     },
  //     //   ],
  //     // },
  //     // {
  //     //   path: 'loading',
  //     //   name: 'Loading',
  //     //   component: () => import('@/views/vab/loading/index'),
  //     //   meta: { title: 'loading', permissions: ['admin'] },
  //     // },
  //     // {
  //     //   path: 'backToTop',
  //     //   name: 'BackToTop',
  //     //   component: () => import('@/views/vab/backToTop/index'),
  //     //   meta: { title: '返回顶部', permissions: ['admin'] },
  //     // },
  //     // {
  //     //   path: 'lodash',
  //     //   name: 'Lodash',
  //     //   component: () => import('@/views/vab/lodash/index'),
  //     //   meta: { title: 'lodash', permissions: ['admin'] },
  //     // },

  //     // {
  //     //   path: 'upload',
  //     //   name: 'Upload',
  //     //   component: () => import('@/views/vab/upload/index'),
  //     //   meta: { title: '上传', permissions: ['admin'] },
  //     // },
  //     // {
  //     //   path: 'log',
  //     //   name: 'Log',
  //     //   component: () => import('@/views/vab/errorLog/index'),
  //     //   meta: { title: '错误日志模拟', permissions: ['admin'] },
  //     // },
  //     // {
  //     //   path: 'https://github.com/zxwk1998/vue-admin-better/',
  //     //   name: 'ExternalLink',
  //     //   meta: {
  //     //     title: '外链',
  //     //     target: '_blank',
  //     //     permissions: ['admin', 'editor'],
  //     //     badge: 'New',
  //     //   },
  //     // },
  //     // {
  //     //   path: 'more',
  //     //   name: 'More',
  //     //   component: () => import('@/views/vab/more/index'),
  //     //   meta: { title: '关于', permissions: ['admin'] },
  //     // },
  //   ],
  // },
  //#endregion
  {
    path: '/personnelManagement',
    component: Layout,
    redirect: 'noRedirect',
    name: 'PersonnelManagement',
    meta: { title: '系统管理', permissions: ['admin'] },
    children: [
      {
        path: 'userManagement',
        name: 'UserManagement',
        component: () => import('@/views/personnelManagement/userManagement/index'),
        meta: { title: '用户管理' },
      },
      {
        path: 'roleManagement',
        name: 'RoleManagement',
        component: () => import('@/views/personnelManagement/roleManagement/index'),
        meta: { title: '角色管理' },
      },
      {
        path: 'menuManagement',
        name: 'MenuManagement',
        component: () => import('@/views/personnelManagement/menuManagement/index'),
        meta: { title: '菜单管理', badge: 'New' },
      },
    ],
  },
  // {
  //   path: '/mall',
  //   component: Layout,
  //   redirect: 'noRedirect',
  //   name: 'Mall',
  //   meta: {
  //     title: '商城',
  //     icon: 'shopping-cart',
  //     permissions: ['admin'],
  //   },

  //   children: [
  //     {
  //       path: 'pay',
  //       name: 'Pay',
  //       component: () => import('@/views/mall/pay/index'),
  //       meta: {
  //         title: '支付',
  //         noKeepAlive: true,
  //       },
  //       children: null,
  //     },
  //     {
  //       path: 'goodsList',
  //       name: 'GoodsList',
  //       component: () => import('@/views/mall/goodsList/index'),
  //       meta: {
  //         title: '商品列表',
  //       },
  //     },
  //   ],
  // },
  // {
  //   path: '/error',
  //   component: EmptyLayout,
  //   redirect: 'noRedirect',
  //   name: 'Error',
  //   meta: { title: '错误页', icon: 'bug' },
  //   children: [
  //     {
  //       path: '401',
  //       name: 'Error401',
  //       component: () => import('@/views/401'),
  //       meta: { title: '401' },
  //     },
  //     {
  //       path: '404',
  //       name: 'Error404',
  //       component: () => import('@/views/404'),
  //       meta: { title: '404' },
  //     },
  //   ],
  // },
  {
    path: '*',
    redirect: '/404',
    hidden: true,
  },
]

const router = new VueRouter({
  base: publicPath,
  mode: routerMode,
  scrollBehavior: () => ({
    y: 0,
  }),
  routes: constantRoutes,
})

export function resetRouter() {
  location.reload()
}

export default router
