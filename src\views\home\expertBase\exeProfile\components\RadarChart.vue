<template>
  <div ref="chart" style="width: 100%; height: 400px;"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      const chartDom = this.$refs.chart;
      const myChart = echarts.init(chartDom);

      const option = {
        tooltip: {},
        radar: {
          indicator: this.data.labels.map((label) => ({
            name: label,
            max: 100,
          })),
        },
        series: [
          {
            name: '能力维度',
            type: 'radar',
            data: [
              {
                value: this.data.datasets[0].data,
                name: '能力维度',
              },
            ],
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.2)',
            },
            lineStyle: {
              color: 'rgba(64, 158, 255, 1)',
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>