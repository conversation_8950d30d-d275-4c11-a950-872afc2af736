<template>
<div style="background-color: white; padding: 20px">
  <!-- 查询工具栏 -->
  <el-form ref="queryForm" :model="queryForm" :rules="formRules" label-width="150px">
    <!-- 第一行查询条件 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="课题名称" prop="subjectName">
          <el-input v-model="queryForm.subjectName" clearable placeholder="请输入课题名称关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="课题来源" prop="subjectSource">
          <el-input v-model="queryForm.subjectSource" clearable placeholder="请输入课题来源关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="负责人" prop="leader">
          <el-select v-model="queryForm.leader" clearable placeholder="请选择负责人" filterable>
            <el-option v-for="person in oaPersonnelList" :key="person.id"
              :label="`${person.name} (${person.department})`" :value="person.name">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="研究方式" prop="researchMethod">
          <el-select v-model="queryForm.researchMethod" clearable placeholder="请选择研究方式">
            <el-option label="自主" value="自主"></el-option>
            <el-option label="联合" value="联合"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 第二行查询条件 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="主要内容及预期成果" prop="contentAndResults">
          <el-input v-model="queryForm.contentAndResults" clearable placeholder="请输入内容或成果关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="审批结果" prop="approvalStatus">
          <el-select v-model="queryForm.approvalStatus" clearable placeholder="请选择审批结果">
            <el-option label="审批通过" value="1"></el-option>
            <el-option label="审批中" value="2"></el-option>
            <el-option label="未提交审批" value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <!-- 操作工具栏 -->
  <div style="margin-bottom: 20px">
    <el-button type="primary" @click="handleAdd">新增</el-button>
  </div>

  <!-- 表格 -->
  <QTable
    :data="currentPageData"
    :columns="columns"
    :config="config"
    :status-config="statusConfig"
    :tag-config="tagConfig"
    @selection-change="handleSelectionChange"
  >
    <template #operation>
      <el-table-column label="操作" width="auto">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleView(scope.row)">查看</el-button>
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" @click="handleSubmitPreProject(scope.row)">
            立项审批
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </template>
  </QTable>

  <!-- 分页 -->
  <div style="margin-top: 20px;float:right">
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
      :total="totalCount">
    </el-pagination>
  </div>

  <!-- 新增/编辑对话框 -->
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" @close="resetForm">
    <el-form ref="dataForm" :model="formData" :rules="formRules" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="课题名称" prop="subjectName">
            <el-input v-model="formData.subjectName" placeholder="请输入课题名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课题来源" prop="subjectSource">
            <el-select v-model="formData.subjectSource" placeholder="请选择课题来源" style="width: 100%">
              <el-option label="省国资委" value="省国资委"></el-option>
              <el-option label="国家自然科学基金" value="国家自然科学基金"></el-option>
              <el-option label="科技部" value="科技部"></el-option>
              <el-option label="教育部" value="教育部"></el-option>
              <el-option label="企业自主" value="企业自主"></el-option>
              <el-option label="其他" value="其他"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人" prop="leader">
            <el-input v-model="formData.leader" placeholder="请输入负责人姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="研究方式" prop="researchMethod">
            <el-select v-model="formData.researchMethod" placeholder="请选择研究方式" style="width: 100%">
              <el-option label="自主" value="自主"></el-option>
              <el-option label="合作" value="合作"></el-option>
              <el-option label="委托" value="委托"></el-option>
              <el-option label="联合" value="联合"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="草稿" value="0"></el-option>
              <el-option label="审批通过" value="1"></el-option>
              <el-option label="未通过" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="主要内容" prop="mainContent">
        <el-input v-model="formData.mainContent" type="textarea" :rows="4" placeholder="请输入主要研究内容"></el-input>
      </el-form-item>
      <el-form-item label="预期成果" prop="expectedResults">
        <el-input v-model="formData.expectedResults" type="textarea" :rows="4" placeholder="请输入预期成果"></el-input>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker v-model="formData.startTime" type="date" placeholder="选择开始时间" style="width: 100%"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker v-model="formData.endTime" type="date" placeholder="选择结束时间" style="width: 100%"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>

  <!-- 查看详情对话框 -->
  <el-dialog title="课题详情" :visible.sync="viewDialogVisible" width="1000px">
    <div v-if="viewData">
      <!-- 课题需求详情 -->
      <div style="margin-bottom: 30px;">
        <h3 style="margin-bottom: 15px; color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 5px;">
          📋 课题需求详情
        </h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="课题名称1" :span="2">{{ viewData.subjectName }}</el-descriptions-item>
          <el-descriptions-item label="课题来源">{{ viewData.subjectSource }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ viewData.leader }}</el-descriptions-item>
          <el-descriptions-item label="研究方式">{{ viewData.researchMethod }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusType(viewData.status)">
              {{ getStatusText(viewData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="研究期限">{{ viewData.researchPeriod }}</el-descriptions-item>
          <el-descriptions-item label="主要内容" :span="2">
            <div style="white-space: pre-wrap;">{{ viewData.mainContent }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="预期成果" :span="2">
            <div style="white-space: pre-wrap;">{{ viewData.expectedResults }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 立项审批流程详情（仅在审批中或审批完成时显示） -->
      <div v-if="viewData.status === '1' || viewData.status === '2'" style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 15px; color: #67C23A; border-bottom: 2px solid #67C23A; padding-bottom: 5px;">
          🔄 立项审批流程详情
        </h3>

        <!-- 立项基本信息 -->
        <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
          <h4 style="margin: 0 0 10px 0; color: #606266;">立项信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <p><strong>项目名称：</strong>{{ getPreProjectInfo(viewData.id).projectName }}</p>
            </el-col>
            <el-col :span="8">
              <p><strong>委托单位：</strong>{{ getPreProjectInfo(viewData.id).delegateUnit }}</p>
            </el-col>
            <el-col :span="8">
              <p><strong>项目编号：</strong>{{ getPreProjectInfo(viewData.id).projectNumber }}</p>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <p><strong>项目负责人：</strong>{{ getPreProjectInfo(viewData.id).projectLeader }}</p>
            </el-col>
            <el-col :span="8">
              <p><strong>立项时间：</strong>{{ getPreProjectInfo(viewData.id).projectStartTime }}</p>
            </el-col>
            <el-col :span="8">
              <p><strong>预估收入：</strong>{{ getPreProjectInfo(viewData.id).estimatedIncome }}万元</p>
            </el-col>
          </el-row>
        </div>

        <!-- 审批流程进度 -->
        <div>
          <h4 style="margin-bottom: 15px; color: #606266;">审批流程进度</h4>
          <div v-if="viewData.status === '1'">
            <!-- 审批通过的完整流程 -->
            <el-steps :active="3" direction="vertical" finish-status="success">
              <el-step title="部门负责人审批">
                <div slot="description">
                  <p>审批人：张三</p>
                  <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
                  <p style="color: #909399; font-size: 12px;">审批意见：同意该课题立项，技术方案可行。</p>
                </div>
              </el-step>
              <el-step title="技术总监审批">
                <div slot="description">
                  <p>审批人：李四</p>
                  <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-16 14:20)</p>
                  <p style="color: #909399; font-size: 12px;">审批意见：技术路线合理，预算合适，同意立项。</p>
                </div>
              </el-step>
              <el-step title="总经理审批">
                <div slot="description">
                  <p>审批人：王五</p>
                  <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-17 09:15)</p>
                  <p style="color: #909399; font-size: 12px;">审批意见：项目具有重要意义，批准立项执行。</p>
                </div>
              </el-step>
            </el-steps>
            <div style="margin-top: 20px; padding: 10px; background-color: #f0f9ff; border-left: 4px solid #67C23A;">
              <p style="margin: 0; color: #67C23A; font-weight: bold;">🎉 审批已完成</p>
            </div>
          </div>

          <div v-else-if="viewData.status === '2'">
            <!-- 审批中的流程 -->
            <el-steps :active="1" direction="vertical" process-status="process">
              <el-step title="部门负责人审批">
                <div slot="description">
                  <p>审批人：张三</p>
                  <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
                  <p style="color: #909399; font-size: 12px;">审批意见：同意该课题立项，技术方案可行。</p>
                </div>
              </el-step>
              <el-step title="技术总监审批">
                <div slot="description">
                  <p>审批人：李四</p>
                  <p style="color: #E6A23C; font-size: 12px;">⏳ 审批中...</p>
                  <p style="color: #909399; font-size: 12px;">提交时间：2024-01-16 09:00</p>
                </div>
              </el-step>
              <el-step title="总经理审批">
                <div slot="description">
                  <p>审批人：王五</p>
                  <p style="color: #909399; font-size: 12px;">⏸ 等待中</p>
                </div>
              </el-step>
            </el-steps>
            <div style="margin-top: 20px; padding: 10px; background-color: #fdf6ec; border-left: 4px solid #E6A23C;">
              <p style="margin: 0; color: #E6A23C; font-weight: bold;">⏳ 当前正在技术总监审批环节，请耐心等待...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 草稿状态提示 -->
      <div v-else-if="viewData.status === '0'" style="margin-bottom: 20px;">
        <div style="padding: 15px; background-color: #f4f4f5; border-left: 4px solid #909399; border-radius: 4px;">
          <p style="margin: 0; color: #606266;">
            📝 该课题需求当前为草稿状态，尚未提交至立项流程。
            <br>
            如需启动立项流程，请点击表格中的"提交立项"按钮。
          </p>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="viewDialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>

  <!-- 提交立项对话框 -->
  <el-dialog title="提交立项" :visible.sync="submitDialogVisible" width="900px" @close="resetSubmitForm">
    <el-form ref="submitForm" :model="submitFormData" :rules="submitFormRules" label-width="120px">
      <!-- 第一部分：立项审批信息 -->
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 15px; color: #409EFF;">立项审批信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="submitFormData.projectName" placeholder="请输入项目名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位" prop="delegateUnit">
              <el-input v-model="submitFormData.delegateUnit" placeholder="请输入委托单位"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目编号" prop="projectNumber">
              <el-input v-model="submitFormData.projectNumber" placeholder="请输入项目编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人" prop="projectLeader">
              <el-select v-model="submitFormData.projectLeader" placeholder="请选择项目负责人" style="width: 100%" filterable>
                <el-option v-for="person in oaPersonnelList" :key="person.id"
                  :label="`${person.name} (${person.department} - ${person.position})`" :value="person.name">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目立项时间" prop="projectStartTime">
              <el-date-picker v-model="submitFormData.projectStartTime" type="date" placeholder="选择项目立项时间"
                style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估收入" prop="estimatedIncome">
              <el-input v-model="submitFormData.estimatedIncome" placeholder="请输入预估收入（万元）">
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="研究内容" prop="researchContent">
          <el-input v-model="submitFormData.researchContent" type="textarea" :rows="4" placeholder="请输入研究内容">
          </el-input>
        </el-form-item>
      </div>

      <!-- 第二部分：选择流程名称 -->
      <div>
        <h3 style="margin-bottom: 15px; color: #409EFF;">选择审批流程</h3>
        <el-form-item label="流程名称" prop="processName">
          <el-select v-model="submitFormData.processName" placeholder="请选择流程名称" style="width: 100%"
            @change="handleProcessChange">
            <el-option v-for="process in processOptions" :key="process.name" :label="process.name"
              :value="process.name">
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 显示选择的流程步骤 -->
        <div v-if="selectedProcess" style="margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="margin: 0; color: #606266;">审批流程及审批人员</h4>
            <el-button v-if="!isEditingProcess" size="small" type="primary" icon="el-icon-edit"
              @click="startEditProcess">
              编辑流程
            </el-button>
            <div v-else>
              <el-button size="small" @click="cancelEditProcess">取消</el-button>
              <el-button size="small" type="primary" @click="saveEditProcess">保存</el-button>
            </div>
          </div>

          <!-- 非编辑模式 - 显示流程步骤 -->
          <div v-if="!isEditingProcess">
            <el-steps :active="0" direction="vertical" style="margin-left: 20px;">
              <el-step v-for="(step, index) in selectedProcess.steps" :key="index" :title="step.name"
                :description="`审批人：${step.approver}`">
              </el-step>
            </el-steps>
          </div>

          <!-- 编辑模式 - 可编辑的流程步骤 -->
          <div v-else-if="editableProcess">
            <div style="margin-bottom: 15px;">
              <el-button size="small" type="success" icon="el-icon-plus" @click="addProcessStep">
                添加审批步骤
              </el-button>
            </div>

            <div v-for="(step, index) in editableProcess.steps" :key="index"
              style="margin-bottom: 15px; padding: 15px; border: 1px solid #DCDFE6; border-radius: 4px; background-color: #FAFAFA;">
              <div style="display: flex; align-items: center; gap: 10px;">
                <span style="min-width: 80px; font-weight: bold;">步骤 {{ index + 1 }}:</span>
                <el-input v-model="step.name" placeholder="请输入审批步骤名称" style="flex: 1;">
                </el-input>
                <el-select v-model="step.approver" placeholder="选择审批人" style="width: 200px;" filterable>
                  <el-option v-for="person in oaPersonnelList" :key="person.id"
                    :label="`${person.name} (${person.position})`" :value="person.name">
                  </el-option>
                </el-select>
                <el-button size="small" type="danger" icon="el-icon-delete" @click="removeProcessStep(index)"
                  :disabled="editableProcess.steps.length <= 1">
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="submitDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirmSubmit">确认提交</el-button>
    </div>
  </el-dialog>

  <!-- 审批进度对话框 -->
  <el-dialog title="审批进度" :visible.sync="progressDialogVisible" width="700px">
    <div v-if="currentProjectData">
      <!-- 项目基本信息 -->
      <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <h3 style="margin: 0 0 10px 0; color: #409EFF;">项目信息</h3>
        <p><strong>项目名称：</strong>{{ currentProjectData.subjectName }}</p>
        <p><strong>负责人：</strong>{{ currentProjectData.leader }}</p>
        <p><strong>当前状态：</strong>
          <el-tag :type="getStatusType(currentProjectData.status)">
            {{ getStatusText(currentProjectData.status) }}
          </el-tag>
        </p>
      </div>

      <!-- 审批流程进度 -->
      <div>
        <h3 style="margin-bottom: 15px; color: #409EFF;">审批流程进度</h3>
        <div v-if="currentProjectData.status === '1'">
          <!-- 审批通过的完整流程 -->
          <el-steps :active="3" direction="vertical" finish-status="success">
            <el-step title="部门负责人审批" description="审批人：张三">
              <div slot="description">
                <p>审批人：张三</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
              </div>
            </el-step>
            <el-step title="技术总监审批" description="审批人：李四">
              <div slot="description">
                <p>审批人：李四</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-16 14:20)</p>
              </div>
            </el-step>
            <el-step title="总经理审批" description="审批人：王五">
              <div slot="description">
                <p>审批人：王五</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-17 09:15)</p>
              </div>
            </el-step>
          </el-steps>
          <div style="margin-top: 20px; padding: 10px; background-color: #f0f9ff; border-left: 4px solid #67C23A;">
            <p style="margin: 0; color: #67C23A; font-weight: bold;">🎉 审批已完成</p>
          </div>
        </div>

        <div v-else-if="currentProjectData.status === '2'">
          <!-- 审批中的流程 -->
          <el-steps :active="1" direction="vertical" process-status="process">
            <el-step title="部门负责人审批" description="审批人：张三">
              <div slot="description">
                <p>审批人：张三</p>
                <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
              </div>
            </el-step>
            <el-step title="技术总监审批" description="审批人：李四">
              <div slot="description">
                <p>审批人：李四</p>
                <p style="color: #E6A23C; font-size: 12px;">⏳ 审批中...</p>
              </div>
            </el-step>
            <el-step title="总经理审批" description="审批人：王五">
              <div slot="description">
                <p>审批人：王五</p>
                <p style="color: #909399; font-size: 12px;">⏸ 等待中</p>
              </div>
            </el-step>
          </el-steps>
          <div style="margin-top: 20px; padding: 10px; background-color: #fdf6ec; border-left: 4px solid #E6A23C;">
            <p style="margin: 0; color: #E6A23C; font-weight: bold;">⏳ 当前正在技术总监审批环节，请耐心等待...</p>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="progressDialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>

  <!-- 研究计划对话框 -->
  <el-dialog :title="isEditingResearchPlan ? '制定研究计划' : '查看研究计划'" :visible.sync="researchPlanDialogVisible"
    width="1000px" @close="isEditingResearchPlan = false">
    <div v-if="researchPlanData">
      <!-- 课题基本信息 -->
      <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <h3 style="margin: 0 0 10px 0; color: #409EFF;">课题基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <p><strong>课题名称：</strong>{{ researchPlanFormData.subjectName }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>课题来源：</strong>{{ researchPlanFormData.subjectSource }}</p>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <p><strong>负责人：</strong>{{ researchPlanFormData.leader }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>研究方式：</strong>{{ researchPlanFormData.researchMethod }}</p>
          </el-col>
        </el-row>
        <div v-if="!isEditingResearchPlan" style="margin-top: 10px;">
          <p><strong>计划状态：</strong>
            <el-tag
              :type="researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 3 ? 'success' : researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 2 ? 'warning' : 'info'">
              {{ researchPlanData && researchPlanData.id ?
                getResearchPlanStatusText(researchPlanStatus[researchPlanData.id]) : '未知状态' }}
            </el-tag>
          </p>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div
        v-if="!isEditingResearchPlan && researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 3"
        style="margin-bottom: 20px; text-align: right;">
        <el-button type="primary" @click="startEditResearchPlan">修改研究计划</el-button>
      </div>

      <!-- 研究计划表单 -->
      <el-form :model="researchPlanFormData" label-width="120px" :disabled="!isEditingResearchPlan">
        <h3 style="margin-bottom: 15px; color: #409EFF;">研究计划详情</h3>

        <el-form-item label="研究目标" required>
          <el-input v-model="researchPlanFormData.researchObjective" type="textarea" :rows="3" placeholder="请输入研究目标">
          </el-input>
        </el-form-item>

        <el-form-item label="研究内容" required>
          <el-input v-model="researchPlanFormData.researchContent" type="textarea" :rows="4" placeholder="请输入详细的研究内容">
          </el-input>
        </el-form-item>

        <el-form-item label="技术路线" required>
          <el-input v-model="researchPlanFormData.technicalRoute" type="textarea" :rows="3" placeholder="请输入技术路线">
          </el-input>
        </el-form-item>

        <el-form-item label="预期成果" required>
          <el-input v-model="researchPlanFormData.expectedResults" type="textarea" :rows="3" placeholder="请输入预期成果">
          </el-input>
        </el-form-item>

        <el-form-item label="风险评估">
          <el-input v-model="researchPlanFormData.riskAssessment" type="textarea" :rows="2" placeholder="请输入风险评估">
          </el-input>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预算计划" required>
              <el-input v-model="researchPlanFormData.budgetPlan" type="textarea" :rows="2" placeholder="请输入预算计划">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时间安排" required>
              <el-input v-model="researchPlanFormData.timeline" type="textarea" :rows="2" placeholder="请输入时间安排">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队组成">
              <el-input v-model="researchPlanFormData.teamComposition" type="textarea" :rows="2" placeholder="请输入团队组成">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备需求">
              <el-input v-model="researchPlanFormData.equipmentRequirement" type="textarea" :rows="2"
                placeholder="请输入设备需求">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 修改原因（仅修改时显示） -->
        <el-form-item
          v-if="isEditingResearchPlan && researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 3"
          label="调整原因" required>
          <el-input v-model="researchPlanFormData.modifyReason" type="textarea" :rows="2" placeholder="请输入调整原因">
          </el-input>
        </el-form-item>

        <!-- 审批流程选择（仅编辑时显示） -->
        <div v-if="isEditingResearchPlan">
          <h3 style="margin: 20px 0 15px 0; color: #409EFF;">选择审批流程</h3>
          <el-form-item label="流程名称" required>
            <el-select v-model="researchPlanFormData.processName" placeholder="请选择审批流程" style="width: 100%">
              <el-option v-for="process in processOptions" :key="process.name" :label="process.name"
                :value="process.name">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="researchPlanDialogVisible = false">
        {{ isEditingResearchPlan ? '取消' : '关闭' }}
      </el-button>
      <el-button v-if="isEditingResearchPlan" type="primary" @click="submitResearchPlan">
        提交审批
      </el-button>
      <el-button
        v-if="!isEditingResearchPlan && researchPlanData && researchPlanData.id && researchPlanStatus[researchPlanData.id] === 3"
        @click="cancelEditResearchPlan">
        取消修改
      </el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
import QTable from '@/components/QTable'
export default {
  name: 'askProcess',
  components: {
    QTable,
  },
  data() {
    return {
      // 静态数据
      tableData: [
        {
          id: 1,
          subjectName: '盆地低渗透油气田含油污水回用处理技术及工程应用',
          subjectSource: '省国资委',
          leader: '田野',
          researchMethod: '自主',
          mainContent: '针对鄂尔多斯盆地低渗透油气田含油污水处理与循环利用、环境保护与储层保护等技术难题进行了多学科联合攻关，融合了化学化工、环境工程、石油工程等学科理论与技术，取得了以下重大创新与突破',
          expectedResults: '发明了含油、含甲醇采气污水预处理方法，形成了甲醇回收处理工艺技术。发明了以氮气气浮除油方法为主的含油污水综合处理技术，提出了一种全新的含油污水处理、回注工艺。自主研发了车载式作业废液密闭处理装置，形成了作业废液就地处理后直接进入油水集输系统的新技术。创新了煅烧污泥用作高强度混凝土添加剂技术；并应用自主研发的高温动态腐蚀检测仪，系统研究了油气田各种水系统的腐蚀结垢机理，开发出了系列、高效、低成本水处理药剂，形成了防腐阻垢成套技术。',
          startTime: '2025-07-17',
          endTime: '2025-12-17',
          researchPeriod: '2025/7/17----2025/12/17',
          status: '1', // 0 草稿 1 审批通过 2 审批中
        },
        {
          id: 2,
          subjectName: '智能制造关键技术研究与应用',
          subjectSource: '科技部',
          leader: '李明',
          researchMethod: '合作',
          mainContent: '围绕智能制造核心技术，开展工业互联网、人工智能、大数据等关键技术研究，构建智能制造技术体系，推动制造业数字化转型升级。',
          expectedResults: '形成一套完整的智能制造技术解决方案，建立智能制造示范生产线，提升生产效率30%以上，降低生产成本20%以上。',
          startTime: '2024-01-01',
          endTime: '2026-12-31',
          researchPeriod: '2024/1/1----2026/12/31',
          status: '0', // 0 草稿 1 审批通过 2 审批中
        },
        {
          id: 3,
          subjectName: '新能源汽车动力电池回收利用技术',
          subjectSource: '国家自然科学基金',
          leader: '王芳',
          researchMethod: '自主',
          mainContent: '研究新能源汽车动力电池的回收处理技术，开发高效的电池材料回收工艺，实现电池材料的循环利用，减少环境污染。',
          expectedResults: '建立完整的动力电池回收技术体系，实现锂、钴、镍等关键材料回收率达到95%以上，形成产业化应用示范。',
          startTime: '2024-03-01',
          endTime: '2027-02-28',
          researchPeriod: '2024/3/1----2027/2/28',
          status: '2', // 0 草稿 1 审批通过 2 审批中
        },
        {
          id: 4,
          subjectName: '5G通信网络优化算法研究',
          subjectSource: '教育部',
          leader: '张伟',
          researchMethod: '联合',
          mainContent: '针对5G网络的复杂性和多样性，研究网络优化算法，提升网络性能和用户体验，降低网络运营成本。',
          expectedResults: '提出新的5G网络优化算法，网络容量提升40%，延迟降低50%，能耗降低30%。',
          startTime: '2024-06-01',
          endTime: '2026-05-31',
          researchPeriod: '2024/6/1----2026/5/31',
          status: '1', // 0 草稿 1 审批通过 2 审批中
        },
        {
          id: 5,
          subjectName: '生物医学材料表面改性技术',
          subjectSource: '企业自主',
          leader: '陈红',
          researchMethod: '委托',
          mainContent: '开发新型生物医学材料表面改性技术，提高材料的生物相容性和功能性，应用于医疗器械和植入材料。',
          expectedResults: '开发出具有优异生物相容性的表面改性技术，材料植入成功率提升至98%以上。',
          startTime: '2024-09-01',
          endTime: '2025-08-31',
          researchPeriod: '2024/9/1----2025/8/31',
          status: '0', // 0 草稿 1 审批通过 2 审批中
        }
      ],
      columns: [
        {type: 'selection', width: 55},
        {prop: 'id', label: '序号', width: 50},
        {prop: 'subjectName', label: '课题名称', width: 300, showOverflowTooltip: true},
        {prop: 'subjectSource', label: '课题来源', width: 100, showOverflowTooltip: true},
        {prop: 'leader', label: '负责人', width: 100, showOverflowTooltip: true},
        {prop: 'researchMethod', label: '研究方式', width: 100, showOverflowTooltip: true},
        {prop: 'researchPeriod', label: '研究期限', width: 200, showOverflowTooltip: true},
        {prop: 'mainContent', label: '研究内容', width: 200, showOverflowTooltip: true},
        {type: 'tag', prop: 'status', label: '状态', width: 100,}
      ],
      config: {
        border: true,
        style: "width: 100%",
        height: "400px",
      },
      statusConfig: ['info', 'success', 'warning'],
      tagConfig: ['草稿', '审批通过', '审批中'],
      selectedRows: [],
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      // 对话框相关
      dialogVisible: false,
      viewDialogVisible: false,
      dialogTitle: '新增课题',
      isEdit: false,
      editId: null,
      viewData: null,
      // 表单数据
      formData: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        status: '',
        mainContent: '',
        expectedResults: '',
        startTime: '',
        endTime: ''
      },
      // 表单验证规则
      formRules: {
        subjectName: [
          { required: true, message: '请输入课题名称', trigger: 'blur' },
          { min: 5, max: 100, message: '长度在 5 到 100 个字符', trigger: 'blur' }
        ],
        subjectSource: [
          { required: true, message: '请选择课题来源', trigger: 'change' }
        ],
        leader: [
          { required: true, message: '请输入负责人姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        researchMethod: [
          { required: true, message: '请选择研究方式', trigger: 'change' }
        ],
        mainContent: [
          { required: true, message: '请输入主要研究内容', trigger: 'blur' }
        ],
        expectedResults: [
          { required: true, message: '请输入预期成果', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ]
      },
      queryForm: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        contentAndResults: '',
        approvalStatus: ''
      },
      // 原始数据备份，用于查询重置
      originalTableData: [],
      // 提交立项相关
      submitDialogVisible: false,
      progressDialogVisible: false, // 审批进度对话框
      currentProjectData: null, // 当前操作的项目数据
      submitFormData: {
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        projectStartTime: '',
        estimatedIncome: '',
        researchContent: '',
        processName: ''
      },
      // 流程选项
      processOptions: [
        {
          name: '标准审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '技术总监审批', approver: '李四' },
            { name: '总经理审批', approver: '王五' }
          ]
        },
        {
          name: '快速审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '总经理审批', approver: '王五' }
          ]
        },
        {
          name: '重大项目审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '技术总监审批', approver: '李四' },
            { name: '财务总监审批', approver: '赵六' },
            { name: '总经理审批', approver: '王五' },
            { name: '董事长审批', approver: '孙七' }
          ]
        }
      ],
      // OA人员名单（模拟数据）
      oaPersonnelList: [
        { id: 1, name: '张三', department: '技术部', position: '部门负责人' },
        { id: 2, name: '李四', department: '技术部', position: '技术总监' },
        { id: 3, name: '王五', department: '管理层', position: '总经理' },
        { id: 4, name: '赵六', department: '财务部', position: '财务总监' },
        { id: 5, name: '孙七', department: '管理层', position: '董事长' },
        { id: 6, name: '周八', department: '技术部', position: '高级工程师' },
        { id: 7, name: '吴九', department: '技术部', position: '项目经理' },
        { id: 8, name: '郑十', department: '研发部', position: '研发经理' }
      ],
      // 当前选择的流程
      selectedProcess: null,
      // 编辑模式相关
      isEditingProcess: false,
      editableProcess: null,
      // 研究计划相关
      researchPlanDialogVisible: false,
      researchPlanData: null, // 当前查看/编辑的研究计划
      isEditingResearchPlan: false,
      researchPlanFormData: {
        // 课题基本信息（只读）
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        // 研究计划字段
        researchObjective: '', // 研究目标
        researchContent: '', // 研究内容
        technicalRoute: '', // 技术路线
        expectedResults: '', // 预期成果
        riskAssessment: '', // 风险评估
        budgetPlan: '', // 预算计划
        timeline: '', // 时间安排
        teamComposition: '', // 团队组成
        equipmentRequirement: '', // 设备需求
        // 审批相关
        processName: '',
        // 修改相关
        modifyReason: '' // 调整原因（仅修改时需要）
      },
      // 研究计划状态：0-未填写，1-已填写未提交，2-审批中，3-审批通过
      researchPlanStatus: {},
      // 表单验证规则
      submitFormRules: {
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        delegateUnit: [
          { required: true, message: '请输入委托单位', trigger: 'blur' }
        ],
        projectNumber: [
          { required: true, message: '请输入项目编号', trigger: 'blur' }
        ],
        projectLeader: [
          { required: true, message: '请选择项目负责人', trigger: 'change' }
        ],
        projectStartTime: [
          { required: true, message: '请选择项目立项时间', trigger: 'change' }
        ],
        estimatedIncome: [
          { required: true, message: '请输入预估收入', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        researchContent: [
          { required: true, message: '请输入研究内容', trigger: 'blur' }
        ],
        processName: [
          { required: true, message: '请选择流程名称', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    // 当前页数据
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.tableData.slice(start, end)
    }
  },
  created() {
    // 备份原始数据
    this.originalTableData = JSON.parse(JSON.stringify(this.tableData))
    this.totalCount = this.tableData.length

    // 初始化研究计划状态（模拟数据）
    this.researchPlanStatus = {
      1: 3, // 审批通过
      2: 0, // 未填写
      3: 2, // 审批中
      4: 1, // 已填写未提交
      5: 0  // 未填写
    }
  },
  methods: {
    // 根据状态获取标签类型
    getStatusType(status) {
      const statusMap = {
        '0': 'info',    // 草稿 - 灰色
        '1': 'success', // 审批通过 - 绿色
        '2': 'warning'  // 审批中 - 橙色
      }
      return statusMap[status] || 'info'
    },

    // 根据状态获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '草稿',
        '1': '审批通过',
        '2': '审批中'
      }
      return statusMap[status] || '未知状态'
    },

    // 表格选择变化
    handleSelectionChange(val) {
      this.selectedRows = val
    },

    // 查询功能
    handleQuery() {
      let filteredData = JSON.parse(JSON.stringify(this.originalTableData))

      // 根据课题名称筛选（关键字查询）
      if (this.queryForm.subjectName && this.queryForm.subjectName.trim()) {
        filteredData = filteredData.filter(item =>
          item.subjectName.toLowerCase().includes(this.queryForm.subjectName.trim().toLowerCase())
        )
      }

      // 根据课题来源筛选（关键字查询）
      if (this.queryForm.subjectSource && this.queryForm.subjectSource.trim()) {
        filteredData = filteredData.filter(item =>
          item.subjectSource.toLowerCase().includes(this.queryForm.subjectSource.trim().toLowerCase())
        )
      }

      // 根据负责人筛选（精确匹配）
      if (this.queryForm.leader && this.queryForm.leader.trim()) {
        filteredData = filteredData.filter(item =>
          item.leader === this.queryForm.leader
        )
      }

      // 根据研究方式筛选（精确匹配）
      if (this.queryForm.researchMethod && this.queryForm.researchMethod.trim()) {
        filteredData = filteredData.filter(item =>
          item.researchMethod === this.queryForm.researchMethod
        )
      }

      // 根据主要内容及预期成果筛选（关键字查询）
      if (this.queryForm.contentAndResults && this.queryForm.contentAndResults.trim()) {
        const keyword = this.queryForm.contentAndResults.trim().toLowerCase()
        filteredData = filteredData.filter(item =>
          item.mainContent.toLowerCase().includes(keyword) ||
          item.expectedResults.toLowerCase().includes(keyword)
        )
      }

      // 根据审批结果筛选（精确匹配）
      if (this.queryForm.approvalStatus && this.queryForm.approvalStatus.trim()) {
        filteredData = filteredData.filter(item =>
          item.status === this.queryForm.approvalStatus
        )
      }

      this.tableData = filteredData
      this.totalCount = filteredData.length
      this.currentPage = 1 // 重置到第一页

      this.$message.success(`查询完成，共找到 ${filteredData.length} 条记录`)
    },

    // 重置功能
    handleReset() {
      // 重置查询表单
      this.queryForm = {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        contentAndResults: '',
        approvalStatus: ''
      }

      // 恢复原始数据
      this.tableData = JSON.parse(JSON.stringify(this.originalTableData))
      this.totalCount = this.originalTableData.length
      this.currentPage = 1

      // 重置表单验证
      if (this.$refs.queryForm) {
        this.$refs.queryForm.resetFields()
      }

      this.$message.success('重置成功')
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    // 当前页变化
    handleCurrentChange(val) {
      this.currentPage = val
    },
    // 新增
    handleAdd() {
      this.dialogTitle = '新增课题'
      this.isEdit = false
      this.editId = null
      this.resetForm()
      this.dialogVisible = true
    },
    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑课题'
      this.isEdit = true
      this.editId = row.id
      this.formData = {
        subjectName: row.subjectName,
        subjectSource: row.subjectSource,
        leader: row.leader,
        researchMethod: row.researchMethod,
        status: row.status,
        mainContent: row.mainContent,
        expectedResults: row.expectedResults,
        startTime: row.startTime,
        endTime: row.endTime
      }
      this.dialogVisible = true
    },

    // 提交立项
    handleSubmitPreProject(row) {
      this.currentProjectData = row

      if (row.status === '1') {
        // 审批通过状态 - 显示审批进度
        this.progressDialogVisible = true
      } else if (row.status === '2') {
        // 审批中状态 - 显示审批进度
        this.progressDialogVisible = true
      } else {
        // 草稿状态 - 显示填写表单
        this.submitFormData = {
          projectName: row.subjectName,
          delegateUnit: '',
          projectNumber: '',
          projectLeader: '',
          projectStartTime: '',
          estimatedIncome: '',
          researchContent: row.mainContent,
          processName: ''
        }
        this.selectedProcess = null
        this.submitDialogVisible = true
      }
    },

    // 流程选择变化
    handleProcessChange(processName) {
      this.selectedProcess = this.processOptions.find(p => p.name === processName)
      // 创建可编辑的流程副本
      if (this.selectedProcess) {
        this.editableProcess = {
          name: this.selectedProcess.name,
          steps: this.selectedProcess.steps.map(step => ({
            name: step.name,
            approver: step.approver
          }))
        }
      }
    },

    // 开启编辑模式
    startEditProcess() {
      this.isEditingProcess = true
    },

    // 取消编辑
    cancelEditProcess() {
      this.isEditingProcess = false
      // 恢复原始数据
      if (this.selectedProcess) {
        this.editableProcess = {
          name: this.selectedProcess.name,
          steps: this.selectedProcess.steps.map(step => ({
            name: step.name,
            approver: step.approver
          }))
        }
      }
    },

    // 保存编辑
    saveEditProcess() {
      if (this.editableProcess && this.editableProcess.steps.length > 0) {
        // 验证所有步骤都有审批人
        const hasEmptyApprover = this.editableProcess.steps.some(step => !step.approver || !step.approver.trim())
        if (hasEmptyApprover) {
          this.$message.warning('请为所有审批步骤指定审批人')
          return
        }

        // 更新选中的流程
        this.selectedProcess = {
          name: this.editableProcess.name,
          steps: [...this.editableProcess.steps]
        }

        this.isEditingProcess = false
        this.$message.success('审批流程已更新')
      }
    },

    // 添加审批步骤
    addProcessStep() {
      if (this.editableProcess) {
        this.editableProcess.steps.push({
          name: '',
          approver: ''
        })
      }
    },

    // 删除审批步骤
    removeProcessStep(index) {
      if (this.editableProcess && this.editableProcess.steps.length > 1) {
        this.editableProcess.steps.splice(index, 1)
      } else {
        this.$message.warning('至少需要保留一个审批步骤')
      }
    },

    // 处理研究计划
    handleResearchPlan(row) {
      if (!row || !row.id) {
        this.$message.error('数据异常，请刷新页面重试')
        return
      }

      const planStatus = this.researchPlanStatus[row.id] || 0

      if (planStatus === 0) {
        // 未填写 - 新建研究计划
        this.startNewResearchPlan(row)
      } else {
        // 已有研究计划 - 查看或编辑
        this.viewResearchPlan(row)
      }
    },

    // 新建研究计划
    startNewResearchPlan(row) {
      this.researchPlanFormData = {
        // 课题基本信息
        subjectName: row.subjectName,
        subjectSource: row.subjectSource,
        leader: row.leader,
        researchMethod: row.researchMethod,
        // 研究计划字段
        researchObjective: '',
        researchContent: '',
        technicalRoute: '',
        expectedResults: '',
        riskAssessment: '',
        budgetPlan: '',
        timeline: '',
        teamComposition: '',
        equipmentRequirement: '',
        processName: '',
        modifyReason: ''
      }
      this.researchPlanData = row
      this.isEditingResearchPlan = true
      this.researchPlanDialogVisible = true
    },

    // 查看研究计划
    viewResearchPlan(row) {
      // 获取已保存的研究计划数据（模拟数据）
      const savedPlan = this.getSavedResearchPlan(row.id)
      this.researchPlanFormData = savedPlan
      this.researchPlanData = row

      // 只有审批通过的计划才能编辑
      this.isEditingResearchPlan = false
      this.researchPlanDialogVisible = true
    },

    // 获取已保存的研究计划（模拟数据）
    getSavedResearchPlan(projectId) {
      const savedPlans = {
        1: {
          subjectName: '盆地低渗透油气田含油污水回用处理技术及工程应用',
          subjectSource: '省国资委',
          leader: '田野',
          researchMethod: '自主',
          researchObjective: '解决鄂尔多斯盆地低渗透油气田含油污水处理与循环利用技术难题',
          researchContent: '开展含油污水预处理、氮气气浮除油、作业废液处理等关键技术研究',
          technicalRoute: '实验室研究 → 中试试验 → 工程示范 → 产业化应用',
          expectedResults: '形成完整的含油污水处理技术体系和装备',
          riskAssessment: '技术风险：中等；市场风险：低；资金风险：低',
          budgetPlan: '总预算150万元，其中设备费60万，人员费50万，材料费40万',
          timeline: '第一阶段：理论研究（1-6月）；第二阶段：实验验证（7-12月）',
          teamComposition: '项目负责人1名，高级工程师2名，工程师3名，研究生2名',
          equipmentRequirement: '高温动态腐蚀检测仪、气浮设备、水质分析仪器等',
          processName: '标准审批流程',
          modifyReason: ''
        },
        3: {
          subjectName: '新能源汽车动力电池回收利用技术',
          subjectSource: '国家自然科学基金',
          leader: '王芳',
          researchMethod: '自主',
          researchObjective: '建立完整的动力电池回收技术体系',
          researchContent: '研究电池拆解、材料分离、有价金属回收等关键技术',
          technicalRoute: '电池拆解 → 材料分离 → 金属提取 → 材料再生',
          expectedResults: '实现锂、钴、镍等关键材料回收率达到95%以上',
          riskAssessment: '技术风险：高；环保风险：中等；经济风险：中等',
          budgetPlan: '总预算200万元，设备投入占60%，人员成本占30%',
          timeline: '研发周期24个月，分为4个阶段实施',
          teamComposition: '博士2名，硕士4名，技术员6名',
          equipmentRequirement: '拆解设备、分离设备、检测仪器等',
          processName: '重大项目审批流程',
          modifyReason: ''
        },
        4: {
          subjectName: '5G通信网络优化算法研究',
          subjectSource: '教育部',
          leader: '张伟',
          researchMethod: '联合',
          researchObjective: '提升5G网络性能和用户体验',
          researchContent: '研究网络优化算法，降低延迟，提高容量',
          technicalRoute: '算法设计 → 仿真验证 → 现网测试',
          expectedResults: '网络容量提升40%，延迟降低50%',
          riskAssessment: '技术难度大，需要与运营商密切合作',
          budgetPlan: '120万元，主要用于算法开发和测试',
          timeline: '18个月完成，分3个阶段',
          teamComposition: '算法工程师4名，测试工程师2名',
          equipmentRequirement: '高性能计算服务器、网络测试设备',
          processName: '快速审批流程',
          modifyReason: ''
        }
      }

      return savedPlans[projectId] || {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        researchObjective: '',
        researchContent: '',
        technicalRoute: '',
        expectedResults: '',
        riskAssessment: '',
        budgetPlan: '',
        timeline: '',
        teamComposition: '',
        equipmentRequirement: '',
        processName: '',
        modifyReason: ''
      }
    },

    // 开始编辑研究计划（仅审批通过的可编辑）
    startEditResearchPlan() {
      this.isEditingResearchPlan = true
    },

    // 取消编辑研究计划
    cancelEditResearchPlan() {
      this.isEditingResearchPlan = false
      // 恢复原始数据
      const savedPlan = this.getSavedResearchPlan(this.researchPlanData.id)
      this.researchPlanFormData = savedPlan
    },

    // 提交研究计划
    submitResearchPlan() {
      // 验证表单
      if (!this.validateResearchPlan()) {
        return
      }

      // 这里应该调用API提交数据
      this.$message.success('研究计划提交成功！')

      // 更新状态为审批中
      this.researchPlanStatus[this.researchPlanData.id] = 2
      this.researchPlanDialogVisible = false
    },

    // 验证研究计划表单
    validateResearchPlan() {
      const requiredFields = [
        'researchObjective', 'researchContent', 'technicalRoute',
        'expectedResults', 'budgetPlan', 'timeline', 'processName'
      ]

      for (let field of requiredFields) {
        if (!this.researchPlanFormData[field] || !this.researchPlanFormData[field].trim()) {
          this.$message.warning('请填写完整的研究计划信息')
          return false
        }
      }

      // 如果是修改模式，检查调整原因
      if (this.researchPlanStatus[this.researchPlanData.id] === 3 && this.isEditingResearchPlan) {
        if (!this.researchPlanFormData.modifyReason || !this.researchPlanFormData.modifyReason.trim()) {
          this.$message.warning('请填写调整原因')
          return false
        }
      }

      return true
    },

    // 获取研究计划状态文本
    getResearchPlanStatusText(status) {
      const statusMap = {
        0: '未填写',
        1: '已填写未提交',
        2: '审批中',
        3: '审批通过'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取立项信息（模拟数据）
    getPreProjectInfo(projectId) {
      // 根据项目ID返回立项信息，这里使用模拟数据
      const preProjectData = {
        1: {
          projectName: '盆地低渗透油气田含油污水回用处理技术及工程应用',
          delegateUnit: '中石油勘探开发研究院',
          projectNumber: 'PRJ-2024-001',
          projectLeader: '田野',
          projectStartTime: '2024-01-15',
          estimatedIncome: '150.5'
        },
        2: {
          projectName: '智能制造关键技术研究与应用',
          delegateUnit: '工信部智能制造研究院',
          projectNumber: 'PRJ-2024-002',
          projectLeader: '李明',
          projectStartTime: '2024-01-20',
          estimatedIncome: '200.0'
        },
        3: {
          projectName: '新能源汽车动力电池回收利用技术',
          delegateUnit: '国家新能源汽车技术创新中心',
          projectNumber: 'PRJ-2024-003',
          projectLeader: '王芳',
          projectStartTime: '2024-01-25',
          estimatedIncome: '180.8'
        },
        4: {
          projectName: '5G通信网络优化算法研究',
          delegateUnit: '中国移动研究院',
          projectNumber: 'PRJ-2024-004',
          projectLeader: '张伟',
          projectStartTime: '2024-01-30',
          estimatedIncome: '120.0'
        },
        5: {
          projectName: '生物医学材料表面改性技术',
          delegateUnit: '国家生物医学材料工程技术研究中心',
          projectNumber: 'PRJ-2024-005',
          projectLeader: '陈红',
          projectStartTime: '2024-02-01',
          estimatedIncome: '95.5'
        }
      }

      return preProjectData[projectId] || {
        projectName: '未知项目',
        delegateUnit: '未知单位',
        projectNumber: '未知编号',
        projectLeader: '未知负责人',
        projectStartTime: '未知时间',
        estimatedIncome: '0'
      }
    },

    // 获取项目审批进度信息（模拟数据）
    getProjectProgress(status) {
      // 根据状态返回不同的审批进度
      const baseSteps = [
        { name: '部门负责人审批', approver: '张三', status: 'finish', time: '2024-01-15 10:30' },
        { name: '技术总监审批', approver: '李四', status: 'finish', time: '2024-01-16 14:20' },
        { name: '总经理审批', approver: '王五', status: 'finish', time: '2024-01-17 09:15' }
      ]

      if (status === '1') {
        // 审批通过 - 所有步骤完成
        return {
          processName: '标准审批流程',
          currentStep: 3,
          steps: baseSteps,
          finalStatus: '审批通过',
          finalTime: '2024-01-17 09:15'
        }
      } else if (status === '2') {
        // 审批中 - 部分步骤完成
        return {
          processName: '标准审批流程',
          currentStep: 1,
          steps: [
            { name: '部门负责人审批', approver: '张三', status: 'finish', time: '2024-01-15 10:30' },
            { name: '技术总监审批', approver: '李四', status: 'process', time: '' },
            { name: '总经理审批', approver: '王五', status: 'wait', time: '' }
          ],
          finalStatus: '审批中',
          finalTime: ''
        }
      }

      return null
    },

    // 确认提交立项
    handleConfirmSubmit() {
      this.$refs.submitForm.validate((valid) => {
        if (valid) {
          // 这里可以调用API提交数据
          this.$message.success('立项提交成功！')
          this.submitDialogVisible = false
          this.resetSubmitForm()
        }
      })
    },

    // 重置提交表单
    resetSubmitForm() {
      this.submitFormData = {
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        projectStartTime: '',
        estimatedIncome: '',
        researchContent: '',
        processName: ''
      }
      this.selectedProcess = null
      this.isEditingProcess = false
      this.editableProcess = null
      if (this.$refs.submitForm) {
        this.$refs.submitForm.resetFields()
      }
    },
    // 查看详情
    handleView(row) {
      this.viewData = row
      this.viewDialogVisible = true
    },
    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该课题吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从当前显示数据中删除
        const index = this.tableData.findIndex(item => item.id === row.id)
        if (index > -1) {
          this.tableData.splice(index, 1)
        }

        // 从原始数据中删除
        const originalIndex = this.originalTableData.findIndex(item => item.id === row.id)
        if (originalIndex > -1) {
          this.originalTableData.splice(originalIndex, 1)
        }

        this.totalCount = this.tableData.length
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 提交表单
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const researchPeriod = `${this.formData.startTime}----${this.formData.endTime}`

          if (this.isEdit) {
            // 编辑当前显示数据
            const index = this.tableData.findIndex(item => item.id === this.editId)
            if (index > -1) {
              this.tableData[index] = {
                ...this.tableData[index],
                ...this.formData,
                researchPeriod
              }
            }

            // 编辑原始数据
            const originalIndex = this.originalTableData.findIndex(item => item.id === this.editId)
            if (originalIndex > -1) {
              this.originalTableData[originalIndex] = {
                ...this.originalTableData[originalIndex],
                ...this.formData,
                researchPeriod
              }
            }

            this.$message.success('编辑成功')
          } else {
            // 新增
            const newId = Math.max(...this.originalTableData.map(item => item.id)) + 1
            const newItem = {
              id: newId,
              ...this.formData,
              researchPeriod
            }
            // 同时添加到原始数据和当前显示数据
            this.originalTableData.push(newItem)
            this.tableData.push(newItem)
            this.totalCount = this.tableData.length
            this.$message.success('新增成功')
          }
          this.dialogVisible = false
        }
      })
    },
    // 重置表单
    resetForm() {
      this.formData = {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        status: '',
        mainContent: '',
        expectedResults: '',
        startTime: '',
        endTime: ''
      }
      if (this.$refs.dataForm) {
        this.$refs.dataForm.resetFields()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}

// 查询工具栏样式
.el-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;

  .el-row {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-form-item {
    margin-bottom: 10px;
  }
}

// 查询按钮组样式
.el-button+.el-button {
  margin-left: 10px;
}

// 展开/收起按钮样式
.el-button--text {
  padding: 0;
  margin-left: 15px;

  i {
    margin-left: 5px;
  }
}
</style>
