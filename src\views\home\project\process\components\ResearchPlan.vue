<template>
  <div class="research-plan-container">
    <el-card>
      <div slot="header">
        <span>📋 研究计划管理</span>
        <el-button v-if="planStatus === 0" style="float: right; padding: 3px 0" type="text" @click="startNewPlan">
          新建研究计划
        </el-button>
        <el-button v-else style="float: right; padding: 3px 0" type="text" @click="editPlan">
          编辑计划
        </el-button>
      </div>

      <!-- 研究计划状态显示 -->
      <div class="plan-status" style="margin-bottom: 20px;">
        <el-alert :title="getStatusTitle()" :type="getStatusType()" :description="getStatusDescription()" show-icon
          :closable="false">
        </el-alert>
      </div>

      <!-- 研究计划内容显示 -->
      <div v-if="planStatus > 0" class="plan-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="研究目标">
            <div style="white-space: pre-wrap;">{{ planData.researchObjective }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="研究内容">
            <div style="white-space: pre-wrap;">{{ planData.researchContent }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="研究计划">
            <div style="white-space: pre-wrap;">{{ planData.researchPlan }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="预期成果">
            <div style="white-space: pre-wrap;">{{ planData.expectedResults }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="风险评估">
            <div style="white-space: pre-wrap;">{{ planData.riskAssessment }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="预算计划">
            <div style="white-space: pre-wrap;">{{ planData.budgetPlan }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="时间安排">
            <div style="white-space: pre-wrap;">{{ planData.timeline }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px; text-align: right;">
          <el-button v-if="planStatus === 1" type="primary" @click="submitPlan">提交计划</el-button>
          <el-button type="default" @click="editPlan">编辑计划</el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state" style="text-align: center; padding: 40px;">
        <i class="el-icon-document" style="font-size: 64px; color: #C0C4CC;"></i>
        <p style="color: #909399; margin-top: 16px;">暂无研究计划</p>
        <el-button type="primary" @click="startNewPlan">创建研究计划</el-button>
      </div>
    </el-card>

    <!-- 编辑研究计划对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false">

      <el-form :model="formData" :rules="formRules" ref="planForm" label-width="120px">
        <el-form-item label="研究目标" prop="researchObjective">
          <el-input v-model="formData.researchObjective" type="textarea" :rows="3" placeholder="请描述本课题的研究目标和意义">
          </el-input>
        </el-form-item>

        <el-form-item label="研究内容" prop="researchContent">
          <el-input v-model="formData.researchContent" type="textarea" :rows="4" placeholder="请详细描述研究的主要内容和技术路线">
          </el-input>
        </el-form-item>

        <el-form-item label="研究计划" prop="researchPlan">
          <el-input v-model="formData.researchPlan" type="textarea" :rows="4" placeholder="请制定详细的研究计划和实施步骤">
          </el-input>
        </el-form-item>

        <el-form-item label="预期成果" prop="expectedResults">
          <el-input v-model="formData.expectedResults" type="textarea" :rows="3" placeholder="请描述预期的研究成果和产出">
          </el-input>
        </el-form-item>

        <el-form-item label="风险评估" prop="riskAssessment">
          <el-input v-model="formData.riskAssessment" type="textarea" :rows="3" placeholder="请分析可能遇到的风险和应对措施">
          </el-input>
        </el-form-item>

        <el-form-item label="预算计划" prop="budgetPlan">
          <el-input v-model="formData.budgetPlan" type="textarea" :rows="3" placeholder="请制定详细的预算计划">
          </el-input>
        </el-form-item>

        <el-form-item label="时间安排" prop="timeline">
          <el-input v-model="formData.timeline" type="textarea" :rows="3" placeholder="请安排详细的时间进度计划">
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePlan">保存</el-button>
        <el-button v-if="planStatus === 1" type="success" @click="saveAndSubmit">保存并提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ResearchPlan',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      planStatus: 0, // 0: 未填写, 1: 已填写未提交, 2: 已填写已提交
      planData: {},
      formData: {
        researchObjective: '',
        researchContent: '',
        researchPlan: '',
        expectedResults: '',
        riskAssessment: '',
        budgetPlan: '',
        timeline: ''
      },
      formRules: {
        researchObjective: [
          { required: true, message: '请输入研究目标', trigger: 'blur' },
          { min: 10, message: '研究目标至少10个字符', trigger: 'blur' }
        ],
        researchContent: [
          { required: true, message: '请输入研究内容', trigger: 'blur' },
          { min: 20, message: '研究内容至少20个字符', trigger: 'blur' }
        ],
        researchPlan: [
          { required: true, message: '请输入研究计划', trigger: 'blur' },
          { min: 20, message: '研究计划至少20个字符', trigger: 'blur' }
        ],
        expectedResults: [
          { required: true, message: '请输入预期成果', trigger: 'blur' }
        ],
        riskAssessment: [
          { required: true, message: '请输入风险评估', trigger: 'blur' }
        ],
        budgetPlan: [
          { required: true, message: '请输入预算计划', trigger: 'blur' }
        ],
        timeline: [
          { required: true, message: '请输入时间安排', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loadPlanData()
  },
  methods: {
    // 加载研究计划数据
    loadPlanData() {
      // 这里应该从后端API获取研究计划数据
      // 暂时设置为未填写状态，实际应该调用API
      this.planStatus = 0 // 默认未填写

      // 可以在这里调用API获取研究计划数据
      // this.fetchResearchPlanData()
    },

    // 可选：从API获取研究计划数据
    async fetchResearchPlanData() {
      try {
        // 这里应该调用实际的API
        // const response = await this.$api.getResearchPlan(this.projectInfo.id)
        // this.planStatus = response.data.status
        // this.planData = response.data.planData

        console.log('获取研究计划数据:', this.projectInfo.id)
      } catch (error) {
        console.error('获取研究计划数据失败:', error)
        this.$message.error('获取研究计划数据失败')
      }
    },

    // 开始新建计划
    startNewPlan() {
      this.dialogTitle = '新建研究计划'
      this.formData = {
        researchObjective: '',
        researchContent: '',
        researchPlan: '',
        expectedResults: '',
        riskAssessment: '',
        budgetPlan: '',
        timeline: ''
      }
      this.dialogVisible = true
    },

    // 编辑计划
    editPlan() {
      this.dialogTitle = '编辑研究计划'
      this.formData = { ...this.planData }
      this.dialogVisible = true
    },

    // 保存计划
    savePlan() {
      this.$refs.planForm.validate((valid) => {
        if (valid) {
          this.planData = { ...this.formData }
          this.planStatus = 1 // 已填写未提交
          this.dialogVisible = false
          this.$message.success('研究计划保存成功')
        }
      })
    },

    // 保存并提交
    saveAndSubmit() {
      this.$refs.planForm.validate((valid) => {
        if (valid) {
          this.planData = { ...this.formData }
          this.planStatus = 2 // 已填写已提交
          this.dialogVisible = false
          this.$message.success('研究计划提交成功')
        }
      })
    },

    // 提交计划
    submitPlan() {
      this.$confirm('确认提交研究计划吗？提交后将无法修改。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.planStatus = 2
        this.$message.success('研究计划提交成功')
      })
    },

    // 获取状态标题
    getStatusTitle() {
      const titles = {
        0: '未填写研究计划',
        1: '研究计划已保存',
        2: '研究计划已提交'
      }
      return titles[this.planStatus] || '未知状态'
    },

    // 获取状态类型
    getStatusType() {
      const types = {
        0: 'info',
        1: 'warning',
        2: 'success'
      }
      return types[this.planStatus] || 'info'
    },

    // 获取状态描述
    getStatusDescription() {
      const descriptions = {
        0: '请创建并填写研究计划',
        1: '研究计划已保存，可以继续编辑或提交',
        2: '研究计划已提交，等待审核'
      }
      return descriptions[this.planStatus] || ''
    }
  }
}
</script>

<style scoped>
.research-plan-container {
  height: 100%;
}

.plan-status {
  margin-bottom: 20px;
}

.plan-content {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  text-align: right;
}
</style>
