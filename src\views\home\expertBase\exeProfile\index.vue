<template>
  <div class="expert-profile">
    <!-- 搜索模块：单独一行 -->
    <div class="search-section">
      <el-select v-model="searchKeyword" filterable placeholder="请选择专家姓名" clearable>
        <el-option v-for="expert in experts" :key="expert.name" :label="expert.name" :value="expert.name">
        </el-option>
      </el-select>
      <el-button type="primary" icon="el-icon-search" @click="handleSearch">
        搜索
      </el-button>
    </div>

    <!-- 专家基本信息 -->
    <el-card class="info-card">
      <div class="top-info">
        <div class="avatar-name">
          <el-avatar :size="80" :src="currentExpert.avatar" class="avatar">
            {{ currentExpert.name.charAt(0) }}
          </el-avatar>
          <div class="name-title">
            <h2>{{ currentExpert.name }}</h2>
            <p>{{ currentExpert.title }}</p>
          </div>
        </div>
      </div>

      <!-- 基本信息模块 -->
      <div class="basic-info">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <i class="el-icon-male info-icon gender"></i>
              <div class="info-text">
                <div class="info-label">性别</div>
                <div class="info-value">{{ currentExpert.gender }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <i class="el-icon-user info-icon age"></i>
              <div class="info-text">
                <div class="info-label">年龄</div>
                <div class="info-value">{{ currentExpert.age }}岁</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <i class="el-icon-school info-icon education"></i>
              <div class="info-text">
                <div class="info-label">教育背景</div>
                <div class="info-value">{{ currentExpert.education }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <i class="el-icon-office-building info-icon workunit"></i>
              <div class="info-text">
                <div class="info-label">工作单位</div>
                <div class="info-value">{{ currentExpert.workUnit }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
        <!-- <div class="work-hours">
          <i class="el-icon-time info-icon workhours"></i>
          <div class="info-text">
            <div class="info-label">工作时长</div>
            <div class="info-value">{{ currentExpert.workHours }}小时</div>
          </div>
        </div> -->
      </div>
    </el-card>

    <!-- 专长领域模块 -->
    <el-card class="specialty-card">
      <div slot="header" class="clearfix">
        <h3><i class="el-icon-star-on"></i> 专长领域</h3>
      </div>
      <div class="specialty-tags">
        <el-tag v-for="(tag, index) in currentExpert.specialty" :key="index" size="medium" :type="getTagType(index)">
          {{ tag }}
        </el-tag>
      </div>
    </el-card>

    <!-- 项目经历和培训经历模块 -->
    <div class="experience-section">
      <!-- 左侧：项目经历 -->
      <div class="project-experience">
        <el-card class="experience-card">
          <div slot="header">
            <h3><i class="el-icon-folder-opened"></i> 项目经历</h3>
          </div>
          <el-timeline>
            <el-timeline-item v-for="(project, index) in currentExpert.projects" :key="index"
              :timestamp="project.period" placement="top">
              <el-card class="timeline-card">
                <h4>{{ project.title }}</h4>
                <p>{{ project.description }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>

      <!-- 右侧：培训经历 -->
      <div class="training-experience">
        <el-card class="experience-card">
          <div slot="header">
            <h3><i class="el-icon-suitcase"></i> 培训经历</h3>
          </div>
          <el-timeline>
            <el-timeline-item v-for="(training, index) in currentExpert.trainings" :key="index"
              :timestamp="training.date" placement="top">
              <el-card class="timeline-card">
                <h4>{{ training.title }}</h4>
                <p>{{ training.description }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      experts: [
        {
          name: '田高良',
          avatar: '',
          title: '财务管理教授 · 资深培训专家',
          gender: '男',
          age: 45,
          education: '博士研究生',
          workUnit: '西安财经大学',
          workHours: 150,
          specialty: ['财务预警', '内部控制', '风险管理', '资产评估', '信用管理', '财务管理'],
          score: 97,
          projects: [
            {
              title: '某央企财务风险预警系统构建',
              period: '2020.03 - 2020.12',
              description: '为某大型央企构建了完整的财务风险预警系统，帮助客户建立了全面的财务风险防控体系'
            },
            {
              title: '某省属国企内部控制体系建设',
              period: '2019.06 - 2019.11',
              description: '主导某省属国企的内部控制体系建设，帮助客户完善了各项内部控制制度'
            },
            {
              title: '某上市公司资产评估项目',
              period: '2018.09 - 2019.02',
              description: '参与某上市公司的并购重组资产评估项目，为客户提供了专业的资产评估服务'
            }
          ],
          trainings: [
            {
              title: '财务风险管理高级研修班',
              date: '2021.08',
              description: '为某大型国有企业集团开展为期5天的财务风险管理高级研修班，培训学员80余人次'
            },
            {
              title: '内部控制与风险管理专题培训',
              date: '2021.03',
              description: '为某省属企业集团提供内部控制与风险管理专题培训，培训中层以上管理人员60余人'
            },
            {
              title: '企业财务报表分析专题培训',
              date: '2020.11',
              description: '为某金融企业开展企业财务报表分析专题培训，培训财务人员50余人次'
            }
          ]
        },
        {
          name: '张伟',
          avatar: '',
          title: '计算机科学教授 · 人工智能专家',
          gender: '男',
          age: 50,
          education: '博士研究生',
          workUnit: '清华大学',
          workHours: 200,
          specialty: ['机器学习', '深度学习', '自然语言处理', '人工智能', '大数据分析'],
          score: 95,
          projects: [
            {
              title: '某金融机构智能风控系统开发',
              period: '2021.01 - 2021.12',
              description: '领导团队为某大型金融机构开发了智能风控系统，显著提升了风险识别和预警能力'
            },
            {
              title: '某科技公司智能客服系统开发',
              period: '2019.08 - 2020.05',
              description: '为某科技公司开发了基于自然语言处理的智能客服系统，大幅提升了客户服务效率'
            },
            {
              title: '某政府部门大数据分析平台建设',
              period: '2018.03 - 2018.12',
              description: '主导某政府部门的大数据分析平台建设项目，帮助客户建立了完善的数据分析体系'
            }
          ],
          trainings: [
            {
              title: '人工智能基础培训',
              date: '2022.05',
              description: '为某科技园区企业开展为期3天的人工智能基础培训，培训学员120余人次'
            },
            {
              title: '深度学习技术专题培训',
              date: '2021.11',
              description: '为某高校师生开展深度学习技术专题培训，培训中高级技术人员80余人'
            },
            {
              title: '大数据分析应用专题讲座',
              date: '2021.07',
              description: '为某政府部门开展大数据分析应用专题讲座，培训管理人员50余人次'
            }
          ]
        },
        {
          name: '李娜',
          avatar: '',
          title: '心理学博士 · 咨询专家',
          gender: '女',
          age: 38,
          education: '硕士研究生',
          workUnit: '北京大学',
          workHours: 120,
          specialty: ['心理咨询', '情绪管理', '职业规划', '心理分析'],
          score: 91,
          projects: [
            {
              title: '某大型企业员工心理健康项目',
              period: '2021.02 - 2021.12',
              description: '为某大型企业设计并实施了员工心理健康项目，有效提升了员工的工作满意度和工作效率'
            },
            {
              title: '某高校学生心理辅导项目',
              period: '2019.09 - 2020.06',
              description: '为某高校设计并实施了学生心理辅导项目，为超过500名学生提供了心理咨询服务'
            },
            {
              title: '某政府机构干部心理调适培训',
              period: '2018.04 - 2018.12',
              description: '为某政府机构开展干部心理调适培训，帮助干部提升心理素质和压力管理能力'
            }
          ],
          trainings: [
            {
              title: '职场压力管理专题培训',
              date: '2022.09',
              description: '为某大型企业开展职场压力管理专题培训，培训员工150余人次'
            },
            {
              title: '心理咨询师专业技能培训',
              date: '2022.03',
              description: '为某心理咨询机构开展心理咨询师专业技能培训，培训学员80余人次'
            },
            {
              title: '职业规划与发展专题讲座',
              date: '2021.11',
              description: '为某高校毕业生开展职业规划与发展专题讲座，培训学生200余人次'
            }
          ]
        },
        {
          name: '王强',
          avatar: '',
          title: '机械工程专家 · 高级工程师',
          gender: '男',
          age: 42,
          education: '博士研究生',
          workUnit: '中国科学院',
          workHours: 180,
          specialty: ['机械设计', '自动化控制', '智能制造', '工业优化'],
          score: 88,
          projects: [
            {
              title: '某汽车企业智能制造升级项目',
              period: '2020.05 - 2021.02',
              description: '为某汽车企业实施智能制造升级项目，帮助客户实现了生产效率的显著提升'
            },
            {
              title: '某工程机械优化设计项目',
              period: '2019.01 - 2019.10',
              description: '为某工程机械企业开展优化设计项目，帮助客户优化了多个关键部件的设计'
            },
            {
              title: '某制造企业自动化控制系统开发',
              period: '2017.08 - 2018.05',
              description: '为某制造企业开发自动化控制系统，帮助客户实现了生产过程的自动化控制'
            }
          ],
          trainings: [
            {
              title: '智能制造技术专题培训',
              date: '2022.07',
              description: '为某机械制造企业开展智能制造技术专题培训，培训技术人员100余人次'
            },
            {
              title: '机械设计优化专题讲座',
              date: '2021.09',
              description: '为某高校师生开展机械设计优化专题讲座，培训师生60余人次'
            },
            {
              title: '自动化控制技术专题培训',
              date: '2021.02',
              description: '为某制造企业开展自动化控制技术专题培训，培训技术人员80余人次'
            }
          ]
        }
      ],
      currentExpert: {},
    };
  },
  created() {
    this.currentExpert = this.experts[0];
  },
  methods: {
    handleSearch() {
      if (!this.searchKeyword) {
        this.currentExpert = this.experts[0];
        return;
      }

      const expert = this.experts.find(item => item.name.includes(this.searchKeyword));

      if (expert) {
        this.currentExpert = expert;
      } else {
        this.$message.warning('未找到相关专家');
      }
    },

    getTagType(index) {
      const types = ['primary', 'success', 'warning', 'danger', 'info'];
      return types[index % types.length];
    }
  },
};
</script>

<style scoped>
.expert-profile {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

/* 搜索模块 */
.search-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  background-color: #ffffff;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-section .el-select {
  width: 250px;
  margin-right: 15px;
}

/* 专家基本信息卡片 */
.info-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: none;
}

.info-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 顶部信息模块 */
.top-info {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.avatar-name {
  display: flex;
  align-items: center;
}

.avatar {
  background-color: #409eff;
  color: #fff;
  font-size: 32px;
  margin-right: 20px;
}

.name-title h2 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.name-title p {
  margin: 0;
  font-size: 16px;
  color: #606266;
}

/* 基本信息模块 */
.basic-info {
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.work-hours {
  display: flex;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.info-icon {
  font-size: 20px;
  margin-right: 12px;
  color: #409eff;
  width: 24px;
  text-align: center;
}

.info-text .info-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 3px;
}

.info-text .info-value {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

/* 专长领域卡片 */
.specialty-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: none;
}

.specialty-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.specialty-card h3 {
  margin: 0;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.specialty-card h3 i {
  margin-right: 8px;
  color: #409eff;
}

.specialty-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 15px 0;
}

/* 项目经历和培训经历模块 */
.experience-section {
  display: flex;
  gap: 20px;
}

.project-experience,
.training-experience {
  flex: 1;
  min-width: 0;
}

.experience-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: none;
  height: 100%;
}

.experience-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.experience-card h3 {
  margin: 0;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.experience-card h3 i {
  margin-right: 8px;
  color: #409eff;
}

/* 时间轴样式 */
.el-timeline {
  padding-left: 5px;
  padding-top: 10px;
}

.el-timeline-item {
  padding-bottom: 10px;
}

.el-timeline-item__timestamp {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.timeline-card {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  box-shadow: none;
}

.timeline-card h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #303133;
}

.timeline-card p {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .experience-section {
    flex-direction: column;
  }

  .search-section .el-select {
    width: 200px;
  }

  .info-item {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .expert-profile {
    padding: 15px;
  }

  .search-section {
    flex-direction: column;
    align-items: stretch;
  }

  .search-section .el-select {
    width: 100%;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .top-info {
    flex-direction: column;
    text-align: center;
  }

  .avatar-name {
    flex-direction: column;
  }

  .avatar {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .basic-info .el-col {
    margin-bottom: 15px;
  }

  .info-item {
    justify-content: center;
  }

  .work-hours {
    justify-content: center;
  }
}
</style>