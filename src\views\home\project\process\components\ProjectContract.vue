<template>
<div class="project-contract-container">
  <el-card>
    <div slot="header">
      <span>📄 项目合同管理</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="addContract">
        新增合同
      </el-button>
    </div>

    <!-- 合同统计 -->
    <div class="contract-stats" style="margin-bottom: 30px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="合同总数" :value="contractList.length" suffix="份">
            <template slot="prefix">
              <i class="el-icon-document" style="color: #409EFF"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="已签署" :value="signedContractCount" suffix="份">
            <template slot="prefix">
              <i class="el-icon-circle-check" style="color: #67C23A"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="待签署" :value="pendingContractCount" suffix="份">
            <template slot="prefix">
              <i class="el-icon-time" style="color: #E6A23C"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="合同总额" :value="totalContractAmount" suffix="万元">
            <template slot="prefix">
              <i class="el-icon-money" style="color: #F56C6C"></i>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>

    <!-- 合同列表 -->
    <div class="contract-list">
      <el-table :data="contractList" style="width: 100%" empty-text="暂无合同数据">
        <el-table-column prop="contractNumber" label="合同编号" width="150"></el-table-column>
        <el-table-column prop="contractName" label="合同名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="contractType" label="合同类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getContractTypeTagType(scope.row.contractType)">
              {{ getContractTypeText(scope.row.contractType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="合同金额" width="120">
          <template slot-scope="scope">
            {{ scope.row.amount }}万元
          </template>
        </el-table-column>
        <el-table-column prop="signDate" label="签署日期" width="120"></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="mini" @click="viewContract(scope.row)">查看</el-button>
            <el-button size="mini" @click="editContract(scope.row)">编辑</el-button>
            <el-button size="mini" type="success" @click="downloadContract(scope.row)">下载</el-button>
            <el-button size="mini" type="danger" @click="deleteContract(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-if="contractList.length === 0" class="empty-state" style="text-align: center; padding: 40px;">
      <i class="el-icon-document" style="font-size: 64px; color: #C0C4CC;"></i>
      <p style="color: #909399; margin-top: 16px;">暂无合同数据</p>
      <el-button type="primary" @click="addContract">新增合同</el-button>
    </div>
  </el-card>

  <!-- 添加/编辑合同对话框 -->
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false">

    <el-form :model="formData" :rules="formRules" ref="contractForm" label-width="160px">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          合同基本信息
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同编号" prop="contractNumber">
              <el-input v-model="formData.contractNumber" placeholder="请输入合同编号" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同名称" prop="contractName">
              <el-input v-model="formData.contractName" placeholder="请输入合同名称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <h3 class="section-title">
          <i class="el-icon-user"></i>
          合同方信息
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="委托方信息" prop="clientInfo">
              <el-input v-model="formData.clientInfo" type="textarea" :rows="3" placeholder="请输入委托方详细信息（公司名称、联系人、联系方式等）"
                clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="受托方信息" prop="contractorInfo">
              <el-input v-model="formData.contractorInfo" type="textarea" :rows="3"
                placeholder="请输入受托方详细信息（公司名称、联系人、联系方式等）" clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <h3 class="section-title">
          <i class="el-icon-money"></i>
          合同条款
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同金额" prop="contractAmount">
              <el-input v-model="formData.contractAmount" placeholder="请输入合同金额" clearable>
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交付期限" prop="deliveryDeadline">
              <el-date-picker v-model="formData.deliveryDeadline" type="date" placeholder="选择交付期限" style="width: 100%"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <h3 class="section-title">
          <i class="el-icon-folder-opened"></i>
          交付物和附件
        </h3>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="交付物" prop="deliverables">
              <el-input v-model="formData.deliverables" type="textarea" :rows="3" placeholder="请详细描述交付物内容、规格、质量要求等"
                clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="附件（保密协议）" prop="confidentialityAgreement">
              <el-upload class="upload-demo" :action="uploadUrl" :on-success="handleConfidentialitySuccess"
                :on-remove="handleConfidentialityRemove" :file-list="confidentialityFileList" :limit="5"
                accept=".pdf,.doc,.docx">
                <el-button size="small" type="primary" icon="el-icon-upload">上传保密协议</el-button>
                <div slot="tip" class="el-upload__tip">支持 PDF、Word 格式，最多5个文件</div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件（合同文件、相关资料上传）" prop="contractDocuments">
              <el-upload class="upload-demo" :action="uploadUrl" :on-success="handleDocumentSuccess"
                :on-remove="handleDocumentRemove" :file-list="documentFileList" :limit="10"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.png">
                <el-button size="small" type="primary" icon="el-icon-upload">上传合同文件</el-button>
                <div slot="tip" class="el-upload__tip">支持多种格式，最多10个文件</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <h3 class="section-title">
          <i class="el-icon-edit-outline"></i>
          其他信息
        </h3>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" type="textarea" :rows="3" placeholder="请输入其他备注信息" clearable>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveContract">保存</el-button>
    </div>
  </el-dialog>

  <!-- 查看合同详情对话框 -->
  <el-dialog title="合同详情" :visible.sync="viewDialogVisible" width="70%">

    <el-descriptions v-if="currentContract" :column="2" border>
      <el-descriptions-item label="合同编号">{{ currentContract.contractNumber }}</el-descriptions-item>
      <el-descriptions-item label="合同名称">{{ currentContract.contractName }}</el-descriptions-item>
      <el-descriptions-item label="合同类型">
        <el-tag :type="getContractTypeTagType(currentContract.contractType)">
          {{ getContractTypeText(currentContract.contractType) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="合同金额">{{ currentContract.amount }}万元</el-descriptions-item>
      <el-descriptions-item label="甲方">{{ currentContract.partyA }}</el-descriptions-item>
      <el-descriptions-item label="乙方">{{ currentContract.partyB }}</el-descriptions-item>
      <el-descriptions-item label="签署日期">{{ currentContract.signDate }}</el-descriptions-item>
      <el-descriptions-item label="生效日期">{{ currentContract.effectiveDate }}</el-descriptions-item>
      <el-descriptions-item label="到期日期">{{ currentContract.expiryDate }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="getStatusTagType(currentContract.status)">
          {{ getStatusText(currentContract.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="合同内容" :span="2">
        <div style="white-space: pre-wrap;">{{ currentContract.content }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="付款条件" :span="2">
        <div style="white-space: pre-wrap;">{{ currentContract.paymentTerms }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="交付条件" :span="2">
        <div style="white-space: pre-wrap;">{{ currentContract.deliveryTerms }}</div>
      </el-descriptions-item>
      <el-descriptions-item v-if="currentContract.notes" label="备注" :span="2">
        <div style="white-space: pre-wrap;">{{ currentContract.notes }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <div slot="footer" class="dialog-footer">
      <el-button @click="viewDialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="editContract(currentContract)">编辑</el-button>
      <el-button type="success" @click="downloadContract(currentContract)">下载</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
export default {
  name: 'ProjectContract',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      viewDialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      editId: null,
      currentContract: null,
      contractList: [],
      formData: {
        contractNumber: '',           // 合同编号
        contractName: '',             // 合同名称
        clientInfo: '',               // 委托方信息
        contractorInfo: '',           // 受托方信息
        contractAmount: '',           // 合同金额
        deliveryDeadline: '',         // 交付期限
        deliverables: '',             // 交付物
        confidentialityAgreement: [], // 保密协议附件
        contractDocuments: [],        // 合同文件附件
        notes: ''                     // 备注
      },

      // 文件上传相关
      uploadUrl: '/api/upload', // 上传接口地址
      confidentialityFileList: [], // 保密协议文件列表
      documentFileList: [],        // 合同文件列表
      formRules: {
        contractNumber: [
          { required: true, message: '请输入合同编号', trigger: 'blur' },
          { min: 3, max: 50, message: '合同编号长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        contractName: [
          { required: true, message: '请输入合同名称', trigger: 'blur' },
          { min: 2, max: 100, message: '合同名称长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        clientInfo: [
          { required: true, message: '请输入委托方信息', trigger: 'blur' },
          { min: 10, message: '委托方信息至少需要10个字符', trigger: 'blur' }
        ],
        contractorInfo: [
          { required: true, message: '请输入受托方信息', trigger: 'blur' },
          { min: 10, message: '受托方信息至少需要10个字符', trigger: 'blur' }
        ],
        contractAmount: [
          { required: true, message: '请输入合同金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        deliveryDeadline: [
          { required: true, message: '请选择交付期限', trigger: 'change' }
        ],
        deliverables: [
          { required: true, message: '请输入交付物信息', trigger: 'blur' },
          { min: 10, message: '交付物描述至少需要10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    signedContractCount() {
      return this.contractList.filter(c => ['signed', 'effective'].includes(c.status)).length
    },
    pendingContractCount() {
      return this.contractList.filter(c => ['draft', 'pending'].includes(c.status)).length
    },
    totalContractAmount() {
      return this.contractList.reduce((total, contract) => {
        return total + (parseFloat(contract.amount) || 0)
      }, 0).toFixed(1)
    }
  },
  created() {
    this.loadContractData()
  },
  methods: {
    // 加载合同数据
    loadContractData() {
      // 这里应该从API获取合同数据
      this.contractList = []

      // 可以调用API获取数据
      // this.fetchContractData()
    },

    // 从API获取合同数据
    async fetchContractData() {
      try {
        // const response = await this.$api.getProjectContracts(this.projectInfo.id)
        // this.contractList = response.data

        console.log('获取合同数据:', this.projectInfo.id)
      } catch (error) {
        console.error('获取合同数据失败:', error)
        this.$message.error('获取合同数据失败')
      }
    },

    // 新增合同
    addContract() {
      this.dialogTitle = '新增合同'
      this.isEdit = false
      this.editId = null
      this.formData = {
        contractNumber: '',           // 合同编号
        contractName: '',             // 合同名称
        clientInfo: '',               // 委托方信息
        contractorInfo: '',           // 受托方信息
        contractAmount: '',           // 合同金额
        deliveryDeadline: '',         // 交付期限
        deliverables: '',             // 交付物
        confidentialityAgreement: [], // 保密协议附件
        contractDocuments: [],        // 合同文件附件
        notes: ''                     // 备注
      }
      // 重置文件列表
      this.confidentialityFileList = []
      this.documentFileList = []
      this.dialogVisible = true
      this.viewDialogVisible = false
    },

    // 编辑合同
    editContract(contract) {
      this.dialogTitle = '编辑合同'
      this.isEdit = true
      this.editId = contract.id
      this.formData = { ...contract }
      this.dialogVisible = true
      this.viewDialogVisible = false
    },

    // 查看合同
    viewContract(contract) {
      this.currentContract = contract
      this.viewDialogVisible = true
    },

    // 保存合同
    saveContract() {
      this.$refs.contractForm.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            // 更新合同
            const index = this.contractList.findIndex(c => c.id === this.editId)
            if (index !== -1) {
              this.contractList.splice(index, 1, { ...this.formData, id: this.editId })
            }
            this.$message.success('合同更新成功')
          } else {
            // 添加新合同
            const newContract = {
              ...this.formData,
              id: Date.now().toString()
            }
            this.contractList.push(newContract)
            this.$message.success('合同添加成功')
          }
          this.dialogVisible = false
        }
      })
    },

    // 下载合同
    downloadContract(contract) {
      // 这里实现合同下载功能
      this.$message.success(`正在下载合同：${contract.contractName}`)
    },

    // 保密协议文件上传成功
    handleConfidentialitySuccess(response, file, fileList) {
      this.confidentialityFileList = fileList
      this.formData.confidentialityAgreement = fileList.map(f => ({
        name: f.name,
        url: f.response?.url || f.url
      }))
      this.$message.success('保密协议上传成功')
    },

    // 保密协议文件移除
    handleConfidentialityRemove(file, fileList) {
      this.confidentialityFileList = fileList
      this.formData.confidentialityAgreement = fileList.map(f => ({
        name: f.name,
        url: f.response?.url || f.url
      }))
    },

    // 合同文件上传成功
    handleDocumentSuccess(response, file, fileList) {
      this.documentFileList = fileList
      this.formData.contractDocuments = fileList.map(f => ({
        name: f.name,
        url: f.response?.url || f.url
      }))
      this.$message.success('合同文件上传成功')
    },

    // 合同文件移除
    handleDocumentRemove(file, fileList) {
      this.documentFileList = fileList
      this.formData.contractDocuments = fileList.map(f => ({
        name: f.name,
        url: f.response?.url || f.url
      }))
    },

    // 删除合同
    deleteContract(contract) {
      this.$confirm('确认删除该合同吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.contractList.findIndex(c => c.id === contract.id)
        if (index !== -1) {
          this.contractList.splice(index, 1)
          this.$message.success('合同删除成功')
        }
      })
    },

    // 获取合同类型标签类型
    getContractTypeTagType(type) {
      const typeMap = {
        'development': 'primary',
        'service': 'success',
        'transfer': 'warning',
        'consulting': 'info',
        'research': 'danger',
        'other': ''
      }
      return typeMap[type] || ''
    },

    // 获取合同类型文本
    getContractTypeText(type) {
      const textMap = {
        'development': '技术开发合同',
        'service': '技术服务合同',
        'transfer': '技术转让合同',
        'consulting': '技术咨询合同',
        'research': '委托研发合同',
        'other': '其他'
      }
      return textMap[type] || '未知'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'draft': 'info',
        'pending': 'warning',
        'signed': 'success',
        'effective': 'success',
        'expired': 'danger',
        'terminated': 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'draft': '草稿',
        'pending': '待签署',
        'signed': '已签署',
        'effective': '已生效',
        'expired': '已到期',
        'terminated': '已终止'
      }
      return textMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
.project-contract-container {
  height: 100%;
}

.contract-stats {
  margin-bottom: 30px;
}

.contract-list {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  text-align: right;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 2px solid #e1e8ed;
  padding-bottom: 10px;
}

.section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

/* 上传组件样式 */
.upload-demo {
  width: 100%;
}

.upload-demo .el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-section {
    padding: 15px;
    margin-bottom: 20px;
  }
}
</style>
