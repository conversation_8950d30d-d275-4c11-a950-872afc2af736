<template>
<div class="project-research-container">
  <el-card>
    <div slot="header">
      <span>🔍 项目调研管理</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="addResearch">
        新增调研
      </el-button>
    </div>

    <!-- 调研统计 -->
    <div class="research-stats" style="margin-bottom: 30px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总调研数" :value="researchList.length" suffix="项">
            <template slot="prefix">
              <i class="el-icon-search" style="color: #409EFF"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="已完成" :value="completedResearchCount" suffix="项">
            <template slot="prefix">
              <i class="el-icon-circle-check" style="color: #67C23A"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="进行中" :value="inProgressResearchCount" suffix="项">
            <template slot="prefix">
              <i class="el-icon-loading" style="color: #E6A23C"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="完成率" :value="researchCompletionRate" suffix="%">
            <template slot="prefix">
              <i class="el-icon-pie-chart" style="color: #F56C6C"></i>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>

    <!-- 调研列表 -->
    <div class="research-list">
      <el-table :data="researchList" border style="width: 100%" empty-text="暂无调研数据">
        <el-table-column prop="researchObject" label="调研对象" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="researchMethods" label="调研方法" width="120">
          <template slot-scope="scope">
            <el-tag size="small" :type="getMethodTagType(scope.row.researchMethods)">
              {{ getMethodText(scope.row.researchMethods) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="researchPurposeContent" label="调研目的和内容" width="200"
          show-overflow-tooltip></el-table-column>
        <el-table-column label="调研时间" width="180">
          <template slot-scope="scope">
            <div>{{ scope.row.researchTime }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="researchTeam" label="调研团队人员" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="feedbackForms" label="调研成果形式" width="140">
          <template slot-scope="scope">
            <el-tag size="small" type="info" style="margin-right: 5px;">
              {{ getFeedbackFormText(scope.row.feedbackForms) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" class-name="operation-column">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button size="mini" @click="viewResearch(scope.row)">查看</el-button>
              <el-button size="mini" @click="editResearch(scope.row)">编辑</el-button>
              <el-button size="mini" type="danger" @click="deleteResearch(scope.row)">删除</el-button>
              <el-button size="mini" type="success" @click="addResearchRes(scope.row)">调研成果</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-if="researchList.length === 0" class="empty-state" style="text-align: center; padding: 40px;">
      <i class="el-icon-search" style="font-size: 64px; color: #C0C4CC;"></i>
      <p style="color: #909399; margin-top: 16px;">暂无调研数据</p>
      <el-button type="primary" @click="addResearch">新增调研</el-button>
    </div>
  </el-card>

  <!-- 调研报告管理 -->
  <el-card style="margin-top: 20px;">
    <div slot="header">
      <span>📋 调研报告管理</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="addReport">
        新增报告
      </el-button>
    </div>

    <!-- 报告统计 -->
    <div class="report-stats" style="margin-bottom: 30px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总报告数" :value="reportList.length" suffix="份">
            <template slot="prefix">
              <i class="el-icon-document" style="color: #409EFF"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="已完成" :value="completedReports" suffix="份">
            <template slot="prefix">
              <i class="el-icon-circle-check" style="color: #67C23A"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="进行中" :value="inProgressReports" suffix="份">
            <template slot="prefix">
              <i class="el-icon-loading" style="color: #E6A23C"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="待开始" :value="pendingReports" suffix="份">
            <template slot="prefix">
              <i class="el-icon-time" style="color: #909399"></i>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>

    <!-- 报告列表 -->
    <div class="report-list">
      <el-table :data="reportList" border style="width: 100%" empty-text="暂无报告数据" header-align="center">
        <el-table-column prop="title" align="left" label="报告标题" width="auto" show-overflow-tooltip></el-table-column>
        <el-table-column prop="researchObject" align="left" label="调研对象" width="auto"
          show-overflow-tooltip></el-table-column>
        <el-table-column prop="feedbackForm" align="left" label="报告类型" width="120">
          <template slot-scope="scope">
            <el-tag size="small" :type="getReportTypeTagType(scope.row.feedbackForm)">
              {{ getFeedbackFormText(scope.row.feedbackForm) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" align="left" label="创建时间" width="auto"
          show-overflow-tooltip></el-table-column>
        <el-table-column prop="status" align="left" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag size="small" :type="getReportStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="auto" class-name="operation-column">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button size="mini" @click="viewReport(scope.row)">查看</el-button>
              <el-button size="mini" @click="editReport(scope.row)">编辑</el-button>
              <el-button size="mini" type="danger" @click="deleteReport(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-if="reportList.length === 0" class="empty-state" style="text-align: center; padding: 40px;">
      <i class="el-icon-document" style="font-size: 64px; color: #C0C4CC;"></i>
      <p style="color: #909399; margin-top: 16px;">暂无报告数据</p>
      <el-button type="primary" @click="addReport">新增报告</el-button>
    </div>
  </el-card>



  <!-- 添加/编辑调研对话框 -->
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false">

    <el-form :model="formData" :rules="formRules" ref="researchForm" label-width="140px">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          基本信息
        </h3>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="调研对象" prop="researchObject">
              <el-input v-model="formData.researchObject" placeholder="请输入调研对象" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调研地点" prop="researchLocation">
              <el-input v-model="formData.researchLocation" placeholder="请输入调研地点" clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="调研目的和内容" prop="researchPurposeContent">
          <el-input v-model="formData.researchPurposeContent" type="textarea" :rows="3" placeholder="请输入调研目的和内容"
            clearable></el-input>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="调研团队人员" prop="researchTeam">
              <el-input v-model="formData.researchTeam" type="textarea" :rows="2" placeholder="请输入调研团队人员信息，多人请用逗号分隔"
                clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <h3 class="section-title">
          <i class="el-icon-time"></i>
          调研方法与时间
        </h3>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="调研方法" prop="researchMethods">
              <el-radio-group v-model="formData.researchMethods">
                <el-radio label="literature">文献检索</el-radio>
                <el-radio label="questionnaire">问卷调研</el-radio>
                <el-radio label="interview">访谈</el-radio>
                <el-radio label="fieldwork">实地考察</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调研时间" prop="researchTime">
              <el-date-picker v-model="formData.researchTime" type="datetime" placeholder="选择调研时间"
                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>



        <h3 class="section-title">
          <i class="el-icon-warning"></i>
          调研要求
        </h3>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="调研要求" prop="researchRequirements">
              <el-radio-group v-model="formData.researchRequirements">
                <el-radio label="confidentiality">保密协议</el-radio>
                <el-radio label="signature">受访人签字</el-radio>
                <el-radio label="none">无特殊要求</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成果反馈形式" prop="feedbackForms">
              <el-radio-group v-model="formData.feedbackForms">
                <el-radio label="questionnaire">问卷反馈</el-radio>
                <el-radio label="interview_record">访谈记录</el-radio>
                <el-radio label="research_report">调研报告</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <h3 class="section-title">
          <i class="el-icon-more"></i>
          其他信息
        </h3>

        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" type="textarea" :rows="3" placeholder="其他备注信息" clearable>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveResearch">保存</el-button>
    </div>
  </el-dialog>

  <!-- 查看调研详情对话框 -->
  <el-dialog title="调研详情" :visible.sync="viewDialogVisible" width="60%">

    <el-descriptions v-if="currentResearch" :column="2" border>
      <el-descriptions-item label="调研对象" :span="2">{{ currentResearch.researchObject }}</el-descriptions-item>
      <el-descriptions-item label="调研地点">{{ currentResearch.researchLocation }}</el-descriptions-item>
      <el-descriptions-item label="调研时间">{{ currentResearch.researchTime }}</el-descriptions-item>
      <el-descriptions-item label="调研方法" :span="2">
        <el-tag v-if="currentResearch.researchMethods" size="small"
          :type="getMethodTagType(currentResearch.researchMethods)">
          {{ getMethodText(currentResearch.researchMethods) }}
        </el-tag>
        <span v-else>未设置</span>
      </el-descriptions-item>
      <el-descriptions-item label="调研团队人员" :span="2">
        <div style="white-space: pre-wrap;">{{ currentResearch.researchTeam }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="调研目的和内容" :span="2">
        <div style="white-space: pre-wrap;">{{ currentResearch.researchPurposeContent }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="调研要求">
        <el-tag v-if="currentResearch.researchRequirements && currentResearch.researchRequirements !== 'none'"
          size="small" type="warning">
          {{ getRequirementText(currentResearch.researchRequirements) }}
        </el-tag>
        <span v-else>无特殊要求</span>
      </el-descriptions-item>
      <el-descriptions-item label="成果反馈形式">
        <el-tag v-if="currentResearch.feedbackForms" size="small" type="success">
          {{ getFeedbackFormText(currentResearch.feedbackForms) }}
        </el-tag>
        <span v-else>未设置</span>
      </el-descriptions-item>
      <el-descriptions-item v-if="currentResearch.notes" label="备注" :span="2">
        <div style="white-space: pre-wrap;">{{ currentResearch.notes }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <div slot="footer" class="dialog-footer">
      <el-button @click="viewDialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="editResearch(currentResearch)">编辑</el-button>
    </div>
  </el-dialog>

  <!-- 新增调研成果 -->
  <el-dialog title="新增调研成果" :visible.sync="viewResDialogVisible" width="60%">

    <el-form ref="addResearchForm" :model="addResearchForm">
      <el-form-item label="调研主题" prop="title">
        <el-select v-model="addResearchForm.title" placeholder="请输入调研主题">
          <el-option v-for="item in researchResList" :key="item.title" :label="item.title" :value="item.title">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="调研结果" prop="result">
        <el-select v-model="addResearchForm.result" placeholder="请输入调研主题">
          <el-option label="是" value="1">
          </el-option>
          <el-option label="否" value="2">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传附件">
        <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/" :on-preview="handlePreview"
          :on-remove="handleRemove" :before-remove="beforeRemove" :limit="1" :on-exceed="handleExceed"
          :file-list="fileList" accept=".pdf,.doc,.docx,.jpg,.png">
          <el-button size="small" type="primary">
            <i class="el-icon-upload2"></i> 点击上传
          </el-button>
          <div slot="tip" class="el-upload__tip">
            支持上传PDF、Word、图片格式文件，单个文件不超过10MB，仅支持上传一个文件
          </div>
        </el-upload>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="viewResDialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="viewResDialogVisible = false">编辑</el-button>
    </div>
  </el-dialog>


  <!-- 新增调研成果 -->
  <el-dialog title="新增调研成果" :visible.sync="viewResDialogVisible" width="60%">

    <el-form ref="addResearchForm" :model="addResearchForm">
      <el-form-item label="调研主题" prop="title">
        <el-select v-model="addResearchForm.title" placeholder="请输入调研主题">
          <el-option v-for="item in researchResList" :key="item.title" :label="item.title" :value="item.title">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="调研成果" prop="content">
        <el-input v-model="addResearchForm.content" type="textarea" :rows="4" placeholder="请输入调研成果"></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="viewResDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="viewResDialogVisible = false">保存</el-button>
    </div>
  </el-dialog>

  <!-- 新增/编辑调研报告对话框 -->
  <el-dialog :title="reportDialogTitle" :visible.sync="reportDialogVisible" width="70%">
    <el-form ref="reportForm" :model="reportFormData" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报告标题" prop="title">
            <el-input v-model="reportFormData.title" placeholder="请输入报告标题"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调研对象" prop="researchObject">
            <el-input v-model="reportFormData.researchObject" placeholder="请输入调研对象"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="报告类型" prop="feedbackForm">
        <el-radio-group v-model="reportFormData.feedbackForm">
          <el-radio label="questionnaire">问卷反馈</el-radio>
          <el-radio label="interview_record">访谈记录</el-radio>
          <el-radio label="research_report">调研报告</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="报告内容" prop="content">
        <el-input v-model="reportFormData.content" type="textarea" :rows="6" placeholder="请输入详细的调研报告内容"></el-input>
      </el-form-item>

      <el-form-item label="调研结论" prop="conclusion">
        <el-input v-model="reportFormData.conclusion" type="textarea" :rows="3" placeholder="请输入调研结论"></el-input>
      </el-form-item>

      <el-form-item label="建议措施" prop="suggestions">
        <el-input v-model="reportFormData.suggestions" type="textarea" :rows="3" placeholder="请输入建议措施"></el-input>
      </el-form-item>

      <el-form-item label="报告状态" prop="status">
        <el-radio-group v-model="reportFormData.status">
          <el-radio label="draft">草稿</el-radio>
          <el-radio label="in-progress">进行中</el-radio>
          <el-radio label="completed">已完成</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="reportDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveReport">保存</el-button>
    </div>
  </el-dialog>

  <!-- 查看调研报告详情对话框 -->
  <el-dialog title="调研报告详情" :visible.sync="viewReportDialogVisible" width="70%">
    <el-descriptions v-if="currentReport" :column="2" border>
      <el-descriptions-item label="报告标题" :span="2">{{ currentReport.title }}</el-descriptions-item>
      <el-descriptions-item label="调研对象">{{ currentReport.researchObject }}</el-descriptions-item>
      <el-descriptions-item label="报告类型">
        <el-tag size="small" :type="getReportTypeTagType(currentReport.feedbackForm)">
          {{ getFeedbackFormText(currentReport.feedbackForm) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ currentReport.createTime }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag size="small" :type="getReportStatusTagType(currentReport.status)">
          {{ getStatusText(currentReport.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="报告内容" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.content }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="调研结论" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.conclusion }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="建议措施" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.suggestions }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <div slot="footer" class="dialog-footer">
      <el-button @click="viewReportDialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="editReport(currentReport)">编辑</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
export default {
  name: 'ProjectResearch',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      fileList: [{ name: 'food.jpeg', url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100' }, { name: 'food2.jpeg', url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100' }]
      , addResearchForm: {
        title: '',
        result: ''
      },
      researchResList: [
        {
          id: 1,
          title: '问卷反馈'
        },
        {
          id: 2,
          title: '访谈记录'
        },
        {
          id: 3,
          title: '调研报告'
        }
      ],
      dialogVisible: false,
      viewDialogVisible: false,
      dialogTitle: '',
      viewResDialogVisible: false,
      isEdit: false,
      editId: null,
      currentResearch: null,
      researchList: [],

      // 调研报告相关数据
      reportList: [],
      reportDialogVisible: false,
      reportDialogTitle: '',
      isReportEdit: false,
      editReportId: null,
      currentReport: null,
      viewReportDialogVisible: false,

      // 报告表单数据
      reportFormData: {
        title: '',              // 报告标题
        researchObject: '',     // 调研对象
        feedbackForm: '',       // 报告类型
        content: '',            // 报告内容
        conclusion: '',         // 调研结论
        suggestions: '',        // 建议措施
        attachments: [],        // 附件
        status: 'draft'         // 状态
      },
      formData: {
        researchObject: '',           // 调研对象
        researchMethods: '',          // 调研方法（单选）
        researchPurposeContent: '',   // 调研目的和内容
        researchTime: '',             // 调研时间（精确到时分秒）
        researchLocation: '',         // 调研地点
        researchTeam: '',             // 调研团队人员
        researchRequirements: '',     // 调研要求（单选）
        feedbackForms: '',            // 调研成果反馈形式（单选）
        notes: ''                     // 备注
      },
      formRules: {
        researchObject: [
          { required: true, message: '请输入调研对象', trigger: 'blur' }
        ],
        researchMethods: [
          { required: true, message: '请选择调研方法', trigger: 'change' }
        ],
        researchPurposeContent: [
          { required: true, message: '请输入调研目的和内容', trigger: 'blur' },
          { min: 10, message: '调研目的和内容至少需要10个字符', trigger: 'blur' }
        ],
        researchTime: [
          { required: true, message: '请选择调研时间', trigger: 'change' }
        ],
        researchLocation: [
          { required: true, message: '请输入调研地点', trigger: 'blur' }
        ],
        researchTeam: [
          { required: true, message: '请输入调研团队人员', trigger: 'blur' }
        ],
        feedbackForms: [
          { required: true, type: 'array', min: 1, message: '请至少选择一种成果反馈形式', trigger: 'change' }
        ]
      },


    }
  },
  computed: {
    completedResearchCount() {
      return this.researchList.filter(r => r.status === 'completed').length
    },
    inProgressResearchCount() {
      return this.researchList.filter(r => r.status === 'in-progress').length
    },
    researchCompletionRate() {
      if (this.researchList.length === 0) return 0
      return Math.round((this.completedResearchCount / this.researchList.length) * 100)
    },

    // 报告统计
    completedReports() {
      return this.reportList.filter(item => item.status === 'completed').length
    },
    inProgressReports() {
      return this.reportList.filter(item => item.status === 'in-progress').length
    },
    pendingReports() {
      return this.reportList.filter(item => item.status === 'pending' || item.status === 'draft').length
    }
  },
  created() {
    this.loadResearchData()
  },
  methods: {
    // 加载调研数据
    loadResearchData() {
      // 这里应该从API获取调研数据
      // 临时添加测试数据
      this.researchList = [
        {
          id: '1',
          researchObject: '目标用户群体',
          researchMethods: 'questionnaire',
          researchPurposeContent: '了解目标用户的需求和使用习惯，为产品设计提供依据',
          researchTime: '2024-01-15 09:00:00',
          researchLocation: '北京市朝阳区',
          researchTeam: '张三, 李四, 王五',
          researchRequirements: 'confidentiality',
          feedbackForms: 'questionnaire',
          status: 'completed'
        },
        {
          id: '2',
          researchObject: '竞争对手产品',
          researchMethods: 'literature',
          researchPurposeContent: '分析竞争对手的产品特点和市场策略，找出差异化优势',
          researchTime: '2024-01-20 14:00:00',
          researchLocation: '上海市浦东新区',
          researchTeam: '李四, 赵六',
          researchRequirements: 'confidentiality',
          feedbackForms: 'research_report',
          status: 'in-progress'
        },
        {
          id: '3',
          researchObject: '技术可行性',
          researchMethods: 'interview',
          researchPurposeContent: '评估新技术的可行性和实施难度，制定技术方案',
          researchTime: '2024-01-25 10:30:00',
          researchLocation: '深圳市南山区',
          researchTeam: '王五, 张三',
          researchRequirements: 'signature',
          feedbackForms: 'interview_record',
          status: 'pending'
        },
        {
          id: '4',
          researchObject: '市场需求',
          researchMethods: 'fieldwork',
          researchPurposeContent: '调研市场对新产品的需求程度和接受度，预测市场前景',
          researchTime: '2024-02-01 16:00:00',
          researchLocation: '广州市天河区',
          researchTeam: '赵六, 李四, 张三',
          researchRequirements: 'none',
          feedbackForms: 'research_report',
          status: 'pending'
        }
      ]

      // 加载报告测试数据
      this.reportList = [
        {
          id: '1',
          title: '目标用户群体调研报告',
          researchObject: '目标用户群体',
          feedbackForm: 'questionnaire',
          content: '通过问卷调研的方式，深入了解目标用户的需求和使用习惯...',
          conclusion: '目标用户主要集中在25-35岁年龄段，对产品功能有明确需求',
          suggestions: '建议优化用户界面设计，增加个性化功能',
          createTime: '2024-01-16 10:00:00',
          status: 'completed',
          attachments: []
        },
        {
          id: '2',
          title: '竞争对手产品分析报告',
          researchObject: '竞争对手产品',
          feedbackForm: 'research_report',
          content: '通过文献检索和实地考察，分析竞争对手的产品特点...',
          conclusion: '竞争对手在技术创新方面领先，但用户体验有待提升',
          suggestions: '建议加强技术研发投入，同时注重用户体验优化',
          createTime: '2024-01-21 14:30:00',
          status: 'in-progress',
          attachments: []
        },
        {
          id: '3',
          title: '技术可行性调研报告',
          researchObject: '技术可行性',
          feedbackForm: 'interview_record',
          content: '通过专家访谈的方式，评估新技术的可行性和实施难度...',
          conclusion: '技术方案可行，但需要投入较多资源',
          suggestions: '建议分阶段实施，优先开发核心功能',
          createTime: '2024-01-26 09:15:00',
          status: 'draft',
          attachments: []
        }
      ]

      // 可以调用API获取数据
      // this.fetchResearchData()
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    handleExceed() {
      this.$message.warning('只能上传一个文件，如需更换请先删除当前文件');
    },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    // 从API获取调研数据
    async fetchResearchData() {
      try {
        // const response = await this.$api.getProjectResearch(this.projectInfo.id)
        // this.researchList = response.data

        console.log('获取调研数据:', this.projectInfo.id)
      } catch (error) {
        console.error('获取调研数据失败:', error)
        this.$message.error('获取调研数据失败')
      }
    },

    // 新增调研
    addResearch() {
      this.dialogTitle = '新增调研'
      this.isEdit = false
      this.editId = null
      this.formData = {
        researchObject: '',           // 调研对象
        researchMethods: '',          // 调研方法（单选）
        researchPurposeContent: '',   // 调研目的和内容
        researchTime: '',             // 调研时间（精确到时分秒）
        researchLocation: '',         // 调研地点
        researchTeam: '',             // 调研团队人员
        researchRequirements: '',     // 调研要求（单选）
        feedbackForms: '',            // 调研成果反馈形式（单选）
        notes: ''                     // 备注
      }
      this.dialogVisible = true
      this.viewDialogVisible = false
    },

    // 编辑调研
    editResearch(research) {
      this.dialogTitle = '编辑调研'
      this.isEdit = true
      this.editId = research.id
      this.formData = { ...research }
      this.dialogVisible = true
      this.viewDialogVisible = false
    },

    // 查看调研
    viewResearch(research) {
      this.currentResearch = research
      this.viewDialogVisible = true
    },
    // 新增调研成果
    addResearchRes() {
      this.viewResDialogVisible = true
    },
    // 保存调研
    saveResearch() {
      this.$refs.researchForm.validate((valid) => {
        if (valid) {
          // 将表单数据映射到表格显示格式
          const mappedData = {
            id: this.isEdit ? this.editId : Date.now().toString(),
            title: this.formData.researchObject || '未设置标题', // 使用调研对象作为标题
            type: this.formData.researchMethods && this.formData.researchMethods.length > 0
              ? this.mapMethodToType(this.formData.researchMethods[0]) // 将调研方法映射为表格类型
              : 'other', // 默认类型
            assignee: this.formData.researchTeam ?
              this.formData.researchTeam.split(/[,，\s]+/)[0] // 取团队人员的第一个作为负责人
              : '未指定',
            startDate: this.formData.researchTime ?
              this.formData.researchTime.split(' ')[0] // 取时间的日期部分
              : new Date().toISOString().split('T')[0],
            endDate: this.formData.researchTime ?
              this.formData.researchTime.split(' ')[0] // 暂时使用相同日期，实际应该有结束时间字段
              : new Date().toISOString().split('T')[0],
            status: 'pending', // 新增的调研默认为待开始状态
            // 保留原始表单数据
            ...this.formData
          }

          if (this.isEdit) {
            // 更新调研
            const index = this.researchList.findIndex(r => r.id === this.editId)
            if (index !== -1) {
              this.researchList.splice(index, 1, mappedData)
            }
            this.$message.success('调研更新成功')
          } else {
            // 添加新调研
            this.researchList.push(mappedData)
            this.$message.success('调研添加成功')
          }
          this.dialogVisible = false
        }
      })
    },

    // 删除调研
    deleteResearch(research) {
      this.$confirm('确认删除该调研吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.researchList.findIndex(r => r.id === research.id)
        if (index !== -1) {
          this.researchList.splice(index, 1)
          this.$message.success('调研删除成功')
        }
      })
    },

    // 获取调研类型标签类型
    getResearchTypeTagType(type) {
      const typeMap = {
        'market': 'success',
        'technical': 'primary',
        'user': 'warning',
        'competitor': 'danger',
        'requirement': 'info',
        'other': ''
      }
      return typeMap[type] || ''
    },

    // 获取调研类型文本
    getResearchTypeText(type) {
      const textMap = {
        'market': '市场调研',
        'technical': '技术调研',
        'user': '用户调研',
        'competitor': '竞品调研',
        'requirement': '需求调研',
        'other': '其他'
      }
      return textMap[type] || '未知'
    },

    // 获取调研方法文本
    getMethodText(method) {
      const methodMap = {
        'literature': '文献检索',
        'questionnaire': '问卷调研',
        'interview': '访谈',
        'fieldwork': '实地考察'
      }
      return methodMap[method] || method
    },

    // 将调研方法映射为表格类型
    mapMethodToType(method) {
      const methodToTypeMap = {
        'literature': 'technical',    // 文献检索 -> 技术调研
        'questionnaire': 'market',    // 问卷调研 -> 市场调研
        'interview': 'user',          // 访谈 -> 用户调研
        'fieldwork': 'requirement'    // 实地考察 -> 需求调研
      }
      return methodToTypeMap[method] || 'other'
    },

    // 获取调研方法标签类型
    getMethodTagType(method) {
      const typeMap = {
        'literature': 'primary',
        'questionnaire': 'success',
        'interview': 'warning',
        'fieldwork': 'info'
      }
      return typeMap[method] || 'default'
    },

    // 获取调研要求文本
    getRequirementText(requirement) {
      const requirementMap = {
        'confidentiality': '保密协议',
        'signature': '受访人签字',
        'none': '无特殊要求'
      }
      return requirementMap[requirement] || requirement
    },

    // 获取调研成果反馈形式文本
    getFeedbackFormText(form) {
      const formMap = {
        'questionnaire': '问卷反馈',
        'interview_record': '访谈记录',
        'research_report': '调研报告'
      }
      return formMap[form] || form
    },

    // 获取报告类型标签类型
    getReportTypeTagType(type) {
      const typeMap = {
        'questionnaire': 'success',
        'interview_record': 'warning',
        'research_report': 'primary'
      }
      return typeMap[type] || 'default'
    },

    // 获取报告状态标签类型
    getReportStatusTagType(status) {
      const statusMap = {
        'draft': 'info',
        'in-progress': 'warning',
        'completed': 'success',
        'pending': 'default'
      }
      return statusMap[status] || 'default'
    },

    // 新增报告
    addReport() {
      this.reportDialogTitle = '新增调研报告'
      this.isReportEdit = false
      this.editReportId = null
      this.reportFormData = {
        title: '',
        researchObject: '',
        feedbackForm: '',
        content: '',
        conclusion: '',
        suggestions: '',
        attachments: [],
        status: 'draft'
      }
      this.reportDialogVisible = true
    },

    // 查看报告
    viewReport(report) {
      this.currentReport = report
      this.viewReportDialogVisible = true
    },

    // 编辑报告
    editReport(report) {
      this.reportDialogTitle = '编辑调研报告'
      this.isReportEdit = true
      this.editReportId = report.id
      this.reportFormData = { ...report }
      this.reportDialogVisible = true
    },

    // 删除报告
    deleteReport(report) {
      this.$confirm('确定要删除这份调研报告吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.reportList.findIndex(r => r.id === report.id)
        if (index !== -1) {
          this.reportList.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 保存报告
    saveReport() {
      this.$refs.reportForm.validate((valid) => {
        if (valid) {
          const reportData = {
            ...this.reportFormData,
            createTime: this.isReportEdit ? this.reportFormData.createTime : new Date().toLocaleString()
          }

          if (this.isReportEdit) {
            // 更新报告
            const index = this.reportList.findIndex(r => r.id === this.editReportId)
            if (index !== -1) {
              this.reportList.splice(index, 1, { ...reportData, id: this.editReportId })
            }
            this.$message.success('报告更新成功')
          } else {
            // 新增报告
            reportData.id = Date.now().toString()
            this.reportList.push(reportData)
            this.$message.success('报告创建成功')
          }

          this.reportDialogVisible = false
        }
      })
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'planning': 'info',
        'in-progress': 'warning',
        'completed': 'success',
        'paused': 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'planning': '计划中',
        'in-progress': '进行中',
        'completed': '已完成',
        'paused': '已暂停',
        'draft': '草稿',
        'reviewing': '审批中',
        'approved': '已通过',
        'rejected': '已拒绝'
      }
      return textMap[status] || '未知'
    },
  }
}
</script>

<style scoped>
.project-research-container {
  height: 100%;
}

.research-stats {
  margin-bottom: 20px;
}

.research-list {
  margin-bottom: 20px;
}

.research-list .el-table {
  border: 1px solid #EBEEF5;
}

.research-list .el-table th {
  background-color: #F5F7FA;
}

.research-list .el-table td,
.research-list .el-table th {
  padding: 12px 0;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  text-align: right;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 2px solid #e1e8ed;
  padding-bottom: 10px;
}

.section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

/* 复选框组样式 */
.el-checkbox-group .el-checkbox {
  margin-right: 15px;
  margin-bottom: 10px;
}

.el-checkbox-group .el-checkbox:last-child {
  margin-right: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-section {
    padding: 15px;
    margin-bottom: 20px;
  }

  .el-checkbox-group .el-checkbox {
    display: block;
    margin-right: 0;
    margin-bottom: 8px;
  }
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 研究报告样式 */
.report-stats {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.table-container {
  margin-top: 20px;
}

/* 审批流程预览样式 */
.process-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.process-preview h4 {
  margin-bottom: 15px;
  color: #495057;
  font-size: 14px;
}

/* 审批进度样式 */
.el-steps {
  margin: 20px 0;
}

.el-step__description {
  padding-right: 10px;
  line-height: 1.4;
}

/* 操作按钮样式 - 防止换行 */
.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  flex-wrap: nowrap;
}

.operation-buttons .el-button {
  margin: 0;
  padding: 5px 8px;
  font-size: 12px;
  min-width: auto;
}

/* 操作列样式 */
.project-research-container>>>.operation-column .cell {
  padding: 0 4px;
  overflow: visible;
}
</style>
