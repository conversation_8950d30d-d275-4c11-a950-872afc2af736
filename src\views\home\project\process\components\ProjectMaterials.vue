<template>
  <div class="project-materials-container">
    <el-card>
      <div slot="header">
        <span>📁 立项材料管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="uploadMaterial">
          上传材料
        </el-button>
      </div>

      <!-- 材料统计 -->
      <div class="materials-stats" style="margin-bottom: 30px;">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="材料总数" :value="materialList.length" suffix="份">
              <template slot="prefix">
                <i class="el-icon-folder" style="color: #409EFF"></i>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="已审核" :value="approvedMaterialCount" suffix="份">
              <template slot="prefix">
                <i class="el-icon-circle-check" style="color: #67C23A"></i>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="待审核" :value="pendingMaterialCount" suffix="份">
              <template slot="prefix">
                <i class="el-icon-time" style="color: #E6A23C"></i>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="总大小" :value="totalFileSize" suffix="MB">
              <template slot="prefix">
                <i class="el-icon-files" style="color: #F56C6C"></i>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
      </div>

      <!-- 材料分类标签 -->
      <div class="material-categories" style="margin-bottom: 20px;">
        <el-tag v-for="category in materialCategories" :key="category.value"
          :type="selectedCategory === category.value ? 'primary' : 'info'" style="margin-right: 10px; cursor: pointer;"
          @click="filterByCategory(category.value)">
          {{ category.label }} ({{ getCategoryCount(category.value) }})
        </el-tag>
      </div>

      <!-- 材料列表 -->
      <div class="materials-list">
        <el-table :data="filteredMaterialList" style="width: 100%" empty-text="暂无材料数据">
          <el-table-column prop="fileName" label="文件名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="category" label="材料类型" width="120">
            <template slot-scope="scope">
              <el-tag :type="getCategoryTagType(scope.row.category)">
                {{ getCategoryText(scope.row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="fileSize" label="文件大小" width="100">
            <template slot-scope="scope">
              {{ formatFileSize(scope.row.fileSize) }}
            </template>
          </el-table-column>
          <el-table-column prop="uploadDate" label="上传日期" width="120"></el-table-column>
          <el-table-column prop="uploader" label="上传人" width="100"></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250">
            <template slot-scope="scope">
              <el-button size="mini" @click="previewMaterial(scope.row)">预览</el-button>
              <el-button size="mini" type="success" @click="downloadMaterial(scope.row)">下载</el-button>
              <el-button size="mini" @click="editMaterial(scope.row)">编辑</el-button>
              <el-button size="mini" type="danger" @click="deleteMaterial(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <div v-if="materialList.length === 0" class="empty-state" style="text-align: center; padding: 40px;">
        <i class="el-icon-folder-add" style="font-size: 64px; color: #C0C4CC;"></i>
        <p style="color: #909399; margin-top: 16px;">暂无立项材料</p>
        <el-button type="primary" @click="uploadMaterial">上传材料</el-button>
      </div>
    </el-card>

    <!-- 上传/编辑材料对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false">

      <el-form :model="formData" :rules="formRules" ref="materialForm" label-width="120px">
        <el-form-item label="材料类型" prop="category">
          <el-select v-model="formData.category" placeholder="请选择材料类型" style="width: 100%">
            <el-option v-for="category in materialCategories.filter(c => c.value !== 'all')" :key="category.value"
              :label="category.label" :value="category.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="formData.fileName" placeholder="请输入文件名称"></el-input>
        </el-form-item>

        <el-form-item label="文件描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请描述文件的内容和用途">
          </el-input>
        </el-form-item>

        <el-form-item v-if="!isEdit" label="文件上传" prop="file">
          <el-upload ref="upload" :auto-upload="false" :on-change="handleFileChange" :file-list="fileList" :limit="1"
            action="#">
            <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
            <div slot="tip" class="el-upload__tip">支持上传 PDF、Word、Excel、图片等格式文件，单个文件不超过50MB</div>
          </el-upload>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="版本号" prop="version">
              <el-input v-model="formData.version" placeholder="如：v1.0"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="草稿" value="draft"></el-option>
                <el-option label="待审核" value="pending"></el-option>
                <el-option label="已审核" value="approved"></el-option>
                <el-option label="已驳回" value="rejected"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="formData.keywords" placeholder="请输入关键词，用逗号分隔"></el-input>
        </el-form-item>

        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" type="textarea" :rows="2" placeholder="其他备注信息">
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMaterial">保存</el-button>
      </div>
    </el-dialog>

    <!-- 预览材料对话框 -->
    <el-dialog title="材料预览" :visible.sync="previewDialogVisible" width="80%">

      <el-descriptions v-if="currentMaterial" :column="2" border>
        <el-descriptions-item label="文件名称" :span="2">{{ currentMaterial.fileName }}</el-descriptions-item>
        <el-descriptions-item label="材料类型">
          <el-tag :type="getCategoryTagType(currentMaterial.category)">
            {{ getCategoryText(currentMaterial.category) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(currentMaterial.status)">
            {{ getStatusText(currentMaterial.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="文件大小">{{ formatFileSize(currentMaterial.fileSize) }}</el-descriptions-item>
        <el-descriptions-item label="版本号">{{ currentMaterial.version }}</el-descriptions-item>
        <el-descriptions-item label="上传人">{{ currentMaterial.uploader }}</el-descriptions-item>
        <el-descriptions-item label="上传日期">{{ currentMaterial.uploadDate }}</el-descriptions-item>
        <el-descriptions-item label="关键词" :span="2">{{ currentMaterial.keywords }}</el-descriptions-item>
        <el-descriptions-item label="文件描述" :span="2">
          <div style="white-space: pre-wrap;">{{ currentMaterial.description }}</div>
        </el-descriptions-item>
        <el-descriptions-item v-if="currentMaterial.notes" label="备注" :span="2">
          <div style="white-space: pre-wrap;">{{ currentMaterial.notes }}</div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 文件预览区域 -->
      <div class="file-preview" style="margin-top: 20px; text-align: center;">
        <div v-if="currentMaterial && isImageFile(currentMaterial.fileName)" style="max-height: 400px; overflow: auto;">
          <img :src="getFilePreviewUrl(currentMaterial)" style="max-width: 100%; height: auto;" alt="预览图片">
        </div>
        <div v-else style="padding: 40px; background: #f5f7fa; border-radius: 4px;">
          <i class="el-icon-document" style="font-size: 48px; color: #C0C4CC;"></i>
          <p style="color: #909399; margin-top: 10px;">该文件类型不支持在线预览</p>
          <el-button v-if="currentMaterial" type="primary" @click="downloadMaterial(currentMaterial)">下载查看</el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关闭</el-button>
        <el-button v-if="currentMaterial" type="primary" @click="editMaterial(currentMaterial)">编辑</el-button>
        <el-button v-if="currentMaterial" type="success" @click="downloadMaterial(currentMaterial)">下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ProjectMaterials',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      previewDialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      editId: null,
      currentMaterial: null,
      selectedCategory: 'all',
      materialList: [],
      fileList: [],
      formData: {
        fileName: '',
        category: '',
        description: '',
        version: 'v1.0',
        status: 'draft',
        keywords: '',
        notes: '',
        file: null
      },
      materialCategories: [
        { label: '全部', value: 'all' },
        { label: '项目申请书', value: 'application' },
        { label: '技术方案', value: 'technical' },
        { label: '预算报告', value: 'budget' },
        { label: '可行性报告', value: 'feasibility' },
        { label: '风险评估', value: 'risk' },
        { label: '合同文件', value: 'contract' },
        { label: '审批文件', value: 'approval' },
        { label: '其他材料', value: 'other' }
      ],
      formRules: {
        fileName: [
          { required: true, message: '请输入文件名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择材料类型', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入文件描述', trigger: 'blur' },
          { min: 10, message: '文件描述至少10个字符', trigger: 'blur' }
        ],
        version: [
          { required: true, message: '请输入版本号', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    filteredMaterialList() {
      if (this.selectedCategory === 'all') {
        return this.materialList
      }
      return this.materialList.filter(material => material.category === this.selectedCategory)
    },
    approvedMaterialCount() {
      return this.materialList.filter(m => m.status === 'approved').length
    },
    pendingMaterialCount() {
      return this.materialList.filter(m => m.status === 'pending').length
    },
    totalFileSize() {
      const totalBytes = this.materialList.reduce((total, material) => {
        return total + (material.fileSize || 0)
      }, 0)
      return (totalBytes / (1024 * 1024)).toFixed(1) // 转换为MB
    }
  },
  created() {
    this.loadMaterialData()
  },
  methods: {
    // 加载材料数据
    loadMaterialData() {
      // 这里应该从API获取材料数据
      this.materialList = []

      // 可以调用API获取数据
      // this.fetchMaterialData()
    },

    // 从API获取材料数据
    async fetchMaterialData() {
      try {
        // const response = await this.$api.getProjectMaterials(this.projectInfo.id)
        // this.materialList = response.data

        console.log('获取材料数据:', this.projectInfo.id)
      } catch (error) {
        console.error('获取材料数据失败:', error)
        this.$message.error('获取材料数据失败')
      }
    },

    // 按分类筛选
    filterByCategory(category) {
      this.selectedCategory = category
    },

    // 获取分类数量
    getCategoryCount(category) {
      if (category === 'all') {
        return this.materialList.length
      }
      return this.materialList.filter(m => m.category === category).length
    },

    // 上传材料
    uploadMaterial() {
      this.dialogTitle = '上传材料'
      this.isEdit = false
      this.editId = null
      this.fileList = []
      this.formData = {
        fileName: '',
        category: '',
        description: '',
        version: 'v1.0',
        status: 'draft',
        keywords: '',
        notes: '',
        file: null
      }
      this.dialogVisible = true
      this.previewDialogVisible = false
    },

    // 编辑材料
    editMaterial(material) {
      this.dialogTitle = '编辑材料'
      this.isEdit = true
      this.editId = material.id
      this.formData = { ...material }
      this.dialogVisible = true
      this.previewDialogVisible = false
    },

    // 预览材料
    previewMaterial(material) {
      this.currentMaterial = material
      this.previewDialogVisible = true
    },

    // 处理文件选择
    handleFileChange(file) {
      this.formData.file = file.raw
      if (!this.formData.fileName) {
        this.formData.fileName = file.name
      }
    },

    // 保存材料
    saveMaterial() {
      this.$refs.materialForm.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            // 更新材料
            const index = this.materialList.findIndex(m => m.id === this.editId)
            if (index !== -1) {
              this.materialList.splice(index, 1, { ...this.formData, id: this.editId })
            }
            this.$message.success('材料更新成功')
          } else {
            // 添加新材料
            const newMaterial = {
              ...this.formData,
              id: Date.now().toString(),
              fileSize: this.formData.file ? this.formData.file.size : 0,
              uploadDate: new Date().toISOString().split('T')[0],
              uploader: '当前用户' // 实际应该从用户信息获取
            }
            this.materialList.push(newMaterial)
            this.$message.success('材料上传成功')
          }
          this.dialogVisible = false
        }
      })
    },

    // 下载材料
    downloadMaterial(material) {
      // 这里实现材料下载功能
      this.$message.success(`正在下载：${material.fileName}`)
    },

    // 删除材料
    deleteMaterial(material) {
      this.$confirm('确认删除该材料吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.materialList.findIndex(m => m.id === material.id)
        if (index !== -1) {
          this.materialList.splice(index, 1)
          this.$message.success('材料删除成功')
        }
      })
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    },

    // 判断是否为图片文件
    isImageFile(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false
      }
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      return imageExtensions.includes(extension)
    },

    // 获取文件预览URL
    getFilePreviewUrl(material) {
      if (!material || !material.id) {
        return ''
      }
      // 这里应该返回实际的文件预览URL
      return '/api/files/preview/' + material.id
    },

    // 获取分类标签类型
    getCategoryTagType(category) {
      const typeMap = {
        'application': 'primary',
        'technical': 'success',
        'budget': 'warning',
        'feasibility': 'info',
        'risk': 'danger',
        'contract': 'primary',
        'approval': 'success',
        'other': ''
      }
      return typeMap[category] || ''
    },

    // 获取分类文本
    getCategoryText(category) {
      const category_obj = this.materialCategories.find(c => c.value === category)
      return category_obj ? category_obj.label : '未知'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'draft': 'info',
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'draft': '草稿',
        'pending': '待审核',
        'approved': '已审核',
        'rejected': '已驳回'
      }
      return textMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
.project-materials-container {
  height: 100%;
}

.materials-stats {
  margin-bottom: 30px;
}

.material-categories {
  margin-bottom: 20px;
}

.materials-list {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.file-preview {
  margin-top: 20px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}
</style>
