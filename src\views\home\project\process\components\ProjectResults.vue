<template>
  <div class="project-results-container">
    <el-card>
      <div slot="header">
        <span>🏆 项目成果管理</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text" 
          @click="addResult">
          添加成果
        </el-button>
      </div>
      
      <!-- 成果统计 -->
      <div class="results-stats" style="margin-bottom: 30px;">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="成果总数" :value="resultList.length" suffix="项">
              <template slot="prefix">
                <i class="el-icon-trophy" style="color: #409EFF"></i>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="已完成" :value="completedResultCount" suffix="项">
              <template slot="prefix">
                <i class="el-icon-circle-check" style="color: #67C23A"></i>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="进行中" :value="inProgressResultCount" suffix="项">
              <template slot="prefix">
                <i class="el-icon-loading" style="color: #E6A23C"></i>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="完成率" :value="resultCompletionRate" suffix="%">
              <template slot="prefix">
                <i class="el-icon-pie-chart" style="color: #F56C6C"></i>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
      </div>
      
      <!-- 成果分类标签 -->
      <div class="result-categories" style="margin-bottom: 20px;">
        <el-tag 
          v-for="category in resultCategories"
          :key="category.value"
          :type="selectedCategory === category.value ? 'primary' : 'info'"
          style="margin-right: 10px; cursor: pointer;"
          @click="filterByCategory(category.value)">
          {{ category.label }} ({{ getCategoryCount(category.value) }})
        </el-tag>
      </div>
      
      <!-- 成果列表 -->
      <div class="results-list">
        <el-table :data="filteredResultList" style="width: 100%" empty-text="暂无成果数据">
          <el-table-column prop="title" label="成果名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="成果类型" width="120">
            <template slot-scope="scope">
              <el-tag :type="getResultTypeTagType(scope.row.type)">
                {{ getResultTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="成果级别" width="100">
            <template slot-scope="scope">
              <el-tag :type="getLevelTagType(scope.row.level)">
                {{ getLevelText(scope.row.level) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="completionDate" label="完成日期" width="120"></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button size="mini" @click="viewResult(scope.row)">查看</el-button>
              <el-button size="mini" @click="editResult(scope.row)">编辑</el-button>
              <el-button size="mini" type="danger" @click="deleteResult(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 空状态 -->
      <div v-if="resultList.length === 0" class="empty-state" style="text-align: center; padding: 40px;">
        <i class="el-icon-trophy" style="font-size: 64px; color: #C0C4CC;"></i>
        <p style="color: #909399; margin-top: 16px;">暂无项目成果</p>
        <el-button type="primary" @click="addResult">添加成果</el-button>
      </div>
    </el-card>
    
    <!-- 添加/编辑成果对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="70%"
      :close-on-click-modal="false">
      
      <el-form :model="formData" :rules="formRules" ref="resultForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="成果名称" prop="title">
              <el-input v-model="formData.title" placeholder="请输入成果名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成果类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择成果类型" style="width: 100%">
                <el-option label="学术论文" value="paper"></el-option>
                <el-option label="发明专利" value="patent"></el-option>
                <el-option label="软件著作权" value="software"></el-option>
                <el-option label="技术标准" value="standard"></el-option>
                <el-option label="产品原型" value="prototype"></el-option>
                <el-option label="技术报告" value="report"></el-option>
                <el-option label="获奖成果" value="award"></el-option>
                <el-option label="其他" value="other"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="成果级别" prop="level">
              <el-select v-model="formData.level" placeholder="请选择成果级别" style="width: 100%">
                <el-option label="国际级" value="international"></el-option>
                <el-option label="国家级" value="national"></el-option>
                <el-option label="省部级" value="provincial"></el-option>
                <el-option label="市级" value="municipal"></el-option>
                <el-option label="校级" value="institutional"></el-option>
                <el-option label="其他" value="other"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="计划中" value="planning"></el-option>
                <el-option label="进行中" value="in-progress"></el-option>
                <el-option label="已完成" value="completed"></el-option>
                <el-option label="已发布" value="published"></el-option>
                <el-option label="已获奖" value="awarded"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="完成日期" prop="completionDate">
              <el-date-picker 
                v-model="formData.completionDate" 
                type="date" 
                placeholder="选择完成日期"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主要贡献者" prop="contributors">
              <el-input v-model="formData.contributors" placeholder="请输入主要贡献者"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="成果描述" prop="description">
          <el-input 
            v-model="formData.description" 
            type="textarea" 
            :rows="4"
            placeholder="请详细描述成果的内容、创新点和价值">
          </el-input>
        </el-form-item>
        
        <el-form-item label="技术指标" prop="technicalSpecs">
          <el-input 
            v-model="formData.technicalSpecs" 
            type="textarea" 
            :rows="3"
            placeholder="请描述成果的主要技术指标和性能参数">
          </el-input>
        </el-form-item>
        
        <el-form-item label="应用前景" prop="applicationProspect">
          <el-input 
            v-model="formData.applicationProspect" 
            type="textarea" 
            :rows="3"
            placeholder="请描述成果的应用前景和市场价值">
          </el-input>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发表期刊/会议" prop="publication">
              <el-input v-model="formData.publication" placeholder="如适用，请输入发表期刊或会议"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专利/证书号" prop="certificateNumber">
              <el-input v-model="formData.certificateNumber" placeholder="如适用，请输入专利号或证书号"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="formData.keywords" placeholder="请输入关键词，用逗号分隔"></el-input>
        </el-form-item>
        
        <el-form-item label="备注" prop="notes">
          <el-input 
            v-model="formData.notes" 
            type="textarea" 
            :rows="2"
            placeholder="其他备注信息">
          </el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveResult">保存</el-button>
      </div>
    </el-dialog>
    
    <!-- 查看成果详情对话框 -->
    <el-dialog
      title="成果详情"
      :visible.sync="viewDialogVisible"
      width="70%">
      
      <el-descriptions v-if="currentResult" :column="2" border>
        <el-descriptions-item label="成果名称" :span="2">{{ currentResult.title }}</el-descriptions-item>
        <el-descriptions-item label="成果类型">
          <el-tag :type="getResultTypeTagType(currentResult.type)">
            {{ getResultTypeText(currentResult.type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="成果级别">
          <el-tag :type="getLevelTagType(currentResult.level)">
            {{ getLevelText(currentResult.level) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(currentResult.status)">
            {{ getStatusText(currentResult.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="完成日期">{{ currentResult.completionDate }}</el-descriptions-item>
        <el-descriptions-item label="主要贡献者" :span="2">{{ currentResult.contributors }}</el-descriptions-item>
        <el-descriptions-item v-if="currentResult.publication" label="发表期刊/会议">{{ currentResult.publication }}</el-descriptions-item>
        <el-descriptions-item v-if="currentResult.certificateNumber" label="专利/证书号">{{ currentResult.certificateNumber }}</el-descriptions-item>
        <el-descriptions-item label="关键词" :span="2">{{ currentResult.keywords }}</el-descriptions-item>
        <el-descriptions-item label="成果描述" :span="2">
          <div style="white-space: pre-wrap;">{{ currentResult.description }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="技术指标" :span="2">
          <div style="white-space: pre-wrap;">{{ currentResult.technicalSpecs }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="应用前景" :span="2">
          <div style="white-space: pre-wrap;">{{ currentResult.applicationProspect }}</div>
        </el-descriptions-item>
        <el-descriptions-item v-if="currentResult.notes" label="备注" :span="2">
          <div style="white-space: pre-wrap;">{{ currentResult.notes }}</div>
        </el-descriptions-item>
      </el-descriptions>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="editResult(currentResult)">编辑</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ProjectResults',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      viewDialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      editId: null,
      currentResult: null,
      selectedCategory: 'all',
      resultList: [],
      formData: {
        title: '',
        type: '',
        level: '',
        status: 'planning',
        completionDate: '',
        contributors: '',
        description: '',
        technicalSpecs: '',
        applicationProspect: '',
        publication: '',
        certificateNumber: '',
        keywords: '',
        notes: ''
      },
      resultCategories: [
        { label: '全部', value: 'all' },
        { label: '学术论文', value: 'paper' },
        { label: '发明专利', value: 'patent' },
        { label: '软件著作权', value: 'software' },
        { label: '技术标准', value: 'standard' },
        { label: '产品原型', value: 'prototype' },
        { label: '技术报告', value: 'report' },
        { label: '获奖成果', value: 'award' },
        { label: '其他', value: 'other' }
      ],
      formRules: {
        title: [
          { required: true, message: '请输入成果名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择成果类型', trigger: 'change' }
        ],
        level: [
          { required: true, message: '请选择成果级别', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        completionDate: [
          { required: true, message: '请选择完成日期', trigger: 'change' }
        ],
        contributors: [
          { required: true, message: '请输入主要贡献者', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入成果描述', trigger: 'blur' },
          { min: 20, message: '成果描述至少20个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    filteredResultList() {
      if (this.selectedCategory === 'all') {
        return this.resultList
      }
      return this.resultList.filter(result => result.type === this.selectedCategory)
    },
    completedResultCount() {
      return this.resultList.filter(r => ['completed', 'published', 'awarded'].includes(r.status)).length
    },
    inProgressResultCount() {
      return this.resultList.filter(r => ['planning', 'in-progress'].includes(r.status)).length
    },
    resultCompletionRate() {
      if (this.resultList.length === 0) return 0
      return Math.round((this.completedResultCount / this.resultList.length) * 100)
    }
  },
  created() {
    this.loadResultData()
  },
  methods: {
    // 加载成果数据
    loadResultData() {
      // 这里应该从API获取成果数据
      this.resultList = []
      
      // 可以调用API获取数据
      // this.fetchResultData()
    },
    
    // 从API获取成果数据
    async fetchResultData() {
      try {
        // const response = await this.$api.getProjectResults(this.projectInfo.id)
        // this.resultList = response.data
        
        console.log('获取成果数据:', this.projectInfo.id)
      } catch (error) {
        console.error('获取成果数据失败:', error)
        this.$message.error('获取成果数据失败')
      }
    },
    
    // 按分类筛选
    filterByCategory(category) {
      this.selectedCategory = category
    },
    
    // 获取分类数量
    getCategoryCount(category) {
      if (category === 'all') {
        return this.resultList.length
      }
      return this.resultList.filter(r => r.type === category).length
    },
    
    // 添加成果
    addResult() {
      this.dialogTitle = '添加成果'
      this.isEdit = false
      this.editId = null
      this.formData = {
        title: '',
        type: '',
        level: '',
        status: 'planning',
        completionDate: '',
        contributors: '',
        description: '',
        technicalSpecs: '',
        applicationProspect: '',
        publication: '',
        certificateNumber: '',
        keywords: '',
        notes: ''
      }
      this.dialogVisible = true
      this.viewDialogVisible = false
    },
    
    // 编辑成果
    editResult(result) {
      this.dialogTitle = '编辑成果'
      this.isEdit = true
      this.editId = result.id
      this.formData = { ...result }
      this.dialogVisible = true
      this.viewDialogVisible = false
    },
    
    // 查看成果
    viewResult(result) {
      this.currentResult = result
      this.viewDialogVisible = true
    },
    
    // 保存成果
    saveResult() {
      this.$refs.resultForm.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            // 更新成果
            const index = this.resultList.findIndex(r => r.id === this.editId)
            if (index !== -1) {
              this.resultList.splice(index, 1, { ...this.formData, id: this.editId })
            }
            this.$message.success('成果更新成功')
          } else {
            // 添加新成果
            const newResult = {
              ...this.formData,
              id: Date.now().toString()
            }
            this.resultList.push(newResult)
            this.$message.success('成果添加成功')
          }
          this.dialogVisible = false
        }
      })
    },
    
    // 删除成果
    deleteResult(result) {
      this.$confirm('确认删除该成果吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.resultList.findIndex(r => r.id === result.id)
        if (index !== -1) {
          this.resultList.splice(index, 1)
          this.$message.success('成果删除成功')
        }
      })
    },
    
    // 获取成果类型标签类型
    getResultTypeTagType(type) {
      const typeMap = {
        'paper': 'primary',
        'patent': 'success',
        'software': 'warning',
        'standard': 'info',
        'prototype': 'danger',
        'report': 'primary',
        'award': 'success',
        'other': ''
      }
      return typeMap[type] || ''
    },
    
    // 获取成果类型文本
    getResultTypeText(type) {
      const category_obj = this.resultCategories.find(c => c.value === type)
      return category_obj ? category_obj.label : '未知'
    },
    
    // 获取级别标签类型
    getLevelTagType(level) {
      const typeMap = {
        'international': 'danger',
        'national': 'warning',
        'provincial': 'primary',
        'municipal': 'success',
        'institutional': 'info',
        'other': ''
      }
      return typeMap[level] || ''
    },
    
    // 获取级别文本
    getLevelText(level) {
      const textMap = {
        'international': '国际级',
        'national': '国家级',
        'provincial': '省部级',
        'municipal': '市级',
        'institutional': '校级',
        'other': '其他'
      }
      return textMap[level] || '未知'
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'planning': 'info',
        'in-progress': 'warning',
        'completed': 'success',
        'published': 'success',
        'awarded': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'planning': '计划中',
        'in-progress': '进行中',
        'completed': '已完成',
        'published': '已发布',
        'awarded': '已获奖'
      }
      return textMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
.project-results-container {
  height: 100%;
}

.results-stats {
  margin-bottom: 30px;
}

.result-categories {
  margin-bottom: 20px;
}

.results-list {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  text-align: right;
}
</style>
