<template>
<div style="background-color: white; padding: 20px">
  <!-- 查询工具栏 -->
  <el-form ref="queryForm" :model="queryForm" :rules="formRules" label-width="150px">
    <!-- 第一行查询条件 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="课题名称" prop="subjectName">
          <el-input v-model="queryForm.subjectName" clearable placeholder="请输入课题名称关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="课题来源" prop="subjectSource">
          <el-input v-model="queryForm.subjectSource" clearable placeholder="请输入课题来源关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="负责人" prop="leader">
          <el-select v-model="queryForm.leader" clearable placeholder="请选择负责人" filterable>
            <el-option v-for="person in oaPersonnelList" :key="person.id"
              :label="`${person.name} (${person.department})`" :value="person.name">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="研究方式" prop="researchMethod">
          <el-select v-model="queryForm.researchMethod" clearable placeholder="请选择研究方式">
            <el-option label="自主" value="自主"></el-option>
            <el-option label="联合" value="联合"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 第二行查询条件 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="年份" prop="year">
          <el-select v-model="queryForm.year" clearable placeholder="请选择年份">
            <el-option label="2024年" value="2024"></el-option>
            <el-option label="2023年" value="2023"></el-option>
            <el-option label="2022年" value="2022"></el-option>
            <el-option label="2021年" value="2021"></el-option>
            <el-option label="2020年" value="2020"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="是否重大项目" prop="isMajorProject">
          <el-select v-model="queryForm.isMajorProject" clearable placeholder="请选择">
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <!-- 操作工具栏 -->
  <div style="margin-bottom: 20px">
    <el-button type="primary" @click="handleAdd">新增</el-button>
    <el-button type="primary" @click="handleImport">导入Excel</el-button>
    <el-button type="success" @click="handleDownloadTemplate">下载模板</el-button>
    <el-button type="warning" @click="handleExport">导出Excel</el-button>

    <!-- 使用提示 -->
    <el-tooltip content="支持.xlsx和.xls格式，建议先下载模板查看格式要求" placement="top">
      <i class="el-icon-question" style="margin-left: 10px; color: #909399; cursor: help;"></i>
    </el-tooltip>
  </div>

  <!-- 表格 -->
  <el-table :data="currentPageData" border stripe style="width: 100%" height="400px"
    @selection-change="handleSelectionChange" v-loading="loading">
    <el-table-column type="selection" width="55"></el-table-column>
    <el-table-column prop="id" label="序号" width="50"></el-table-column>
    <el-table-column prop="subjectName" label="课题名称" show-overflow-tooltip width="auto"></el-table-column>
    <el-table-column prop="subjectSource" label="课题来源" show-overflow-tooltip align="center" width="auto"></el-table-column>
    <el-table-column prop="leader" label="负责人" width="auto" align="center"></el-table-column>
    <el-table-column prop="researchMethod" label="研究方式" width="auto" align="center"></el-table-column>
    <el-table-column prop="year" label="年份" width="auto" align="center"></el-table-column>
    <el-table-column prop="isMajorProject" label="重大项目" width="auto" align="center">
      <template slot-scope="scope">
        <el-tag :type="scope.row.isMajorProject === '1' ? 'danger' : 'info'" size="small">
          {{ scope.row.isMajorProject === '1' ? '是' : '否' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="researchPeriod" label="研究期限" width="auto" align="center" show-overflow-tooltip></el-table-column>
    <el-table-column prop="mainContent" label="研究内容" width="auto" show-overflow-tooltip></el-table-column>
    <el-table-column prop="expectedResults" label="预期成果" width="auto" show-overflow-tooltip></el-table-column>
    <el-table-column label="操作" width="200" align="center" fixed="right">
      <template slot-scope="scope">
        <el-button size="mini" @click="handleView(scope.row)">查看</el-button>
        <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
        <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <!-- 分页 -->
  <div style="margin-top: 20px;float:right">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalCount"
    />
  </div>

  <!-- 新增/编辑对话框 -->
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" @close="resetForm">
    <el-form ref="dataForm" :model="formData" :rules="formRules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="课题名称" prop="subjectName">
            <el-input v-model="formData.subjectName" placeholder="请输入课题名称" maxlength="100" show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课题来源" prop="subjectSource">
            <el-select v-model="formData.subjectSource" placeholder="请选择课题来源" style="width: 100%" filterable allow-create>
              <el-option label="省国资委" value="省国资委"></el-option>
              <el-option label="国家自然科学基金" value="国家自然科学基金"></el-option>
              <el-option label="科技部" value="科技部"></el-option>
              <el-option label="教育部" value="教育部"></el-option>
              <el-option label="企业自主" value="企业自主"></el-option>
              <el-option label="其他" value="其他"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人" prop="leader">
            <el-input v-model="formData.leader" placeholder="请输入负责人姓名" maxlength="20"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="研究方式" prop="researchMethod">
            <el-select v-model="formData.researchMethod" placeholder="请选择研究方式" style="width: 100%">
              <el-option label="自主" value="自主"></el-option>
              <el-option label="合作" value="合作"></el-option>
              <el-option label="委托" value="委托"></el-option>
              <el-option label="联合" value="联合"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="年份" prop="year">
            <el-select v-model="formData.year" placeholder="请选择年份" style="width: 100%">
              <el-option label="2024年" value="2024"></el-option>
              <el-option label="2023年" value="2023"></el-option>
              <el-option label="2022年" value="2022"></el-option>
              <el-option label="2021年" value="2021"></el-option>
              <el-option label="2020年" value="2020"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否重大项目" prop="isMajorProject">
            <el-select v-model="formData.isMajorProject" placeholder="请选择" style="width: 100%">
              <el-option label="是" value="1"></el-option>
              <el-option label="否" value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="formData.startTime"
              type="date"
              placeholder="选择开始时间"
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              type="date"
              placeholder="选择结束时间"
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="主要内容" prop="mainContent">
        <el-input
          v-model="formData.mainContent"
          type="textarea"
          :rows="4"
          placeholder="请输入主要研究内容"
          maxlength="500"
          show-word-limit>
        </el-input>
      </el-form-item>
      <el-form-item label="预期成果" prop="expectedResults">
        <el-input
          v-model="formData.expectedResults"
          type="textarea"
          :rows="4"
          placeholder="请输入预期成果"
          maxlength="500"
          show-word-limit>
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
    </div>
  </el-dialog>

</div>
</template>

<script>
import * as api from './api/api'



export default {
  name: 'PreProjectInitiation',
  components: {},
  data() {
    return {
      // 静态数据
      tableData: [
        {
          id: 1,
          subjectName: '人工智能在医疗诊断中的应用研究',
          subjectSource: '国家自然科学基金',
          leader: '张三',
          researchMethod: '自主',
          year: '2024',
          isMajorProject: '1',
          researchPeriod: '2024-01-01 至 2026-12-31',
          mainContent: '研究人工智能技术在医疗影像诊断中的应用，开发智能诊断系统，提高诊断准确率和效率。',
          expectedResults: '发表高水平论文3-5篇，申请发明专利2-3项，开发原型系统1套。',
          startTime: '2024-01-01',
          endTime: '2026-12-31',
          createTime: '2024-01-01'
        },
        {
          id: 2,
          subjectName: '新能源汽车动力电池回收利用技术',
          subjectSource: '科技部',
          leader: '李四',
          researchMethod: '联合',
          year: '2024',
          isMajorProject: '0',
          researchPeriod: '2024-03-01 至 2025-12-31',
          mainContent: '研究动力电池回收处理技术，建立完整的回收利用体系，实现资源循环利用。',
          expectedResults: '建立回收技术标准1套，申请专利3-5项，建设示范基地1个。',
          startTime: '2024-03-01',
          endTime: '2025-12-31',
          createTime: '2024-02-15'
        },
        {
          id: 3,
          subjectName: '5G通信网络优化算法研究',
          subjectSource: '企业自主',
          leader: '王五',
          researchMethod: '合作',
          year: '2023',
          isMajorProject: '0',
          researchPeriod: '2023-06-01 至 2024-05-31',
          mainContent: '研究5G网络优化算法，提升网络性能和用户体验，降低网络延迟。',
          expectedResults: '发表论文2-3篇，开发算法软件1套，申请软件著作权2项。',
          startTime: '2023-06-01',
          endTime: '2024-05-31',
          createTime: '2023-05-20'
        }
      ],
      selectedRows: [],
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      // 加载状态
      loading: false,
      // 对话框相关
      dialogVisible: false,
      dialogTitle: '新增课题',
      isEdit: false,
      editId: null,
      submitLoading: false,
      // 表单数据
      formData: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        year: '',
        isMajorProject: '',
        startTime: '',
        endTime: '',
        mainContent: '',
        expectedResults: ''
      },
      // 表单验证规则
      formRules: {
        subjectName: [
          { required: true, message: '请输入课题名称', trigger: 'blur' },
          { min: 5, max: 100, message: '长度在 5 到 100 个字符', trigger: 'blur' }
        ],
        subjectSource: [
          { required: true, message: '请选择课题来源', trigger: 'change' }
        ],
        leader: [
          { required: true, message: '请输入负责人姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        researchMethod: [
          { required: true, message: '请选择研究方式', trigger: 'change' }
        ],
        year: [
          { required: true, message: '请选择年份', trigger: 'change' }
        ],
        isMajorProject: [
          { required: true, message: '请选择是否重大项目', trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' },
          { validator: this.validateEndTime, trigger: 'change' }
        ],
        mainContent: [
          { required: true, message: '请输入主要研究内容', trigger: 'blur' },
          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }
        ],
        expectedResults: [
          { required: true, message: '请输入预期成果', trigger: 'blur' },
          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }
        ]
      },
      queryForm: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        year: '',
        isMajorProject: ''
      },
      // 原始数据备份，用于查询重置
      originalTableData: []
    }
  },
  mounted() {
    // 初始化原始数据
    this.originalTableData = [...this.tableData]
    this.totalCount = this.tableData.length
  },
  computed: {
    // 当前页数据
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.tableData.slice(start, end)
    }
  },
  created() {
    // 初始化数据
    this.loadTableData()
  },
  methods: {
    // 加载表格数据
    async loadTableData() {
      try {
        this.loading = true
        const response = await api.getTableData()

        if (response.code === 200) {
          this.tableData = response.data
          this.originalTableData = JSON.parse(JSON.stringify(response.data))
          this.totalCount = response.total
          this.$message.success('数据加载成功')
        } else {
          this.$message.error(response.message || '数据加载失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('网络请求失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 分页查询数据
    async loadTableDataByPage() {
      try {
        this.loading = true
        const response = await api.getTableDataByPage(
          this.currentPage,
          this.pageSize,
          this.queryForm
        )

        if (response.code === 200) {
          this.tableData = response.data
          this.totalCount = response.total
        } else {
          this.$message.error(response.message || '查询失败')
        }
      } catch (error) {
        console.error('查询数据失败:', error)
        this.$message.error('查询失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 表格选择变化
    handleSelectionChange(val) {
      this.selectedRows = val

      // 优化的控制台输出
      if (val.length === 0) {
        console.log("[表格选择] 已清空所有选择")
      } else {
        console.log(`[表格选择] 已选中 ${val.length} 行数据`)
        console.log("[选中数据] 详情:", val.map(item => ({
          id: item.id || '未知ID',
          subjectName: item.subjectName || '未知课题名称',
          leader: item.leader || '未知负责人',
          year: item.year || '未知年份',
          isMajorProject: item.isMajorProject === '1' ? '重大项目' : '普通项目'
        })))

        // 如果需要查看完整数据，可以展开下面的注释
        // console.log("[完整数据]", val)
      }
    },

    // 查询功能
    async handleQuery() {
      this.currentPage = 1 // 重置到第一页
      await this.loadTableDataByPage()
    },

    // 重置功能
    async handleReset() {
      // 重置查询表单
      this.queryForm = {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        year: '',
        isMajorProject: ''
      }

      // 重置表单验证
      if (this.$refs.queryForm) {
        this.$refs.queryForm.resetFields()
      }

      this.currentPage = 1
      await this.loadTableDataByPage()
      this.$message.success('重置成功')
    },

    // 分页大小变化
    async handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      await this.loadTableDataByPage()
    },
    // 当前页变化
    async handleCurrentChange(val) {
      this.currentPage = val
      await this.loadTableDataByPage()
    },

    // 新增功能
    handleAdd() {
      this.dialogTitle = '新增课题'
      this.isEdit = false
      this.editId = null
      this.resetForm()
      this.dialogVisible = true
    },

    // 提交表单
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true

          // 构建研究期限
          const researchPeriod = `${this.formData.startTime} 至 ${this.formData.endTime}`

          if (this.isEdit) {
            // 编辑模式
            this.updateItem(researchPeriod)
          } else {
            // 新增模式
            this.addNewItem(researchPeriod)
          }
        } else {
          this.$message.warning('请完善表单信息')
        }
      })
    },

    // 新增数据项
    addNewItem(researchPeriod) {
      try {
        // 生成新的ID
        const newId = this.tableData.length > 0 ? Math.max(...this.tableData.map(item => item.id)) + 1 : 1

        const newItem = {
          id: newId,
          ...this.formData,
          researchPeriod,
          createTime: new Date().toISOString().split('T')[0]
        }

        // 添加到表格数据
        this.tableData.unshift(newItem)
        this.originalTableData.unshift(newItem)
        this.totalCount = this.tableData.length

        this.$message.success('新增成功')
        this.dialogVisible = false
        this.resetForm()

        console.log('[新增成功] 新增数据:', newItem)
      } catch (error) {
        console.error('新增失败:', error)
        this.$message.error('新增失败，请稍后重试')
      } finally {
        this.submitLoading = false
      }
    },

    // 更新数据项
    updateItem(researchPeriod) {
      try {
        // 更新当前显示数据
        const index = this.tableData.findIndex(item => item.id === this.editId)
        if (index > -1) {
          this.tableData[index] = {
            ...this.tableData[index],
            ...this.formData,
            researchPeriod,
            updateTime: new Date().toISOString().split('T')[0]
          }
        }

        // 更新原始数据
        const originalIndex = this.originalTableData.findIndex(item => item.id === this.editId)
        if (originalIndex > -1) {
          this.originalTableData[originalIndex] = {
            ...this.originalTableData[originalIndex],
            ...this.formData,
            researchPeriod,
            updateTime: new Date().toISOString().split('T')[0]
          }
        }

        this.$message.success('编辑成功')
        this.dialogVisible = false
        this.resetForm()

        console.log('[编辑成功] 更新数据:', this.formData)
      } catch (error) {
        console.error('编辑失败:', error)
        this.$message.error('编辑失败，请稍后重试')
      } finally {
        this.submitLoading = false
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        year: '',
        isMajorProject: '',
        startTime: '',
        endTime: '',
        mainContent: '',
        expectedResults: ''
      }

      // 重置表单验证
      if (this.$refs.dataForm) {
        this.$refs.dataForm.resetFields()
      }

      this.submitLoading = false
    },

    // 编辑功能
    handleEdit(row) {
      this.dialogTitle = '编辑课题'
      this.isEdit = true
      this.editId = row.id

      // 填充表单数据
      this.formData = {
        subjectName: row.subjectName || '',
        subjectSource: row.subjectSource || '',
        leader: row.leader || '',
        researchMethod: row.researchMethod || '',
        year: row.year || '',
        isMajorProject: row.isMajorProject || '',
        startTime: row.startTime || '',
        endTime: row.endTime || '',
        mainContent: row.mainContent || '',
        expectedResults: row.expectedResults || ''
      }

      this.dialogVisible = true

      console.log('[编辑] 编辑数据:', row)
    },

    // 删除功能
    handleDelete(row) {
      this.$confirm(`确认删除课题"${row.subjectName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        try {
          // 从当前显示数据中删除
          const index = this.tableData.findIndex(item => item.id === row.id)
          if (index > -1) {
            this.tableData.splice(index, 1)
          }

          // 从原始数据中删除
          const originalIndex = this.originalTableData.findIndex(item => item.id === row.id)
          if (originalIndex > -1) {
            this.originalTableData.splice(originalIndex, 1)
          }

          this.totalCount = this.tableData.length
          this.$message.success('删除成功')

          console.log('[删除成功] 删除数据:', row)
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败，请稍后重试')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 验证结束时间
    validateEndTime(_, value, callback) {
      if (value && this.formData.startTime) {
        if (new Date(value) <= new Date(this.formData.startTime)) {
          callback(new Error('结束时间必须晚于开始时间'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },

    // 导入功能
    handleImport() {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.xlsx,.xls'

      input.onchange = (event) => {
        const file = event.target.files[0]
        if (!file) return

        // 验证文件类型
        const allowedTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel'
        ]

        if (!allowedTypes.includes(file.type)) {
          this.$message.error('请选择Excel文件（.xlsx或.xls格式）')
          return
        }

        // 验证文件大小（限制为5MB）
        if (file.size > 5 * 1024 * 1024) {
          this.$message.error('文件大小不能超过5MB')
          return
        }

        this.loading = true

        // 动态导入 xlsx 库
        import('xlsx').then(XLSX => {
          const reader = new FileReader()

          reader.onload = (e) => {
            try {
              const data = new Uint8Array(e.target.result)
              const workbook = XLSX.read(data, { type: 'array' })

              // 读取第一个工作表
              const firstSheetName = workbook.SheetNames[0]
              const worksheet = workbook.Sheets[firstSheetName]

              // 转换为JSON数据
              const jsonData = XLSX.utils.sheet_to_json(worksheet)

              if (jsonData.length === 0) {
                this.$message.warning('Excel文件中没有数据')
                this.loading = false
                return
              }

              // 数据格式转换和验证
              const importedData = this.processImportData(jsonData)

              if (importedData.length > 0) {
                // 添加到表格数据中
                this.tableData = [...this.tableData, ...importedData]
                this.originalTableData = [...this.originalTableData, ...importedData]
                this.totalCount = this.tableData.length

                this.$message.success(`成功导入 ${importedData.length} 条数据`)
              } else {
                this.$message.warning('没有有效的数据可导入')
              }

            } catch (error) {
              console.error('解析Excel文件失败:', error)
              this.$message.error('文件格式错误，请检查Excel文件格式')
            } finally {
              this.loading = false
            }
          }

          reader.onerror = () => {
            this.$message.error('文件读取失败')
            this.loading = false
          }

          reader.readAsArrayBuffer(file)

        }).catch(error => {
          console.error('导入 xlsx 库失败:', error)
          this.$message.error('导入功能需要安装 xlsx 库，请联系管理员')
          this.loading = false
        })
      }

      // 触发文件选择
      input.click()
    },

    // 处理导入数据
    processImportData(jsonData) {
      const validData = []

      jsonData.forEach((row, index) => {
        try {
          // 数据映射和验证
          const item = {
            id: Date.now() + index, // 生成唯一ID
            subjectName: this.getExcelValue(row, ['课题名称', '项目名称', '名称']),
            subjectSource: this.getExcelValue(row, ['课题来源', '来源', '资助单位']),
            leader: this.getExcelValue(row, ['负责人', '项目负责人', '主持人']),
            researchMethod: this.getExcelValue(row, ['研究方式', '研究类型', '方式']),
            year: this.getExcelValue(row, ['年份', '年度', '立项年份']),
            isMajorProject: this.parseBoolean(this.getExcelValue(row, ['是否重大项目', '重大项目', '是否重点'])),
            researchPeriod: this.getExcelValue(row, ['研究期限', '项目期限', '期限']),
            mainContent: this.getExcelValue(row, ['主要内容', '研究内容', '内容']),
            expectedResults: this.getExcelValue(row, ['预期成果', '预期结果', '成果'])
          }

          // 验证必填字段
          if (item.subjectName && item.leader) {
            validData.push(item)
          } else {
            console.warn(`第 ${index + 2} 行数据缺少必填字段，已跳过`)
          }

        } catch (error) {
          console.warn(`第 ${index + 2} 行数据格式错误，已跳过:`, error)
        }
      })

      return validData
    },

    // 获取Excel单元格值（支持多种列名）
    getExcelValue(row, possibleKeys) {
      for (const key of possibleKeys) {
        if (row[key] !== undefined && row[key] !== null && row[key] !== '') {
          return String(row[key]).trim()
        }
      }
      return ''
    },

    // 解析布尔值
    parseBoolean(value) {
      if (!value) return '0'
      const str = String(value).toLowerCase().trim()
      return ['是', 'true', '1', 'yes', '重大', '重点'].includes(str) ? '1' : '0'
    },

    // 下载导入模板
    handleDownloadTemplate() {
      try {
        // 动态导入 xlsx 库
        import('xlsx').then(XLSX => {
          // 创建模板数据
          const templateData = [
            {
              '课题名称': '示例：人工智能在医疗诊断中的应用研究',
              '课题来源': '国家自然科学基金',
              '负责人': '张三',
              '研究方式': '自主',
              '年份': '2024',
              '是否重大项目': '是',
              '研究期限': '2024-01-01至2026-12-31',
              '主要内容': '研究人工智能技术在医疗影像诊断中的应用，开发智能诊断系统',
              '预期成果': '发表高水平论文3-5篇，申请发明专利2-3项，开发原型系统1套'
            },
            {
              '课题名称': '',
              '课题来源': '',
              '负责人': '',
              '研究方式': '',
              '年份': '',
              '是否重大项目': '',
              '研究期限': '',
              '主要内容': '',
              '预期成果': ''
            }
          ]

          // 创建工作簿
          const ws = XLSX.utils.json_to_sheet(templateData)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, '年度计划模板')

          // 设置列宽
          const colWidths = [
            { wch: 30 },  // 课题名称
            { wch: 20 },  // 课题来源
            { wch: 12 },  // 负责人
            { wch: 12 },  // 研究方式
            { wch: 8 },   // 年份
            { wch: 15 },  // 是否重大项目
            { wch: 25 },  // 研究期限
            { wch: 40 },  // 主要内容
            { wch: 40 }   // 预期成果
          ]
          ws['!cols'] = colWidths

          // 添加数据验证说明
          const instructions = [
            '数据填写说明：',
            '1. 课题名称：必填，不超过100字符',
            '2. 课题来源：建议填写具体资助单位或来源',
            '3. 负责人：必填，项目主要负责人姓名',
            '4. 研究方式：可选值：自主、联合、合作、委托',
            '5. 年份：格式如：2024',
            '6. 是否重大项目：可选值：是、否',
            '7. 研究期限：建议格式：开始日期至结束日期',
            '8. 主要内容：项目的主要研究内容描述',
            '9. 预期成果：项目预期达到的成果',
            '',
            '注意：第一行为示例数据，第二行开始填写实际数据'
          ]

          // 创建说明工作表
          const instructionWs = XLSX.utils.aoa_to_sheet(instructions.map(item => [item]))
          XLSX.utils.book_append_sheet(wb, instructionWs, '填写说明')

          // 生成文件名
          const fileName = `年度计划导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`

          // 导出文件
          XLSX.writeFile(wb, fileName)

          this.$message.success(`模板下载成功！文件名：${fileName}`)
        }).catch(error => {
          console.error('导入 xlsx 库失败:', error)
          this.$message.error('下载功能需要安装 xlsx 库，请联系管理员')
        })
      } catch (error) {
        console.error('下载模板失败:', error)
        this.$message.error('下载失败，请稍后重试')
      }
    },

    // 导出功能
    handleExport() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 动态导入 xlsx 库
        import('xlsx').then(XLSX => {
          // 准备导出数据
          const exportData = this.tableData.map((item, index) => ({
            '序号': index + 1,
            '课题名称': item.subjectName || '',
            '课题来源': item.subjectSource || '',
            '负责人': item.leader || '',
            '研究方式': item.researchMethod || '',
            '年份': item.year || '',
            '是否重大项目': item.isMajorProject === '1' ? '是' : '否',
            '研究期限': item.researchPeriod || '',
            '主要内容': item.mainContent || '',
            '预期成果': item.expectedResults || ''
          }))

          // 创建工作簿
          const ws = XLSX.utils.json_to_sheet(exportData)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, '年度计划')

          // 设置列宽
          const colWidths = [
            { wch: 8 },   // 序号
            { wch: 30 },  // 课题名称
            { wch: 20 },  // 课题来源
            { wch: 12 },  // 负责人
            { wch: 12 },  // 研究方式
            { wch: 8 },   // 年份
            { wch: 15 },  // 是否重大项目
            { wch: 25 },  // 研究期限
            { wch: 40 },  // 主要内容
            { wch: 40 }   // 预期成果
          ]
          ws['!cols'] = colWidths

          // 生成文件名
          const fileName = `年度计划_${new Date().toISOString().slice(0, 10)}.xlsx`

          // 导出文件
          XLSX.writeFile(wb, fileName)

          this.$message.success(`导出成功！文件名：${fileName}`)
        }).catch(error => {
          console.error('导入 xlsx 库失败:', error)
          this.$message.error('导出功能需要安装 xlsx 库，请联系管理员')
        })
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请稍后重试')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}

// 查询工具栏样式
.el-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;

  .el-row {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-form-item {
    margin-bottom: 10px;
  }
}

// 查询按钮组样式
.el-button+.el-button {
  margin-left: 10px;
}

// 展开/收起按钮样式
.el-button--text {
  padding: 0;
  margin-left: 15px;

  i {
    margin-left: 5px;
  }
}

// 表头居中样式
:deep(.el-table th) {
  text-align: center;
}

:deep(.el-table th .cell) {
  text-align: center;
}
</style>
