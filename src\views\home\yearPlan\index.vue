<template>
<div style="background-color: white; padding: 20px">
  <!-- 查询工具栏 -->
  <el-form ref="queryForm" :model="queryForm" :rules="formRules" label-width="150px">
    <!-- 第一行查询条件 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="课题名称" prop="subjectName">
          <el-input v-model="queryForm.subjectName" clearable placeholder="请输入课题名称关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="课题来源" prop="subjectSource">
          <el-input v-model="queryForm.subjectSource" clearable placeholder="请输入课题来源关键字"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="负责人" prop="leader">
          <el-select v-model="queryForm.leader" clearable placeholder="请选择负责人" filterable>
            <el-option v-for="person in oaPersonnelList" :key="person.id"
              :label="`${person.name} (${person.department})`" :value="person.name">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="研究方式" prop="researchMethod">
          <el-select v-model="queryForm.researchMethod" clearable placeholder="请选择研究方式">
            <el-option label="自主" value="自主"></el-option>
            <el-option label="联合" value="联合"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 第二行查询条件 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="年份" prop="year">
          <el-select v-model="queryForm.year" clearable placeholder="请选择年份">
            <el-option label="2024年" value="2024"></el-option>
            <el-option label="2023年" value="2023"></el-option>
            <el-option label="2022年" value="2022"></el-option>
            <el-option label="2021年" value="2021"></el-option>
            <el-option label="2020年" value="2020"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="是否重大项目" prop="isMajorProject">
          <el-select v-model="queryForm.isMajorProject" clearable placeholder="请选择">
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <!-- 操作工具栏 -->
  <div style="margin-bottom: 20px">
    <el-button type="primary" @click="handleImport">导入</el-button>
    <el-button type="primary" @click="handleExport">导出</el-button>
  </div>

  <!-- 表格 -->
  <el-table :data="currentPageData" border stripe style="width: 100%" height="400px"
    @selection-change="handleSelectionChange" v-loading="loading">
    <el-table-column type="selection" width="55"></el-table-column>
    <el-table-column prop="id" label="序号" width="50"></el-table-column>
    <el-table-column prop="subjectName" label="课题名称" show-overflow-tooltip width="auto"></el-table-column>
    <el-table-column prop="subjectSource" label="课题来源" show-overflow-tooltip width="auto"></el-table-column>
    <el-table-column prop="leader" label="负责人" width="auto" align="center"></el-table-column>
    <el-table-column prop="researchMethod" label="研究方式" width="auto" align="center"></el-table-column>
    <el-table-column prop="year" label="年份" width="auto" align="center"></el-table-column>
    <el-table-column prop="isMajorProject" label="重大项目" width="auto" align="center">
      <template slot-scope="scope">
        <el-tag :type="scope.row.isMajorProject === '1' ? 'danger' : 'info'" size="small">
          {{ scope.row.isMajorProject === '1' ? '是' : '否' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="researchPeriod" label="研究期限" width="auto" align="center"></el-table-column>
    <el-table-column prop="mainContent" label="研究内容" width="auto" show-overflow-tooltip></el-table-column>
  </el-table>

  <!-- 分页 -->
  <div style="margin-top: 20px;float:right">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalCount"
    />
  </div>

</div>
</template>

<script>
import * as api from './api/api'



export default {
  name: 'PreProjectInitiation',
  components: {},
  data() {
    return {
      // 静态数据
      tableData: [],
      selectedRows: [],
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      // 加载状态
      loading: false,
      queryForm: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        year: '',
        isMajorProject: ''
      },
      // 原始数据备份，用于查询重置
      originalTableData: []
    }
  },
  computed: {
    // 当前页数据
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.tableData.slice(start, end)
    }
  },
  created() {
    // 初始化数据
    this.loadTableData()
  },
  methods: {


    // 加载表格数据
    async loadTableData() {
      try {
        this.loading = true
        const response = await api.getTableData()

        if (response.code === 200) {
          this.tableData = response.data
          this.originalTableData = JSON.parse(JSON.stringify(response.data))
          this.totalCount = response.total
          this.$message.success('数据加载成功')
        } else {
          this.$message.error(response.message || '数据加载失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('网络请求失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 分页查询数据
    async loadTableDataByPage() {
      try {
        this.loading = true
        const response = await api.getTableDataByPage(
          this.currentPage,
          this.pageSize,
          this.queryForm
        )

        if (response.code === 200) {
          this.tableData = response.data
          this.totalCount = response.total
        } else {
          this.$message.error(response.message || '查询失败')
        }
      } catch (error) {
        console.error('查询数据失败:', error)
        this.$message.error('查询失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 表格选择变化
    handleSelectionChange(val) {
      this.selectedRows = val

      // 优化的控制台输出
      if (val.length === 0) {
        console.log("[表格选择] 已清空所有选择")
      } else {
        console.log(`[表格选择] 已选中 ${val.length} 行数据`)
        console.log("[选中数据] 详情:", val.map(item => ({
          id: item.id || '未知ID',
          subjectName: item.subjectName || '未知课题名称',
          leader: item.leader || '未知负责人',
          year: item.year || '未知年份',
          isMajorProject: item.isMajorProject === '1' ? '重大项目' : '普通项目'
        })))

        // 如果需要查看完整数据，可以展开下面的注释
        // console.log("[完整数据]", val)
      }
    },

    // 查询功能
    async handleQuery() {
      this.currentPage = 1 // 重置到第一页
      await this.loadTableDataByPage()
    },

    // 重置功能
    async handleReset() {
      // 重置查询表单
      this.queryForm = {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        year: '',
        isMajorProject: ''
      }

      // 重置表单验证
      if (this.$refs.queryForm) {
        this.$refs.queryForm.resetFields()
      }

      this.currentPage = 1
      await this.loadTableDataByPage()
      this.$message.success('重置成功')
    },

    // 分页大小变化
    async handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      await this.loadTableDataByPage()
    },
    // 当前页变化
    async handleCurrentChange(val) {
      this.currentPage = val
      await this.loadTableDataByPage()
    },
    // 导入功能
    handleImport() {
      this.$message.info('导入功能开发中...')
    },

    // 导出功能
    handleExport() {
      this.$message.info('导出功能开发中...')
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}

// 查询工具栏样式
.el-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;

  .el-row {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-form-item {
    margin-bottom: 10px;
  }
}

// 查询按钮组样式
.el-button+.el-button {
  margin-left: 10px;
}

// 展开/收起按钮样式
.el-button--text {
  padding: 0;
  margin-left: 15px;

  i {
    margin-left: 5px;
  }
}

// 表头居中样式
:deep(.el-table th) {
  text-align: center;
}

:deep(.el-table th .cell) {
  text-align: center;
}
</style>
