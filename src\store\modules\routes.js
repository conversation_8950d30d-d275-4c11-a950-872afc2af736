/**
 * @description 路由和tab状态管理
 */
import { asyncRoutes, constantRoutes } from '@/router'
import { filterAsyncRoutes } from '@/utils/handleRoutes'

const state = () => ({
  routes: [],
  currentTab: '', // 存储当前选中的tab
})

const getters = {
  routes: (state) => state.routes,
  currentTab: (state) => state.currentTab,
  // 获取当前tab对应的children
  currentTabChildren: (state) => {
    if (!state.currentTab) return []
    const currentRoute = state.routes.find((route) => route.path === state.currentTab)
    return currentRoute && currentRoute.children ? currentRoute.children : []
  },
}

const mutations = {
  setRoutes(state, routes) {
    state.routes = constantRoutes.concat(routes)
  },
  setCurrentTab(state, tab) {
    state.currentTab = tab
  },
}

const actions = {
  async setRoutes({ commit }, permissions) {
    const finallyAsyncRoutes = await filterAsyncRoutes([...asyncRoutes], permissions)
    commit('setRoutes', finallyAsyncRoutes)
    return finallyAsyncRoutes
  },
  async setAllRoutes({ commit }) {
    commit('setRoutes', asyncRoutes)
    return asyncRoutes
  },
  setCurrentTab({ commit }, tab) {
    commit('setCurrentTab', tab)
  },
}

export default { state, getters, mutations, actions }
