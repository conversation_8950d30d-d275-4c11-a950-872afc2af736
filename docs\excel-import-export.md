# Excel 导入导出功能使用说明

## 📋 功能概述

年度计划页面现已支持Excel文件的导入导出功能，使用前端 `xlsx` 库实现，无需后端支持。

## 🚀 安装依赖

如果项目中还没有安装 `xlsx` 库，请运行以下命令：

```bash
# 使用 npm
npm install xlsx

# 使用 yarn
yarn add xlsx

# 使用 pnpm
pnpm add xlsx
```

## ✨ 功能特性

### 1. **导出Excel**
- 支持将当前表格数据导出为Excel文件
- 自动设置列宽，优化显示效果
- 文件名包含当前日期，便于管理
- 支持中文列名和数据

### 2. **导入Excel**
- 支持 `.xlsx` 和 `.xls` 格式
- 智能列名匹配，支持多种列名变体
- 数据验证和错误处理
- 文件大小限制（5MB）
- 自动跳过无效数据行

### 3. **下载模板**
- 提供标准的Excel导入模板
- 包含示例数据和填写说明
- 支持数据验证规则说明

## 📊 支持的数据字段

| 字段名 | 必填 | 说明 | 支持的列名变体 |
|--------|------|------|----------------|
| 课题名称 | ✅ | 项目名称 | 课题名称、项目名称、名称 |
| 课题来源 | ❌ | 资助单位 | 课题来源、来源、资助单位 |
| 负责人 | ✅ | 项目负责人 | 负责人、项目负责人、主持人 |
| 研究方式 | ❌ | 研究类型 | 研究方式、研究类型、方式 |
| 年份 | ❌ | 项目年度 | 年份、年度、立项年份 |
| 是否重大项目 | ❌ | 项目级别 | 是否重大项目、重大项目、是否重点 |
| 研究期限 | ❌ | 项目周期 | 研究期限、项目期限、期限 |
| 主要内容 | ❌ | 研究内容 | 主要内容、研究内容、内容 |
| 预期成果 | ❌ | 预期结果 | 预期成果、预期结果、成果 |

## 🔧 使用方法

### 导出数据
1. 点击 **"导出Excel"** 按钮
2. 系统自动下载包含当前表格数据的Excel文件
3. 文件名格式：`年度计划_YYYY-MM-DD.xlsx`

### 导入数据
1. 点击 **"下载模板"** 按钮，获取标准模板
2. 按照模板格式填写数据
3. 点击 **"导入Excel"** 按钮
4. 选择要导入的Excel文件
5. 系统自动验证并导入有效数据

### 数据验证规则
- **课题名称** 和 **负责人** 为必填字段
- **是否重大项目** 支持：是/否、true/false、1/0、yes/no等
- 文件大小不超过 5MB
- 自动跳过空行和无效数据

## ⚠️ 注意事项

1. **文件格式**：仅支持 `.xlsx` 和 `.xls` 格式
2. **数据验证**：导入时会自动验证必填字段
3. **错误处理**：无效数据会被跳过，控制台会显示详细错误信息
4. **性能考虑**：建议单次导入数据不超过1000条
5. **浏览器兼容**：现代浏览器均支持，IE需要11+版本

## 🐛 常见问题

### Q: 导入时提示"需要安装 xlsx 库"
**A:** 请按照上述安装依赖的步骤安装 `xlsx` 库

### Q: 导入的数据不完整
**A:** 检查Excel文件中的列名是否与支持的列名变体匹配，确保必填字段有数据

### Q: 文件上传失败
**A:** 检查文件格式是否正确，文件大小是否超过5MB限制

### Q: 导出的Excel文件打开乱码
**A:** 确保使用现代版本的Excel或WPS打开文件

## 🔄 技术实现

- **前端库**：xlsx (SheetJS)
- **动态导入**：使用 `import()` 动态加载库，减少初始包大小
- **文件处理**：FileReader API 读取文件内容
- **数据转换**：JSON ↔ Excel 格式转换
- **错误处理**：完善的异常捕获和用户提示

## 📈 扩展功能

未来可以考虑添加：
- 批量数据验证规则
- 自定义导出字段选择
- 导入进度显示
- 数据预览功能
- 多工作表支持
