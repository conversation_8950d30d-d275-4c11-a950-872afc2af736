<template>
<div class="pre-project-approval-container">
  <el-card>
    <div slot="header">
      <span>🔄 立项审批管理</span>
      <el-button v-if="!hasSubmitted" style="float: right; padding: 3px 0" type="text" @click="startSubmission">
        提交立项申请
      </el-button>
    </div>

    <!-- 审批状态显示 -->
    <div class="approval-status" style="margin-bottom: 20px;">
      <el-alert :title="getStatusTitle()" :type="getStatusType()" :description="getStatusDescription()" show-icon
        :closable="false">
      </el-alert>
    </div>

    <!-- 立项信息显示 -->
    <div v-if="hasSubmitted" class="approval-content">
      <!-- 立项基本信息 -->
      <div style="margin-bottom: 30px;">
        <h4 style="margin-bottom: 15px; color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 5px;">
          📋 立项基本信息
        </h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="项目名称">{{ submissionData.projectName }}</el-descriptions-item>
          <el-descriptions-item label="委托单位">{{ submissionData.delegateUnit }}</el-descriptions-item>
          <el-descriptions-item label="项目编号">{{ submissionData.projectNumber }}</el-descriptions-item>
          <el-descriptions-item label="受托人">{{ getTrusteeName(submissionData.trustee) }}</el-descriptions-item>
          <el-descriptions-item label="项目负责人">{{ submissionData.projectLeader }}</el-descriptions-item>
          <el-descriptions-item label="立项时间">{{ submissionData.projectStartTime }}</el-descriptions-item>
          <el-descriptions-item label="预估收入">{{ submissionData.estimatedIncome }}万元</el-descriptions-item>
          <el-descriptions-item label="项目描述" :span="2">
            <div style="white-space: pre-wrap;">{{ submissionData.projectDescription }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 经费预算信息 -->
      <div style="margin-bottom: 30px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
          <h4 style="margin: 0; color: #E6A23C; border-bottom: 2px solid #E6A23C; padding-bottom: 5px;">
            💰 经费预算信息
          </h4>
          <!-- <div class="header-actions">
            <el-button type="primary" size="small" icon="el-icon-edit" @click="editBudget">
              编辑经费
            </el-button>
            <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteBudget">
              删除预算
            </el-button>
          </div> -->
        </div>

        <el-descriptions :column="3" border>
          <el-descriptions-item label="项目收入">{{ budgetData.projectIncome.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="经费预算总额">{{ budgetData.totalBudget.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="项目负责人">{{ budgetData.projectManager }}</el-descriptions-item>
          <el-descriptions-item label="会议费/差旅费">{{ budgetData.meetingTravelFee.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="数据采集费">{{ budgetData.dataCollectionFee.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="资料费">{{ budgetData.materialsFee.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="劳务费">{{ budgetData.laborFee.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="专家费">{{ budgetData.expertFee.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="中介机构费">{{ budgetData.intermediaryFee.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="印刷出版费">{{ budgetData.printingFee.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="税费">{{ budgetData.taxFee.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="其他费用">{{ budgetData.otherFee.toFixed(2) }}万元</el-descriptions-item>
          <el-descriptions-item label="费用合计" :span="3">
            <span style="font-weight: bold; color: #E6A23C; font-size: 16px;">{{ calculateTotalFee().toFixed(2)
            }}万元</span>
          </el-descriptions-item>
          <el-descriptions-item label="预算余额" :span="3">
            <span
              :style="{ fontWeight: 'bold', fontSize: '16px', color: budgetData.totalBudget - calculateTotalFee() >= 0 ? '#67C23A' : '#F56C6C' }">
              {{ (budgetData.totalBudget - calculateTotalFee()).toFixed(2) }}万元
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 审批流程进度 -->
      <div v-if="projectInfo.status === '1' || projectInfo.status === '2'" style="margin-bottom: 30px;">
        <h4 style="margin-bottom: 15px; color: #67C23A; border-bottom: 2px solid #67C23A; padding-bottom: 5px;">
          🔄 审批流程进度
        </h4>
        <el-steps :active="getActiveStep()" direction="vertical" :process-status="getProcessStatus()"
          :finish-status="getFinishStatus()">

          <el-step title="部门负责人审批">
            <div slot="description">
              <p>审批人：张三</p>
              <p v-if="projectInfo.status === '1'" style="color: #67C23A; font-size: 12px;">
                ✓ 已通过 (2024-01-15 10:30)
              </p>
              <p v-else-if="projectInfo.status === '2'" style="color: #67C23A; font-size: 12px;">
                ✓ 已通过 (2024-01-15 10:30)
              </p>
              <p v-else style="color: #E6A23C; font-size: 12px;">⏳ 审批中...</p>
              <p style="color: #909399; font-size: 12px;">
                {{ projectInfo.status === '1' || projectInfo.status === '2' ? '审批意见：同意该课题立项，技术方案可行。' : '等待部门负责人审批' }}
              </p>
            </div>
          </el-step>

          <el-step title="技术总监审批">
            <div slot="description">
              <p>审批人：李四</p>
              <p v-if="projectInfo.status === '1'" style="color: #67C23A; font-size: 12px;">
                ✓ 已通过 (2024-01-16 14:20)
              </p>
              <p v-else-if="projectInfo.status === '2'" style="color: #E6A23C; font-size: 12px;">
                ⏳ 审批中...
              </p>
              <p v-else style="color: #909399; font-size: 12px;">⏸ 等待中...</p>
              <p style="color: #909399; font-size: 12px;">
                {{ projectInfo.status === '1' ? '审批意见：技术路线清晰，预算合理，同意立项。' : '等待技术总监审批' }}
              </p>
            </div>
          </el-step>

          <el-step title="总经理审批">
            <div slot="description">
              <p>审批人：王五</p>
              <p v-if="projectInfo.status === '1'" style="color: #67C23A; font-size: 12px;">
                ✓ 已通过 (2024-01-17 09:15)
              </p>
              <p v-else style="color: #909399; font-size: 12px;">⏸ 等待中...</p>
              <p style="color: #909399; font-size: 12px;">
                {{ projectInfo.status === '1' ? '审批意见：项目具有良好的市场前景，批准立项。' : '等待总经理审批' }}
              </p>
            </div>
          </el-step>
        </el-steps>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state" style="text-align: center; padding: 40px;">
      <i class="el-icon-s-check" style="font-size: 64px; color: #C0C4CC;"></i>
      <p style="color: #909399; margin-top: 16px;">尚未提交立项申请</p>
      <!-- <el-button type="primary" @click="startSubmission">提交立项申请</el-button> -->
    </div>
  </el-card>

  <!-- 提交立项申请对话框 -->
  <el-dialog title="提交立项申请" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false">

    <el-form :model="formData" :rules="formRules" ref="submissionForm" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="formData.projectName" placeholder="请输入项目名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="委托单位" prop="delegateUnit">
            <el-input v-model="formData.delegateUnit" placeholder="请输入委托单位"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目编号" prop="projectNumber">
            <el-input v-model="formData.projectNumber" placeholder="请输入项目编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目负责人" prop="projectLeader">
            <el-input v-model="formData.projectLeader" placeholder="请输入项目负责人"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="立项时间" prop="projectStartTime">
            <el-date-picker v-model="formData.projectStartTime" type="date" placeholder="选择立项时间" style="width: 100%">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预估收入" prop="estimatedIncome">
            <el-input v-model="formData.estimatedIncome" placeholder="请输入预估收入">
              <template slot="append">万元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="项目描述" prop="projectDescription">
        <el-input v-model="formData.projectDescription" type="textarea" :rows="4" placeholder="请详细描述项目内容、目标和意义">
        </el-input>
      </el-form-item>

      <el-form-item label="技术要求" prop="technicalRequirements">
        <el-input v-model="formData.technicalRequirements" type="textarea" :rows="3" placeholder="请描述项目的技术要求和难点">
        </el-input>
      </el-form-item>

      <el-form-item label="交付成果" prop="deliverables">
        <el-input v-model="formData.deliverables" type="textarea" :rows="3" placeholder="请描述项目的交付成果和验收标准">
        </el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitApplication">提交申请</el-button>
    </div>
  </el-dialog>

  <!-- 编辑经费预算弹框 -->
  <el-dialog title="编辑经费预算" :visible.sync="editBudgetDialogVisible" width="80%" :close-on-click-modal="false">
    <el-form :model="editBudgetForm" :rules="editBudgetRules" ref="editBudgetForm" label-width="140px">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          基本信息
        </h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目收入" prop="projectIncome">
              <el-input-number v-model="editBudgetForm.projectIncome" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="经费预算总额" prop="totalBudget">
              <el-input-number v-model="editBudgetForm.totalBudget" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目负责人" prop="projectManager">
              <el-input v-model="editBudgetForm.projectManager" placeholder="请输入项目负责人"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 费用明细 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-money"></i>
          费用明细
        </h3>

        <!-- 第一行费用 -->
        <el-row :gutter="20" style="margin-bottom: 15px;">
          <el-col :span="6">
            <el-form-item label="会议费/差旅费/国际合作与交流费">
              <el-input-number v-model="editBudgetForm.meetingTravelFee" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据采集费">
              <el-input-number v-model="editBudgetForm.dataCollectionFee" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="资料费">
              <el-input-number v-model="editBudgetForm.materialsFee" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="劳务费">
              <el-input-number v-model="editBudgetForm.laborFee" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行费用 -->
        <el-row :gutter="20" style="margin-bottom: 15px;">
          <el-col :span="6">
            <el-form-item label="专家费">
              <el-input-number v-model="editBudgetForm.expertFee" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="中介机构费">
              <el-input-number v-model="editBudgetForm.intermediaryFee" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="印刷出版费">
              <el-input-number v-model="editBudgetForm.printingFee" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="税费">
              <el-input-number v-model="editBudgetForm.taxFee" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：其他费用和合计 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="其他费用">
              <el-input-number v-model="editBudgetForm.otherFee" :precision="2" :min="0" style="width: 100%">
                <template slot="append">万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用合计">
              <el-input :value="editBudgetTotalFee.toFixed(2)" :disabled="true" class="total-input">
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 预算说明 -->
      <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
        <h4 style="margin-bottom: 10px; color: #606266;">预算说明：</h4>
        <p style="color: #909399; line-height: 1.6; margin: 0;">
          本预算表根据项目实际需求制定，包含各项费用明细。
          所有费用均按照公司财务制度和相关规定执行，确保资金使用的合理性和有效性。
        </p>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="editBudgetDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveBudget" :loading="savingBudget">保存</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
export default {
  name: 'PreProjectApproval',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      hasSubmitted: false,
      submissionData: {},
      formData: {
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        projectStartTime: '',
        estimatedIncome: '',
        projectDescription: '',
        technicalRequirements: '',
        deliverables: ''
      },
      formRules: {
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        delegateUnit: [
          { required: true, message: '请输入委托单位', trigger: 'blur' }
        ],
        projectNumber: [
          { required: true, message: '请输入项目编号', trigger: 'blur' }
        ],
        projectLeader: [
          { required: true, message: '请输入项目负责人', trigger: 'blur' }
        ],
        projectStartTime: [
          { required: true, message: '请选择立项时间', trigger: 'change' }
        ],
        estimatedIncome: [
          { required: true, message: '请输入预估收入', trigger: 'blur' },
          { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
        ],
        projectDescription: [
          { required: true, message: '请输入项目描述', trigger: 'blur' },
          { min: 20, message: '项目描述至少20个字符', trigger: 'blur' }
        ]
      },

      // 经费预算相关
      budgetData: {
        projectIncome: 120.0,           // 项目收入
        totalBudget: 100.0,             // 经费预算总额
        projectManager: '张三',          // 项目负责人
        meetingTravelFee: 15.5,         // 会议费/差旅费/国际合作与交流费
        dataCollectionFee: 8.2,         // 数据采集费
        materialsFee: 12.3,             // 资料费
        laborFee: 25.0,                 // 劳务费
        expertFee: 18.5,                // 专家费
        intermediaryFee: 6.8,           // 中介机构费
        printingFee: 4.2,               // 印刷出版费
        taxFee: 3.5,                    // 税费
        otherFee: 8.0                   // 其他费用
      },
      // 受托人选项
      trusteeOptions: [
        { value: 'zhang_san', label: '张三 - 高级工程师' },
        { value: 'li_si', label: '李四 - 项目经理' },
        { value: 'wang_wu', label: '王五 - 技术总监' },
        { value: 'zhao_liu', label: '赵六 - 资深顾问' },
        { value: 'sun_qi', label: '孙七 - 首席专家' },
        { value: 'zhou_ba', label: '周八 - 业务主管' }
      ],
      // 编辑经费预算相关
      editBudgetDialogVisible: false,
      savingBudget: false,
      editBudgetForm: {
        projectIncome: 0,
        totalBudget: 0,
        projectManager: '',
        meetingTravelFee: 0,
        dataCollectionFee: 0,
        materialsFee: 0,
        laborFee: 0,
        expertFee: 0,
        intermediaryFee: 0,
        printingFee: 0,
        taxFee: 0,
        otherFee: 0
      },
      editBudgetRules: {
        projectIncome: [
          { required: true, message: '请输入项目收入', trigger: 'blur' }
        ],
        totalBudget: [
          { required: true, message: '请输入经费预算总额', trigger: 'blur' }
        ],
        projectManager: [
          { required: true, message: '请输入项目负责人', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 编辑表单的费用合计计算
    editBudgetTotalFee() {
      const form = this.editBudgetForm
      return (form.meetingTravelFee || 0) +
        (form.dataCollectionFee || 0) +
        (form.materialsFee || 0) +
        (form.laborFee || 0) +
        (form.expertFee || 0) +
        (form.intermediaryFee || 0) +
        (form.printingFee || 0) +
        (form.taxFee || 0) +
        (form.otherFee || 0)
    }
  },
  created() {
    this.loadSubmissionData()
  },
  methods: {
    // 加载提交数据
    loadSubmissionData() {
      // 根据项目状态判断是否已提交
      console.log(this.projectInfo);

      this.hasSubmitted = ['1', '2'].includes(this.projectInfo.status)
      if (this.hasSubmitted) {
        // 从项目信息中获取已提交的数据
        this.submissionData = {
          projectName: this.projectInfo.projectName || '未知项目',
          delegateUnit: this.projectInfo.delegateUnit || '未知单位',
          projectNumber: this.projectInfo.projectNumber || '未知编号',
          trustee: this.projectInfo.trustee || '',
          projectLeader: this.projectInfo.projectLeader || '未知负责人',
          projectStartTime: this.projectInfo.startTime || '未知时间',
          estimatedIncome: this.projectInfo.estimatedIncome || '0',
          projectDescription: this.projectInfo.mainContent || '暂无描述'
        }
      }

      // 可以在这里调用API获取更详细的立项数据
      // this.fetchPreProjectData()
    },

    // 可选：从API获取立项数据
    async fetchPreProjectData() {
      try {
        // 这里应该调用实际的API
        // const response = await this.$api.getPreProjectData(this.projectInfo.id)
        // this.submissionData = response.data

        console.log('获取立项数据:', this.projectInfo.id)
      } catch (error) {
        console.error('获取立项数据失败:', error)
        this.$message.error('获取立项数据失败')
      }
    },

    // 开始提交申请
    startSubmission() {
      this.dialogVisible = true
      // 预填充一些基础信息
      this.formData = {
        projectName: this.projectInfo.subjectName || '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: this.projectInfo.leader || '',
        projectStartTime: '',
        estimatedIncome: '',
        projectDescription: this.projectInfo.mainContent || '',
        technicalRequirements: '',
        deliverables: ''
      }
    },

    // 提交申请
    submitApplication() {
      this.$refs.submissionForm.validate((valid) => {
        if (valid) {
          this.submissionData = { ...this.formData }
          this.hasSubmitted = true
          this.dialogVisible = false

          // 更新项目状态为审批中
          this.$emit('update-status', '2')

          this.$message.success('立项申请提交成功，等待审批')
        }
      })
    },

    // 获取激活步骤
    getActiveStep() {
      if (this.projectInfo.status === '1') return 3 // 全部完成
      if (this.projectInfo.status === '2') return 1 // 审批中
      return 0
    },

    // 获取进程状态
    getProcessStatus() {
      return this.projectInfo.status === '2' ? 'process' : 'finish'
    },

    // 获取完成状态
    getFinishStatus() {
      return this.projectInfo.status === '1' ? 'success' : 'process'
    },

    // 获取状态标题
    getStatusTitle() {
      if (!this.hasSubmitted) return '未提交立项申请'
      if (this.projectInfo.status === '1') return '立项审批已通过'
      if (this.projectInfo.status === '2') return '立项审批进行中'
      return '立项申请已提交'
    },

    // 获取状态类型
    getStatusType() {
      if (!this.hasSubmitted) return 'info'
      if (this.projectInfo.status === '1') return 'success'
      if (this.projectInfo.status === '2') return 'warning'
      return 'info'
    },

    // 获取状态描述
    getStatusDescription() {
      if (!this.hasSubmitted) return '请提交立项申请以启动审批流程'
      if (this.projectInfo.status === '1') return '恭喜！立项申请已通过所有审批环节'
      if (this.projectInfo.status === '2') return '立项申请正在审批中，请耐心等待'
      return '立项申请已提交，等待审批'
    },

    // ========== 经费预算相关方法 ==========

    // 计算费用合计
    calculateTotalFee() {
      const data = this.budgetData
      return data.meetingTravelFee +
        data.dataCollectionFee +
        data.materialsFee +
        data.laborFee +
        data.expertFee +
        data.intermediaryFee +
        data.printingFee +
        data.taxFee +
        data.otherFee
    },

    // 编辑经费预算
    editBudget() {
      // 复制当前预算数据到编辑表单
      this.editBudgetForm = { ...this.budgetData }
      this.editBudgetDialogVisible = true
    },

    // 保存经费预算
    saveBudget() {
      this.$refs.editBudgetForm.validate((valid) => {
        if (valid) {
          this.savingBudget = true

          // 模拟保存
          setTimeout(() => {
            // 更新主预算数据
            this.budgetData = { ...this.editBudgetForm }

            this.savingBudget = false
            this.editBudgetDialogVisible = false
            this.$message.success('经费预算保存成功')
          }, 1500)
        }
      })
    },

    // 删除预算
    deleteBudget() {
      this.$confirm('确定要删除当前预算表吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置预算数据
        this.budgetData = {
          projectIncome: 0,
          totalBudget: 0,
          projectManager: '',
          meetingTravelFee: 0,
          dataCollectionFee: 0,
          materialsFee: 0,
          laborFee: 0,
          expertFee: 0,
          intermediaryFee: 0,
          printingFee: 0,
          taxFee: 0,
          otherFee: 0
        }
        this.$message.success('预算表已删除')
      })
    },

    // 获取受托人姓名
    getTrusteeName(value) {
      const trustee = this.trusteeOptions.find(option => option.value === value)
      return trustee ? trustee.label : value || '未设置'
    }
  }
}
</script>

<style scoped>
.pre-project-approval-container {
  height: 100%;
}

.approval-status {
  margin-bottom: 20px;
}

.approval-content {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  text-align: right;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 预算统计样式 */
.budget-summary {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

/* 预算表格样式 */
.el-table {
  margin-bottom: 20px;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .budget-summary {
    padding: 15px;
  }

  .header-actions {
    flex-direction: column;
    gap: 5px;
  }

  .header-actions .el-button {
    width: 100%;
  }
}

/* 编辑经费预算弹框样式 */
.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 2px solid #e1e8ed;
  padding-bottom: 10px;
}

.section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.budget-summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.budget-summary-item .label {
  font-weight: 500;
  color: #606266;
}

.budget-summary-item .value {
  font-weight: bold;
  color: #409eff;
}

/* 编辑表格样式 */
.el-table .el-input-number {
  width: 100%;
}

.el-table .el-select {
  width: 100%;
}

/* 预算表格样式 */
.budget-table-container {
  margin-top: 20px;
}

.budget-row {
  margin-bottom: 20px;
}

.budget-item {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  height: 100%;
  border: 1px solid #e9ecef;
  transition: all 0.3s;
}

.budget-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.budget-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
  line-height: 1.4;
}

.budget-value {
  display: flex;
  align-items: baseline;
  justify-content: flex-end;
}

.budget-amount {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-right: 4px;
}

.budget-amount.highlight {
  color: #409eff;
  font-size: 20px;
}

.budget-unit {
  font-size: 12px;
  color: #909399;
}

/* 合计项特殊样式 */
.total-item {
  background-color: #e8f4fd;
  border-color: #409eff;
}

.total-label {
  color: #409eff;
  font-weight: bold;
  font-size: 15px;
}

.total-value .budget-amount {
  color: #409eff;
  font-size: 20px;
  font-weight: bold;
}

/* 编辑表单中的合计输入框 */
.total-input .el-input__inner {
  background-color: #e9ecef;
  font-weight: bold;
  color: #495057;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .budget-row .el-col {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .budget-item {
    padding: 12px;
  }

  .budget-amount {
    font-size: 16px;
  }

  .budget-amount.highlight {
    font-size: 18px;
  }

  .total-value .budget-amount {
    font-size: 18px;
  }
}
</style>
