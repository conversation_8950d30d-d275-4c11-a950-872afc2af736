<template>
<div class="project-report">
  <!-- 研究报告列表 -->
  <el-card>
    <div slot="header" class="card-header">
      <span>📊 研究报告数据</span>
      <el-button type="primary" @click="addReport">新增报告</el-button>
    </div>

    <div class="report-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="研究报告总数" :value="reportList.length" suffix="份"></el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="已完成报告" :value="completedReports" suffix="份"></el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="进行中报告" :value="inProgressReports" suffix="份"></el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="待审核报告" :value="pendingReports" suffix="份"></el-statistic>
        </el-col>
      </el-row>
    </div>

    <div class="table-container">
      <el-table :data="reportList" style="width: 100%" border>
        <el-table-column prop="reportTitle" label="研究报告标题" min-width="200"></el-table-column>
        <el-table-column prop="projectLeader" label="项目负责人" width="120"></el-table-column>
        <el-table-column prop="reportType" label="报告类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getReportTypeTagType(scope.row.reportType)">
              {{ getReportTypeText(scope.row.reportType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publishChannel" label="拟发布渠道" width="150">
          <template slot-scope="scope">
            <el-tag size="small">{{ scope.row.publishChannel }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120"></el-table-column>
        <el-table-column label="操作" width="300">
          <template slot-scope="scope">
            <el-button size="mini" @click="viewReport(scope.row)">查看</el-button>
            <el-button size="mini" @click="editReport(scope.row)">编辑</el-button>
            <el-button size="mini" @click="submitForApproval(scope.row)">
              {{ getApprovalButtonText(scope.row.status) }}
            </el-button>
            <el-button size="mini" @click="downloadReport(scope.row)">下载</el-button>
            <el-button size="mini" @click="deleteReport(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-card>

  <!-- 查看报告详情对话框 -->
  <el-dialog title="研究报告详情" :visible.sync="viewDialogVisible" width="80%">
    <el-descriptions v-if="currentReport" :column="2" border>
      <el-descriptions-item label="报告标题" :span="2">{{ currentReport.reportTitle }}</el-descriptions-item>
      <el-descriptions-item label="项目负责人">{{ currentReport.projectLeader }}</el-descriptions-item>
      <el-descriptions-item label="报告类型">
        <el-tag :type="getReportTypeTagType(currentReport.reportType)">
          {{ getReportTypeText(currentReport.reportType) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="拟发布渠道">{{ currentReport.publishChannel }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="getStatusTagType(currentReport.status)">
          {{ getStatusText(currentReport.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="报告名称">{{ currentReport.reportName }}</el-descriptions-item>
      <el-descriptions-item label="报告大纲摘要" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.reportOutline }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="报告作者及分工" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.authorDivision }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="编写时间附件" :span="2">
        <div v-if="currentReport.attachments && currentReport.attachments.length > 0">
          <el-tag v-for="file in currentReport.attachments" :key="file.name" style="margin-right: 5px;">
            {{ file.name }}
          </el-tag>
        </div>
        <span v-else>暂无附件</span>
      </el-descriptions-item>
      <el-descriptions-item v-if="currentReport.notes" label="备注" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.notes }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <div slot="footer" class="dialog-footer">
      <el-button @click="viewDialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="editReport(currentReport)">编辑</el-button>
      <el-button type="success" @click="downloadReport(currentReport)">下载</el-button>
    </div>
  </el-dialog>

  <!-- 添加/编辑报告对话框 -->
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false">
    <el-form :model="formData" :rules="formRules" ref="reportForm" label-width="140px">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          报告基本信息
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="研究报告标题" prop="reportTitle">
              <el-input v-model="formData.reportTitle" placeholder="请输入研究报告标题" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人" prop="projectLeader">
              <el-input v-model="formData.projectLeader" placeholder="请输入项目负责人" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告类型" prop="reportType">
              <el-select v-model="formData.reportType" placeholder="请选择报告类型" style="width: 100%">
                <el-option label="研究报告定稿" value="final"></el-option>
                <el-option label="研究成果" value="result"></el-option>
                <el-option label="阶段性报告" value="stage"></el-option>
                <el-option label="其他" value="other"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="拟发布渠道" prop="publishChannel">
              <el-input v-model="formData.publishChannel" placeholder="请输入拟发布渠道（自媒体/外部媒体/其他）" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 报告内容 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-edit-outline"></i>
          报告内容
        </h3>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="报告名称" prop="reportName">
              <el-input v-model="formData.reportName" placeholder="请输入报告名称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="报告大纲摘要" prop="reportOutline">
          <el-input v-model="formData.reportOutline" type="textarea" :rows="4" placeholder="请输入报告大纲摘要" clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="报告作者及分工" prop="authorDivision">
          <el-input v-model="formData.authorDivision" type="textarea" :rows="3" placeholder="请输入报告作者及分工情况（撰稿人/审校人）"
            clearable>
          </el-input>
        </el-form-item>
      </div>

      <!-- 附件上传 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-folder-opened"></i>
          编写时间附件
        </h3>
        <el-form-item label="附件上传" prop="attachments">
          <el-upload class="upload-demo" :action="uploadUrl" :on-success="handleAttachmentSuccess"
            :on-remove="handleAttachmentRemove" :file-list="attachmentFileList" :limit="10"
            accept=".pdf,.doc,.docx,.xls,.xlsx">
            <el-button size="small" type="primary" icon="el-icon-upload">上传研究报告定稿</el-button>
            <div slot="tip" class="el-upload__tip">支持 PDF、Word、Excel 格式，最多10个文件</div>
          </el-upload>
        </el-form-item>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-more"></i>
          其他信息
        </h3>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" type="textarea" :rows="3" placeholder="请输入其他备注信息" clearable>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveReport">保存</el-button>
    </div>
  </el-dialog>

  <!-- 提交审批对话框 -->
  <el-dialog title="提交研究报告审批" :visible.sync="approvalDialogVisible" width="70%" :close-on-click-modal="false">
    <el-form :model="approvalFormData" :rules="approvalFormRules" ref="approvalForm" label-width="120px">
      <!-- 报告基本信息（只读） -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          报告基本信息
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告标题">
              <el-input :value="approvalFormData.reportTitle" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人">
              <el-input :value="approvalFormData.projectLeader" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告类型">
              <el-input :value="getReportTypeText(approvalFormData.reportType)" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="拟发布渠道">
              <el-input :value="approvalFormData.publishChannel" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 审批流程选择 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-s-order"></i>
          审批流程
        </h3>
        <el-form-item label="选择流程" prop="processName">
          <el-select v-model="approvalFormData.processName" placeholder="请选择审批流程" style="width: 100%"
            @change="handleProcessChange">
            <el-option v-for="process in approvalProcessOptions" :key="process.name" :label="process.name"
              :value="process.name">
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 流程步骤预览 -->
        <div v-if="selectedApprovalProcess" class="process-preview">
          <h4>审批流程预览：</h4>
          <el-steps :active="0" finish-status="success" align-center>
            <el-step v-for="(step, index) in selectedApprovalProcess.steps" :key="index" :title="step.name"
              :description="step.approver">
            </el-step>
          </el-steps>
        </div>
      </div>

      <!-- 提交说明 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-edit-outline"></i>
          提交说明
        </h3>
        <el-form-item label="提交说明" prop="submitReason">
          <el-input v-model="approvalFormData.submitReason" type="textarea" :rows="4" placeholder="请输入提交审批的说明"
            clearable>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="approvalDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmSubmitApproval" :loading="submittingApproval">提交审批</el-button>
    </div>
  </el-dialog>

  <!-- 审批进度查看对话框 -->
  <el-dialog title="审批进度" :visible.sync="progressDialogVisible" width="70%">
    <div v-if="currentApprovalProgress">
      <el-steps :active="currentApprovalProgress.currentStep" finish-status="success" process-status="process">
        <el-step v-for="(step, index) in currentApprovalProgress.steps" :key="index" :title="step.name"
          :description="step.approver" :status="getStepStatus(index, currentApprovalProgress.currentStep)">
          <template slot="description">
            <div>
              <div>{{ step.approver }}</div>
              <div v-if="step.approvalTime" style="font-size: 12px; color: #999;">
                {{ step.approvalTime }}
              </div>
              <div v-if="step.comment" style="font-size: 12px; color: #666;">
                意见：{{ step.comment }}
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="progressDialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
export default {
  name: 'ProjectReport',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      viewDialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      editId: null,
      currentReport: null,
      reportList: [],

      // 审批相关
      approvalDialogVisible: false,
      progressDialogVisible: false,
      submittingApproval: false,
      selectedApprovalProcess: null,
      currentApprovalProgress: null,
      formData: {
        reportTitle: '',        // 研究报告标题
        projectLeader: '',      // 项目负责人
        reportType: '',         // 报告类型
        publishChannel: '',     // 拟发布渠道
        reportName: '',         // 报告名称
        reportOutline: '',      // 报告大纲摘要
        authorDivision: '',     // 报告作者及分工
        attachments: [],        // 附件
        notes: ''               // 备注
      },

      // 文件上传相关
      uploadUrl: '/api/upload',
      attachmentFileList: [],

      // 审批表单数据
      approvalFormData: {
        reportTitle: '',
        projectLeader: '',
        reportType: '',
        publishChannel: '',
        processName: '',
        submitReason: ''
      },

      // 审批流程选项
      approvalProcessOptions: [
        {
          name: '标准审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '技术总监审批', approver: '李四' },
            { name: '总经理审批', approver: '王五' }
          ]
        },
        {
          name: '快速审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '总经理审批', approver: '王五' }
          ]
        },
        {
          name: '专家审批流程',
          steps: [
            { name: '技术专家审批', approver: '专家组' },
            { name: '学术委员会审批', approver: '学术委员会' },
            { name: '总经理审批', approver: '王五' }
          ]
        }
      ],

      formRules: {
        reportTitle: [
          { required: true, message: '请输入研究报告标题', trigger: 'blur' },
          { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        projectLeader: [
          { required: true, message: '请输入项目负责人', trigger: 'blur' }
        ],
        reportType: [
          { required: true, message: '请选择报告类型', trigger: 'change' }
        ],
        publishChannel: [
          { required: true, message: '请输入拟发布渠道', trigger: 'blur' }
        ],
        reportName: [
          { required: true, message: '请输入报告名称', trigger: 'blur' }
        ],
        reportOutline: [
          { required: true, message: '请输入报告大纲摘要', trigger: 'blur' },
          { min: 20, message: '报告大纲摘要至少需要20个字符', trigger: 'blur' }
        ],
        authorDivision: [
          { required: true, message: '请输入报告作者及分工', trigger: 'blur' }
        ]
      },

      // 审批表单验证规则
      approvalFormRules: {
        processName: [
          { required: true, message: '请选择审批流程', trigger: 'change' }
        ],
        submitReason: [
          { required: true, message: '请输入提交说明', trigger: 'blur' },
          { min: 10, message: '提交说明至少需要10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    completedReports() {
      return this.reportList.filter(report => ['completed', 'approved'].includes(report.status)).length
    },
    inProgressReports() {
      return this.reportList.filter(report => ['in-progress', 'reviewing'].includes(report.status)).length
    },
    pendingReports() {
      return this.reportList.filter(report => ['pending', 'draft'].includes(report.status)).length
    }
  },
  mounted() {
    this.loadReportList()
  },
  methods: {
    // 加载报告列表
    loadReportList() {
      // 模拟数据
      this.reportList = [
        {
          id: 1,
          reportTitle: '盆地低渗透油气田含油污水回用处理技术研究报告',
          projectLeader: '田野',
          reportType: 'final',
          publishChannel: '学术期刊',
          reportName: '含油污水处理技术研究',
          reportOutline: '本报告详细阐述了盆地低渗透油气田含油污水回用处理技术的研究成果...',
          authorDivision: '田野（主要撰稿人）、李明（数据分析）、王芳（技术审核）',
          status: 'approved',
          createTime: '2024-01-15',
          attachments: [
            { name: '研究报告定稿.pdf', url: '/files/report1.pdf' }
          ],
          approvalProcess: {
            processName: '标准审批流程',
            submitReason: '研究报告已完成，请审批发布',
            currentStep: 3,
            steps: [
              { name: '部门负责人审批', approver: '张三', status: 'approved', approvalTime: '2024-01-16 10:30', comment: '报告质量良好，同意通过' },
              { name: '技术总监审批', approver: '李四', status: 'approved', approvalTime: '2024-01-17 14:20', comment: '技术内容准确，建议发布' },
              { name: '总经理审批', approver: '王五', status: 'approved', approvalTime: '2024-01-18 09:15', comment: '同意发布' }
            ]
          }
        },
        {
          id: 2,
          reportTitle: '新能源汽车动力电池回收利用技术研究报告',
          projectLeader: '王芳',
          reportType: 'stage',
          publishChannel: '内部发布',
          reportName: '动力电池回收技术研究',
          reportOutline: '本报告研究了新能源汽车动力电池回收利用的关键技术...',
          authorDivision: '王芳（主要撰稿人）、李明（技术支持）',
          status: 'reviewing',
          createTime: '2024-01-20',
          attachments: [
            { name: '阶段性研究报告.pdf', url: '/files/report2.pdf' }
          ],
          approvalProcess: {
            processName: '快速审批流程',
            submitReason: '阶段性研究报告，需要审批后进行下一步工作',
            currentStep: 1,
            steps: [
              { name: '部门负责人审批', approver: '张三', status: 'approved', approvalTime: '2024-01-21 11:00', comment: '阶段性成果不错' },
              { name: '总经理审批', approver: '王五', status: 'pending', approvalTime: null, comment: '' }
            ]
          }
        },
        {
          id: 3,
          reportTitle: '智能制造技术应用研究报告',
          projectLeader: '李明',
          reportType: 'final',
          publishChannel: '行业期刊',
          reportName: '智能制造技术应用',
          reportOutline: '本报告分析了智能制造技术在传统制造业中的应用前景...',
          authorDivision: '李明（主要撰稿人）、田野（数据分析）',
          status: 'draft',
          createTime: '2024-01-22',
          attachments: []
        }
      ]
    },

    // 新增报告
    addReport() {
      this.dialogTitle = '新增研究报告'
      this.isEdit = false
      this.editId = null
      this.formData = {
        reportTitle: '',
        projectLeader: '',
        reportType: '',
        publishChannel: '',
        reportName: '',
        reportOutline: '',
        authorDivision: '',
        attachments: [],
        notes: ''
      }
      this.attachmentFileList = []
      this.dialogVisible = true
    },

    // 编辑报告
    editReport(report) {
      this.dialogTitle = '编辑研究报告'
      this.isEdit = true
      this.editId = report.id
      this.formData = { ...report }
      this.attachmentFileList = report.attachments || []
      this.dialogVisible = true
      this.viewDialogVisible = false
    },

    // 查看报告
    viewReport(report) {
      this.currentReport = report
      this.viewDialogVisible = true
    },

    // 保存报告
    saveReport() {
      this.$refs.reportForm.validate((valid) => {
        if (valid) {
          const reportData = {
            ...this.formData,
            status: 'draft',
            createTime: new Date().toISOString().split('T')[0]
          }

          if (this.isEdit) {
            // 编辑现有报告
            const index = this.reportList.findIndex(item => item.id === this.editId)
            if (index > -1) {
              this.reportList.splice(index, 1, { ...reportData, id: this.editId })
            }
            this.$message.success('报告更新成功')
          } else {
            // 新增报告
            reportData.id = Date.now()
            this.reportList.push(reportData)
            this.$message.success('报告创建成功')
          }

          this.dialogVisible = false
        }
      })
    },

    // 下载报告
    downloadReport(report) {
      this.$message.success(`正在下载报告：${report.reportTitle}`)
    },

    // 删除报告
    deleteReport(report) {
      this.$confirm(`确定要删除报告"${report.reportTitle}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.reportList.findIndex(item => item.id === report.id)
        if (index > -1) {
          this.reportList.splice(index, 1)
          this.$message.success('删除成功')
        }
      })
    },

    // 附件上传成功
    handleAttachmentSuccess(response, file, fileList) {
      this.attachmentFileList = fileList
      this.formData.attachments = fileList.map(f => ({
        name: f.name,
        url: f.response?.url || f.url
      }))
      this.$message.success('附件上传成功')
    },

    // 附件移除
    handleAttachmentRemove(file, fileList) {
      this.attachmentFileList = fileList
      this.formData.attachments = fileList.map(f => ({
        name: f.name,
        url: f.response?.url || f.url
      }))
    },

    // 获取报告类型标签类型
    getReportTypeTagType(type) {
      const typeMap = {
        'final': 'success',
        'result': 'primary',
        'stage': 'warning',
        'other': 'info'
      }
      return typeMap[type] || 'info'
    },

    // 获取报告类型文本
    getReportTypeText(type) {
      const textMap = {
        'final': '研究报告定稿',
        'result': '研究成果',
        'stage': '阶段性报告',
        'other': '其他'
      }
      return textMap[type] || '未知'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'draft': 'info',
        'in-progress': 'warning',
        'pending': 'primary',
        'reviewing': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'completed': 'success'
      }
      return typeMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'draft': '草稿',
        'in-progress': '进行中',
        'pending': '待审核',
        'reviewing': '审批中',
        'approved': '已通过',
        'rejected': '已拒绝',
        'completed': '已完成'
      }
      return textMap[status] || '未知'
    },

    // 提交审批
    submitForApproval(report) {
      // 检查报告状态，如果已经在审批中，则显示审批进度
      if (report.approvalProcess && ['reviewing', 'approved', 'rejected'].includes(report.status)) {
        this.viewApprovalProgress(report)
        return
      }

      // 否则打开提交审批对话框
      this.approvalFormData = {
        reportTitle: report.reportTitle,
        projectLeader: report.projectLeader,
        reportType: report.reportType,
        publishChannel: report.publishChannel,
        processName: '',
        submitReason: ''
      }
      this.selectedApprovalProcess = null
      this.approvalDialogVisible = true
    },

    // 处理流程选择变化
    handleProcessChange(processName) {
      this.selectedApprovalProcess = this.approvalProcessOptions.find(p => p.name === processName)
    },

    // 确认提交审批
    confirmSubmitApproval() {
      this.$refs.approvalForm.validate((valid) => {
        if (valid) {
          this.submittingApproval = true

          // 模拟提交审批
          setTimeout(() => {
            // 更新报告状态
            const report = this.reportList.find(r => r.reportTitle === this.approvalFormData.reportTitle)
            if (report) {
              report.status = 'reviewing'
              report.approvalProcess = {
                processName: this.approvalFormData.processName,
                submitReason: this.approvalFormData.submitReason,
                currentStep: 0,
                steps: this.selectedApprovalProcess.steps.map(step => ({
                  ...step,
                  status: 'pending',
                  approvalTime: null,
                  comment: ''
                }))
              }
            }

            this.submittingApproval = false
            this.approvalDialogVisible = false
            this.$message.success('审批提交成功')
          }, 1500)
        }
      })
    },

    // 查看审批进度
    viewApprovalProgress(report) {
      if (report.approvalProcess) {
        this.currentApprovalProgress = report.approvalProcess
        this.progressDialogVisible = true
      } else {
        this.$message.warning('该报告尚未提交审批')
      }
    },

    // 获取步骤状态
    getStepStatus(stepIndex, currentStep) {
      if (stepIndex < currentStep) {
        return 'finish'
      } else if (stepIndex === currentStep) {
        return 'process'
      } else {
        return 'wait'
      }
    },

    // 获取审批按钮文本
    getApprovalButtonText(status) {
      const textMap = {
        'draft': '提交审批',
        'reviewing': '审批进度',
        'approved': '审批进度',
        'rejected': '审批进度',
        'completed': '审批进度'
      }
      return textMap[status] || '提交审批'
    }
  }
}
</script>

<style scoped lang="scss">
.project-report {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .report-stats {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .table-container {
    margin-top: 20px;
  }

  .dialog-footer {
    text-align: right;
  }

  /* 表单区域样式 */
  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fafbfc;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    border-bottom: 2px solid #e1e8ed;
    padding-bottom: 10px;
  }

  .section-title i {
    margin-right: 8px;
    font-size: 18px;
    color: #409eff;
  }

  /* 上传组件样式 */
  .upload-demo {
    width: 100%;
  }

  .upload-demo .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 5px;
  }

  /* 审批流程预览样式 */
  .process-preview {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .process-preview h4 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 14px;
  }

  /* 审批进度样式 */
  .el-steps {
    margin: 20px 0;
  }

  .el-step__description {
    padding-right: 10px;
    line-height: 1.4;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .form-section {
      padding: 15px;
      margin-bottom: 20px;
    }

    .report-stats {
      padding: 15px;
    }

    .process-preview {
      padding: 10px;
    }

    .el-steps {
      margin: 15px 0;
    }
  }
}
</style>
