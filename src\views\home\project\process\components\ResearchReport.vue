<template>
<div>
  <el-card>
    <div slot="header" class="card-header">
      <span>📋 研究报告管理</span>
      <div class="header-actions">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="addReport">
          新增报告
        </el-button>
      </div>
    </div>

    <!-- 报告统计 -->
    <div class="report-stats" style="margin-bottom: 30px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总报告数" :value="reportList.length" suffix="份">
            <template slot="prefix">
              <i class="el-icon-document" style="color: #409EFF"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="已完成" :value="completedReports" suffix="份">
            <template slot="prefix">
              <i class="el-icon-circle-check" style="color: #67C23A"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="进行中" :value="inProgressReports" suffix="份">
            <template slot="prefix">
              <i class="el-icon-loading" style="color: #E6A23C"></i>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="待处理" :value="pendingReports" suffix="份">
            <template slot="prefix">
              <i class="el-icon-time" style="color: #909399"></i>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>

    <!-- 报告列表 -->
    <div class="report-list">
      <el-table :data="reportList" border empty-text="暂无报告数据" header-align="center">
        <el-table-column prop="projectNameNumber" label="项目名称/编号" width="auto" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reportName" label="报告名称" width="auto" show-overflow-tooltip></el-table-column>
        <el-table-column prop="achievementName" label="成果名称" width="auto" show-overflow-tooltip></el-table-column>
        <el-table-column prop="leader" label="负责人" align="center" width="80"></el-table-column>
        <el-table-column prop="writeTime" label="编写时间" show-overflow-tooltip width="auto"></el-table-column>
        <el-table-column prop="submitTime" label="提交时间" show-overflow-tooltip width="auto"></el-table-column>
        <el-table-column label="提交方式" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.submitMethod === 'online'" type="success" size="small">
              <i class="el-icon-upload"></i> 在线提交
            </el-tag>
            <el-tag v-else-if="scope.row.submitMethod === 'email'" type="primary" size="small">
              <i class="el-icon-message"></i> 邮件提交
            </el-tag>
            <el-tag v-else-if="scope.row.submitMethod === 'offline'" type="warning" size="small">
              <i class="el-icon-document-copy"></i> 现场提交
            </el-tag>
            <el-tag v-else type="info" size="small">
              <i class="el-icon-question"></i> 其他方式
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审批状态" align="center" width="100">
          <template slot-scope="scope">
            <el-tag :type="getReportStatusTagType(scope.row.status)" size="small">
              <i :class="getStatusIcon(scope.row.status)"></i>
              {{ getReportStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="150" class-name="operation-column">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button class="responsive-btn" @click="viewReport(scope.row)">查看</el-button>
              <el-button class="responsive-btn" @click="editReport(scope.row)">编辑</el-button>
              <el-button class="responsive-btn" @click="handleApprovalAction(scope.row)">
                {{ getApprovalButtonText(scope.row.status) }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-if="reportList.length === 0" class="empty-state" style="text-align: center; padding: 40px;">
      <i class="el-icon-document" style="font-size: 64px; color: #C0C4CC;"></i>
      <p style="color: #909399; margin-top: 16px;">暂无报告数据</p>
      <el-button type="primary" @click="addReport">新增报告</el-button>
    </div>
  </el-card>

  <!-- 新增/编辑报告对话框 -->
  <el-dialog :title="reportDialogTitle" :visible.sync="reportDialogVisible" width="80%">
    <el-form ref="reportForm" :model="reportFormData" :rules="reportFormRules" label-width="140px">

      <h3 class="section-title">
        <i class="el-icon-document"></i>
        研究报告定稿
      </h3>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目名称/编号" prop="projectNameNumber">
            <el-input v-model="reportFormData.projectNameNumber" placeholder="请输入项目名称/编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报告名称" prop="reportName">
            <el-input v-model="reportFormData.reportName" placeholder="请输入报告名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="报告概要" prop="reportSummary">
        <el-input v-model="reportFormData.reportSummary" type="textarea" :rows="3" placeholder="请输入报告概要"></el-input>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="成果名称" prop="achievementName">
            <el-input v-model="reportFormData.achievementName" placeholder="请输入成果名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="leader">
            <el-input v-model="reportFormData.leader" placeholder="请输入负责人"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="成果概要" prop="achievementSummary">
        <el-input v-model="reportFormData.achievementSummary" type="textarea" :rows="3"
          placeholder="请输入成果概要"></el-input>
      </el-form-item>

      <h3 class="section-title">
        <i class="el-icon-user"></i>
        报告作者及分工
      </h3>

      <el-form-item label="报告作者及分工" prop="authorDivision">
        <el-input v-model="reportFormData.authorDivision" type="textarea" :rows="3"
          placeholder="请输入撰稿人/审校人及其分工情况"></el-input>
      </el-form-item>

      <h3 class="section-title">
        <i class="el-icon-time"></i>
        编写时间附件
      </h3>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="编写时间" prop="writeTime">
            <el-date-picker v-model="reportFormData.writeTime" type="datetime" placeholder="选择编写时间"
              format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="提交时间" prop="submitTime">
            <el-date-picker v-model="reportFormData.submitTime" type="datetime" placeholder="选择提交时间"
              format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="提交方式" prop="submitMethod">
        <el-select v-model="reportFormData.submitMethod" placeholder="请选择提交方式" style="width: 100%">
          <el-option label="在线提交" value="online"></el-option>
          <el-option label="邮件提交" value="email"></el-option>
          <el-option label="现场提交" value="offline"></el-option>
          <el-option label="其他方式" value="other"></el-option>
        </el-select>
      </el-form-item>

      <h3 class="section-title">
        <i class="el-icon-paperclip"></i>
        附件上传
      </h3>

      <el-form-item label="报告终稿">
        <el-upload class="upload-demo" :action="uploadUrl" :on-success="handleReportAttachmentSuccess"
          :on-remove="handleReportAttachmentRemove" :file-list="reportAttachmentFileList" :limit="1"
          :on-exceed="handleReportExceed" accept=".pdf,.doc,.docx">
          <el-button size="small" type="primary">
            <i class="el-icon-upload2"></i> 上传报告终稿
          </el-button>
          <div slot="tip" class="el-upload__tip">
            <i class="el-icon-info"></i> 支持上传PDF、Word格式文件，单个文件不超过50MB，仅支持上传一个文件
          </div>
        </el-upload>
      </el-form-item>

      <el-form-item label="最终成果上传">
        <el-upload class="upload-demo" :action="uploadUrl" :on-success="handleAchievementAttachmentSuccess"
          :on-remove="handleAchievementAttachmentRemove" :file-list="achievementAttachmentFileList" :limit="1"
          :on-exceed="handleAchievementExceed" accept=".pdf,.doc,.docx,.zip,.rar">
          <el-button size="small" type="success">
            <i class="el-icon-upload2"></i> 上传最终成果
          </el-button>
          <div slot="tip" class="el-upload__tip">
            <i class="el-icon-info"></i> 支持上传PDF、Word、压缩包等格式，单个文件不超过50MB，仅支持上传一个文件
          </div>
        </el-upload>
      </el-form-item>

      <el-form-item label="备注">
        <el-input v-model="reportFormData.notes" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="reportDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveReport">保存</el-button>
    </div>
  </el-dialog>

  <!-- 查看报告详情对话框 -->
  <el-dialog title="报告详情" :visible.sync="viewReportDialogVisible" width="70%">
    <el-descriptions v-if="currentReport" :column="2" border>
      <el-descriptions-item label="项目名称/编号" :span="2">{{ currentReport.projectNameNumber }}</el-descriptions-item>
      <el-descriptions-item label="报告名称" :span="2">{{ currentReport.reportName }}</el-descriptions-item>
      <el-descriptions-item label="报告概要" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.reportSummary }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="成果名称">{{ currentReport.achievementName }}</el-descriptions-item>
      <el-descriptions-item label="负责人">{{ currentReport.leader }}</el-descriptions-item>
      <el-descriptions-item label="成果概要" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.achievementSummary }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="报告作者及分工" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.authorDivision }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="编写时间">{{ currentReport.writeTime }}</el-descriptions-item>
      <el-descriptions-item label="提交时间">{{ currentReport.submitTime }}</el-descriptions-item>
      <el-descriptions-item label="提交方式">
        <el-tag v-if="currentReport.submitMethod === 'online'" type="success">
          <i class="el-icon-upload"></i> 在线提交
        </el-tag>
        <el-tag v-else-if="currentReport.submitMethod === 'email'" type="primary">
          <i class="el-icon-message"></i> 邮件提交
        </el-tag>
        <el-tag v-else-if="currentReport.submitMethod === 'offline'" type="warning">
          <i class="el-icon-document-copy"></i> 现场提交
        </el-tag>
        <el-tag v-else type="info">
          <i class="el-icon-question"></i> 其他方式
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="审批状态">
        <el-tag :type="getReportStatusTagType(currentReport.status)">
          <i :class="getStatusIcon(currentReport.status)"></i>
          {{ getReportStatusText(currentReport.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="备注" :span="2">
        <div style="white-space: pre-wrap;">{{ currentReport.notes || '无' }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <div slot="footer" class="dialog-footer">
      <el-button @click="viewReportDialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="editReport(currentReport)">编辑</el-button>
    </div>
  </el-dialog>

  <!-- 提交审批对话框 -->
  <el-dialog title="提交审批" :visible.sync="approvalDialogVisible" width="60%">
    <el-form ref="approvalForm" :model="approvalFormData" :rules="approvalFormRules" label-width="120px">
      <el-form-item label="报告名称" prop="reportName">
        <el-input v-model="approvalFormData.reportName" readonly></el-input>
      </el-form-item>

      <el-form-item label="审批流程" prop="processName">
        <el-select v-model="approvalFormData.processName" placeholder="请选择审批流程" style="width: 100%"
          @change="handleProcessChange">
          <el-option v-for="process in approvalProcessOptions" :key="process.name" :label="process.name"
            :value="process.name"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="提交理由" prop="submitReason">
        <el-input v-model="approvalFormData.submitReason" type="textarea" :rows="3" placeholder="请输入提交审批的理由"></el-input>
      </el-form-item>

      <!-- 审批流程预览 -->
      <el-form-item label="流程预览" v-if="selectedApprovalProcess">
        <el-steps direction="vertical" :space="80">
          <el-step v-for="(step, index) in selectedApprovalProcess.steps" :key="index" :title="step.name"
            :description="`审批人：${step.approver}`">
          </el-step>
        </el-steps>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="approvalDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmSubmitApproval" :loading="submittingApproval">
        {{ submittingApproval ? '提交中...' : '确认提交' }}
      </el-button>
    </div>
  </el-dialog>

  <!-- 审批进度对话框 -->
  <el-dialog title="审批进度" :visible.sync="progressDialogVisible" width="60%">
    <div v-if="currentApprovalProgress">
      <div style="margin-bottom: 20px;">
        <h4>{{ currentApprovalProgress.processName }}</h4>
        <p style="color: #909399;">提交理由：{{ currentApprovalProgress.submitReason }}</p>
      </div>

      <el-steps :active="currentApprovalProgress.currentStep" direction="vertical" :process-status="getProcessStatus()"
        :finish-status="getFinishStatus()">
        <el-step v-for="(step, index) in currentApprovalProgress.steps" :key="index" :title="step.name"
          :status="getStepStatus(index, currentApprovalProgress.currentStep)">
          <div slot="description">
            <p>审批人：{{ step.approver }}</p>
            <p v-if="step.status === 'approved'" style="color: #67C23A; font-size: 12px;">
              <i class="el-icon-circle-check"></i> 已通过 ({{ step.approvalTime }})
            </p>
            <p v-else-if="step.status === 'rejected'" style="color: #F56C6C; font-size: 12px;">
              <i class="el-icon-circle-close"></i> 已拒绝 ({{ step.approvalTime }})
            </p>
            <p v-else-if="step.status === 'pending' && index === currentApprovalProgress.currentStep"
              style="color: #E6A23C; font-size: 12px;">
              <i class="el-icon-loading"></i> 审批中...
            </p>
            <p v-else style="color: #909399; font-size: 12px;">
              <i class="el-icon-time"></i> 等待中...
            </p>
            <p v-if="step.comment" style="color: #909399; font-size: 12px;">
              审批意见：{{ step.comment }}
            </p>
          </div>
        </el-step>
      </el-steps>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="progressDialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
export default {
  name: 'ResearchReport',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      reportList: [],
      reportDialogVisible: false,
      viewReportDialogVisible: false,
      reportDialogTitle: '',
      isReportEdit: false,
      editReportId: null,
      currentReport: null,

      // 报告表单数据
      reportFormData: {
        projectNameNumber: '',        // 项目名称/编号
        reportName: '',               // 报告名称
        reportSummary: '',            // 报告概要
        achievementName: '',          // 成果名称
        achievementSummary: '',       // 成果概要
        authorDivision: '',           // 报告作者及分工
        leader: '',                   // 负责人
        writeTime: '',                // 编写时间
        submitTime: '',               // 提交时间
        submitMethod: '',             // 提交方式
        attachments: [],              // 报告终稿附件
        achievementAttachments: [],   // 最终成果附件
        notes: ''                     // 备注
      },

      // 报告文件上传相关
      uploadUrl: '/api/upload',
      reportAttachmentFileList: [],
      achievementAttachmentFileList: [],

      // 审批相关
      approvalDialogVisible: false,
      progressDialogVisible: false,
      submittingApproval: false,
      currentApprovalProgress: null,
      selectedApprovalProcess: null,

      // 审批表单数据
      approvalFormData: {
        reportName: '',
        processName: '',
        submitReason: ''
      },

      // 审批流程选项
      approvalProcessOptions: [
        {
          name: '标准审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '技术总监审批', approver: '李四' },
            { name: '总经理审批', approver: '王五' }
          ]
        },
        {
          name: '快速审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '总经理审批', approver: '王五' }
          ]
        },
        {
          name: '重大项目审批流程',
          steps: [
            { name: '部门负责人审批', approver: '张三' },
            { name: '技术总监审批', approver: '李四' },
            { name: '财务总监审批', approver: '赵六' },
            { name: '总经理审批', approver: '王五' },
            { name: '董事长审批', approver: '孙七' }
          ]
        }
      ],

      // 表单验证规则
      reportFormRules: {
        projectNameNumber: [
          { required: true, message: '请输入项目名称/编号', trigger: 'blur' }
        ],
        reportName: [
          { required: true, message: '请输入报告名称', trigger: 'blur' }
        ],
        reportSummary: [
          { required: true, message: '请输入报告概要', trigger: 'blur' }
        ],
        achievementName: [
          { required: true, message: '请输入成果名称', trigger: 'blur' }
        ],
        achievementSummary: [
          { required: true, message: '请输入成果概要', trigger: 'blur' }
        ],
        authorDivision: [
          { required: true, message: '请输入报告作者及分工', trigger: 'blur' }
        ],
        leader: [
          { required: true, message: '请输入负责人', trigger: 'blur' }
        ],
        writeTime: [
          { required: true, message: '请选择编写时间', trigger: 'change' }
        ],
        submitTime: [
          { required: true, message: '请选择提交时间', trigger: 'change' }
        ],
        submitMethod: [
          { required: true, message: '请选择提交方式', trigger: 'change' }
        ]
      },

      // 审批表单验证规则
      approvalFormRules: {
        processName: [
          { required: true, message: '请选择审批流程', trigger: 'change' }
        ],
        submitReason: [
          { required: true, message: '请输入提交理由', trigger: 'blur' },
          { min: 10, message: '提交理由至少需要10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 报告统计
    completedReports() {
      return this.reportList.filter(report => ['completed', 'approved'].includes(report.status)).length
    },
    inProgressReports() {
      return this.reportList.filter(report => ['in-progress', 'reviewing'].includes(report.status)).length
    },
    pendingReports() {
      return this.reportList.filter(report => ['pending', 'draft'].includes(report.status)).length
    }
  },
  created() {
    this.loadReportList()
  },
  methods: {
    // 加载报告列表
    loadReportList() {
      // 临时测试数据
      this.reportList = [
        {
          id: 1,
          projectNameNumber: 'PRJ-2024-001/智能制造系统研发',
          reportName: '智能制造系统研发最终报告',
          reportSummary: '本报告详细阐述了智能制造系统的研发过程、技术方案、测试结果及应用前景',
          achievementName: '智能制造控制系统V1.0',
          achievementSummary: '完成了智能制造控制系统的核心功能开发，实现了生产流程自动化控制',
          authorDivision: '撰稿人：张三（项目经理）、李四（技术负责人）；审校人：王五（技术总监）',
          leader: '张三',
          writeTime: '2024-01-15 14:30:00',
          submitTime: '2024-01-20 09:00:00',
          submitMethod: 'online',
          status: 'approved',
          attachments: [],
          achievementAttachments: [],
          notes: '重要报告，已通过专家评审',
          approvalProcess: {
            processName: '标准审批流程',
            submitReason: '研究报告已完成，申请审批发布',
            currentStep: 3,
            steps: [
              { name: '部门负责人审批', approver: '张三', status: 'approved', approvalTime: '2024-01-16 10:30', comment: '报告质量良好，同意通过' },
              { name: '技术总监审批', approver: '李四', status: 'approved', approvalTime: '2024-01-17 14:20', comment: '技术内容准确，审批通过' },
              { name: '总经理审批', approver: '王五', status: 'approved', approvalTime: '2024-01-18 09:15', comment: '同意发布该研究报告' }
            ]
          }
        },
        {
          id: 2,
          projectNameNumber: 'PRJ-2024-002/数据分析平台',
          reportName: '数据分析平台开发进展报告',
          reportSummary: '报告了数据分析平台的开发进展、关键技术突破和下一步工作计划',
          achievementName: '数据分析平台原型系统',
          achievementSummary: '完成了数据分析平台的原型开发，支持多种数据源接入和可视化分析',
          authorDivision: '撰稿人：李四（开发负责人）、赵六（算法工程师）；审校人：张三（项目经理）',
          leader: '李四',
          writeTime: '2024-01-10 16:00:00',
          submitTime: '2024-01-12 10:30:00',
          submitMethod: 'email',
          status: 'reviewing',
          attachments: [],
          achievementAttachments: [],
          notes: '需要补充性能测试数据',
          approvalProcess: {
            processName: '快速审批流程',
            submitReason: '数据分析平台开发报告，申请审批',
            currentStep: 1,
            steps: [
              { name: '部门负责人审批', approver: '张三', status: 'approved', approvalTime: '2024-01-13 09:30', comment: '开发进展良好，同意继续' },
              { name: '总经理审批', approver: '王五', status: 'pending', approvalTime: null, comment: '' }
            ]
          }
        },
        {
          id: 3,
          projectNameNumber: 'PRJ-2024-003/用户体验优化',
          reportName: '用户体验优化研究报告',
          reportSummary: '基于用户调研和数据分析，提出了系统用户体验的优化建议和改进方案',
          achievementName: '用户体验优化方案',
          achievementSummary: '形成了完整的用户体验优化方案，包括界面改进、交互优化等内容',
          authorDivision: '撰稿人：王五（UX设计师）、钱七（前端开发）；审校人：李四（技术负责人）',
          leader: '王五',
          writeTime: '2024-01-08 11:20:00',
          submitTime: '2024-01-10 15:45:00',
          submitMethod: 'offline',
          status: 'draft',
          attachments: [],
          achievementAttachments: [],
          notes: '已提交审核，等待专家意见'
        }
      ]
    },

    // 新增报告
    addReport() {
      this.reportDialogTitle = '新增研究报告'
      this.isReportEdit = false
      this.editReportId = null
      this.reportFormData = {
        projectNameNumber: '',        // 项目名称/编号
        reportName: '',               // 报告名称
        reportSummary: '',            // 报告概要
        achievementName: '',          // 成果名称
        achievementSummary: '',       // 成果概要
        authorDivision: '',           // 报告作者及分工
        leader: '',                   // 负责人
        writeTime: '',                // 编写时间
        submitTime: '',               // 提交时间
        submitMethod: '',             // 提交方式
        attachments: [],              // 报告终稿附件
        achievementAttachments: [],   // 最终成果附件
        notes: ''                     // 备注
      }
      this.reportAttachmentFileList = []
      this.achievementAttachmentFileList = []
      this.reportDialogVisible = true
    },

    // 编辑报告
    editReport(report) {
      this.reportDialogTitle = '编辑研究报告'
      this.isReportEdit = true
      this.editReportId = report.id
      this.reportFormData = { ...report }
      this.reportAttachmentFileList = report.attachments || []
      this.reportDialogVisible = true
      this.viewReportDialogVisible = false
    },

    // 查看报告
    viewReport(report) {
      this.currentReport = report
      this.viewReportDialogVisible = true
    },

    // 保存报告
    saveReport() {
      this.$refs.reportForm.validate((valid) => {
        if (valid) {
          const reportData = {
            ...this.reportFormData,
            status: this.isReportEdit ? this.reportFormData.status : 'draft'
          }

          if (this.isReportEdit) {
            // 更新报告
            const index = this.reportList.findIndex(r => r.id === this.editReportId)
            if (index !== -1) {
              this.reportList.splice(index, 1, { ...reportData, id: this.editReportId })
            }
            this.$message.success('报告更新成功')
          } else {
            // 新增报告
            reportData.id = Date.now()
            this.reportList.push(reportData)
            this.$message.success('报告创建成功')
          }

          this.reportDialogVisible = false
        }
      })
    },



    // 报告附件上传成功
    handleReportAttachmentSuccess(response, file, fileList) {
      this.reportAttachmentFileList = fileList
      // 单选模式：只保存最后一个文件
      if (fileList.length > 0) {
        const latestFile = fileList[fileList.length - 1]
        this.reportFormData.attachments = [{
          name: latestFile.name,
          url: latestFile.response?.url || latestFile.url
        }]
      } else {
        this.reportFormData.attachments = []
      }
      this.$message.success('报告终稿上传成功')
    },

    // 报告附件移除
    handleReportAttachmentRemove(file, fileList) {
      this.reportAttachmentFileList = fileList
      if (fileList.length > 0) {
        const latestFile = fileList[fileList.length - 1]
        this.reportFormData.attachments = [{
          name: latestFile.name,
          url: latestFile.response?.url || latestFile.url
        }]
      } else {
        this.reportFormData.attachments = []
      }
    },

    // 最终成果附件上传成功
    handleAchievementAttachmentSuccess(response, file, fileList) {
      this.achievementAttachmentFileList = fileList
      // 单选模式：只保存最后一个文件
      if (fileList.length > 0) {
        const latestFile = fileList[fileList.length - 1]
        this.reportFormData.achievementAttachments = [{
          name: latestFile.name,
          url: latestFile.response?.url || latestFile.url
        }]
      } else {
        this.reportFormData.achievementAttachments = []
      }
      this.$message.success('最终成果上传成功')
    },

    // 最终成果附件移除
    handleAchievementAttachmentRemove(file, fileList) {
      this.achievementAttachmentFileList = fileList
      if (fileList.length > 0) {
        const latestFile = fileList[fileList.length - 1]
        this.reportFormData.achievementAttachments = [{
          name: latestFile.name,
          url: latestFile.response?.url || latestFile.url
        }]
      } else {
        this.reportFormData.achievementAttachments = []
      }
    },

    // 报告附件数量超出限制
    handleReportExceed() {
      this.$message.warning('报告终稿只能上传一个文件，如需更换请先删除当前文件')
    },

    // 最终成果附件数量超出限制
    handleAchievementExceed() {
      this.$message.warning('最终成果只能上传一个文件，如需更换请先删除当前文件')
    },

    // 处理审批操作
    handleApprovalAction(report) {
      if (report.status === 'draft' || report.status === 'rejected') {
        // 草稿状态或被拒绝状态 - 提交审批
        this.submitApproval(report)
      } else {
        // 其他状态 - 查看审批进度
        this.viewApprovalProgress(report)
      }
    },

    // 提交审批
    submitApproval(report) {
      this.approvalFormData = {
        reportName: report.reportName,
        processName: '',
        submitReason: ''
      }
      this.approvalDialogVisible = true
    },

    // 审批流程变更
    handleProcessChange(processName) {
      this.selectedApprovalProcess = this.approvalProcessOptions.find(p => p.name === processName)
    },

    // 确认提交审批
    confirmSubmitApproval() {
      this.$refs.approvalForm.validate((valid) => {
        if (valid) {
          this.submittingApproval = true

          // 模拟提交审批
          setTimeout(() => {
            // 更新报告状态
            const report = this.reportList.find(r => r.reportName === this.approvalFormData.reportName)
            if (report) {
              report.status = 'reviewing'
              report.approvalProcess = {
                processName: this.approvalFormData.processName,
                submitReason: this.approvalFormData.submitReason,
                currentStep: 0,
                steps: this.selectedApprovalProcess.steps.map(step => ({
                  ...step,
                  status: 'pending',
                  approvalTime: null,
                  comment: ''
                }))
              }
            }

            this.submittingApproval = false
            this.approvalDialogVisible = false
            this.$message.success('审批提交成功')
          }, 1500)
        }
      })
    },

    // 查看审批进度
    viewApprovalProgress(report) {
      if (report.approvalProcess) {
        this.currentApprovalProgress = report.approvalProcess
        this.progressDialogVisible = true
      } else {
        this.$message.warning('该报告尚未提交审批')
      }
    },

    // 获取步骤状态
    getStepStatus(stepIndex, currentStep) {
      if (stepIndex < currentStep) {
        return 'finish'
      } else if (stepIndex === currentStep) {
        return 'process'
      } else {
        return 'wait'
      }
    },

    // 获取进程状态
    getProcessStatus() {
      return 'process'
    },

    // 获取完成状态
    getFinishStatus() {
      return 'success'
    },

    // 获取审批按钮文本
    getApprovalButtonText(status) {
      const textMap = {
        'draft': '提交审批',
        'reviewing': '审批进度',
        'approved': '审批进度',
        'rejected': '重新提交',
        'completed': '审批进度'
      }
      return textMap[status] || '提交审批'
    },

    // 获取报告类型标签类型
    getReportTypeTagType(type) {
      const typeMap = {
        'final': 'success',
        'result': 'primary',
        'stage': 'warning',
        'other': 'info'
      }
      return typeMap[type] || 'info'
    },

    // 获取报告类型文本
    getReportTypeText(type) {
      const textMap = {
        'final': '最终报告',
        'result': '成果报告',
        'stage': '阶段报告',
        'other': '其他报告'
      }
      return textMap[type] || '未知类型'
    },

    // 获取报告状态标签类型
    getReportStatusTagType(status) {
      const statusMap = {
        'draft': 'info',
        'pending': 'warning',
        'in-progress': 'primary',
        'reviewing': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'completed': 'success'
      }
      return statusMap[status] || 'info'
    },

    // 获取报告状态文本
    getReportStatusText(status) {
      const textMap = {
        'draft': '草稿',
        'pending': '待处理',
        'in-progress': '进行中',
        'reviewing': '审批中',
        'approved': '已批准',
        'rejected': '已拒绝',
        'completed': '已完成'
      }
      return textMap[status] || '未知状态'
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        'draft': 'el-icon-edit-outline',
        'pending': 'el-icon-time',
        'in-progress': 'el-icon-loading',
        'reviewing': 'el-icon-view',
        'approved': 'el-icon-circle-check',
        'rejected': 'el-icon-circle-close',
        'completed': 'el-icon-success'
      }
      return iconMap[status] || 'el-icon-question'
    }
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-stats {
  margin-bottom: 30px;
}

.report-list {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  text-align: right;
}

/* 操作按钮样式 - 防止换行 */
.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  flex-wrap: nowrap;
}

.operation-buttons .el-button {
  margin: 0;
  padding: 5px 8px;
  font-size: 12px;
  min-width: auto;
}

/* 操作列样式 */
::v-deep .operation-column .cell {
  padding: 0 4px;
  overflow: visible;
}

.operation-buttons {
  display: flex;
  flex-wrap: nowrap;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

/* 响应式按钮样式 */
.responsive-btn {
  flex: 1;
  min-width: 0;
  padding: 6px 8px;
  font-size: 12px;
  white-space: nowrap;
  border-radius: 3px;
  transition: all 0.3s ease;
}

/* 大屏幕 */
@media (min-width: 1200px) {
  .responsive-btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .operation-buttons {
    gap: 6px;
  }
}

/* 中等屏幕 */
@media (max-width: 1199px) and (min-width: 768px) {
  .responsive-btn {
    padding: 6px 8px;
    font-size: 12px;
  }

  .operation-buttons {
    gap: 4px;
  }
}

/* 小屏幕 */
@media (max-width: 767px) {
  .responsive-btn {
    padding: 4px 6px;
    font-size: 11px;
  }

  .operation-buttons {
    gap: 2px;
  }
}

/* 超小屏幕 */
@media (max-width: 480px) {
  .responsive-btn {
    padding: 3px 4px;
    font-size: 10px;
  }

  .operation-buttons {
    gap: 1px;
  }
}

/* 确保按钮文字不换行 */
::v-deep .responsive-btn span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 操作列最小宽度确保按钮显示完整 */
::v-deep .operation-column {
  min-width: 200px;
}

/* 按钮悬停效果 */
.responsive-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表单分组标题样式 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 2px solid #e1e8ed;
  padding-bottom: 10px;
}

.section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

/* 提交方式和状态标签样式 */
.el-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.el-tag i {
  font-size: 12px;
}

/* 表格中的标签样式 */
.el-table .el-tag {
  margin: 2px 0;
}

/* 状态标签特殊样式 */
.el-tag.el-tag--success {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

.el-tag.el-tag--primary {
  background-color: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.el-tag.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #e6a23c;
  color: #e6a23c;
}

.el-tag.el-tag--danger {
  background-color: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

.el-tag.el-tag--info {
  background-color: #f4f4f5;
  border-color: #909399;
  color: #909399;
}

/* 表头居中显示 */
::v-deep .el-table th {
  text-align: center !important;
}

::v-deep .el-table th .cell {
  text-align: center !important;
  justify-content: center !important;
}

::v-deep .el-table__header th {
  text-align: center !important;
}

::v-deep .el-table__header .cell {
  text-align: center !important;
  justify-content: center !important;
}

/* 操作按钮响应式大小 */
.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.operation-buttons .el-button {
  font-size: 0.75rem;
  padding: 6px 10px;
  min-width: 50px;
  transition: all 0.3s ease;
}

/* 大屏幕 (>1200px) */
@media (min-width: 1200px) {
  .operation-buttons .el-button {
    font-size: 0.8rem;
    padding: 7px 12px;
    min-width: 55px;
  }
}

/* 中等屏幕 (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .operation-buttons .el-button {
    font-size: 0.75rem;
    padding: 6px 10px;
    min-width: 50px;
  }
}

/* 小屏幕 (481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
  .operation-buttons {
    gap: 2px;
  }

  .operation-buttons .el-button {
    font-size: 0.7rem;
    padding: 5px 8px;
    min-width: 45px;
  }
}

/* 超小屏幕 (≤480px) */
@media (max-width: 480px) {
  .operation-buttons {
    gap: 2px;
    flex-direction: column;
  }

  .operation-buttons .el-button {
    font-size: 0.65rem;
    padding: 4px 6px;
    min-width: 40px;
    width: 100%;
  }
}

/* 根据视口宽度动态调整按钮大小 */
.operation-buttons .el-button {
  font-size: clamp(0.6rem, 1.5vw, 0.85rem);
  padding: clamp(4px, 1vw, 8px) clamp(6px, 1.5vw, 12px);
}
</style>
