<template>
<div class="process-detail-container">
  <!-- 顶部面包屑和返回按钮 -->
  <div class="detail-header">
    <el-button type="text" icon="el-icon-arrow-left" @click="goBack">返回列表</el-button>
    <h2>{{ projectInfo.projectName || '课题详情' }}</h2>
  </div>

  <div class="detail-content">
    <!-- 左侧侧边栏 -->
    <div class="detail-sidebar">
      <el-menu :default-active="activeMenu" class="sidebar-menu" @select="handleMenuSelect" background-color="#f5f7fa"
        text-color="#606266" active-text-color="#409EFF">

        <!-- 1. 课题信息：课题需求信息（课题基本信息）；----编辑 -->
        <el-menu-item index="view">
          <i class="el-icon-view"></i>
          <span>课题信息</span>
        </el-menu-item>



        <!-- 2. 预立项审批： -->
        <el-menu-item index="submit-approval">
          <i class="el-icon-s-check"></i>
          <span>预立项审批</span>
        </el-menu-item>

        <!-- 3. 研究计划：研究计划、研究计划调整；
        <el-menu-item index="research-plan">
          <i class="el-icon-document"></i>
          <span>研究计划</span>
        </el-menu-item> -->

        <!-- 4. 立项审批：立项审批表、经费审批表；----编辑 -->
        <el-menu-item index="project-process">
          <i class="el-icon-circle-check"></i>
          <span>立项审批</span>
        </el-menu-item>

        <!-- 5. 实施方案：包含了时间节点； -->
        <el-menu-item index="implementation-plan">
          <i class="el-icon-s-grid"></i>
          <span>实施方案</span>
        </el-menu-item>

        <!-- 6. 项目里程碑： -->
        <el-menu-item index="milestone-management">
          <i class="el-icon-date"></i>
          <span>项目里程碑</span>
        </el-menu-item>

        <!-- 7. 项目合同： -->
        <el-menu-item index="project-contract">
          <i class="el-icon-document-copy"></i>
          <span>项目合同</span>
        </el-menu-item>

        <!-- 8. 项目调研： -->
        <el-menu-item index="project-research">
          <i class="el-icon-search"></i>
          <span>项目调研</span>
        </el-menu-item>

        <!-- 9. 研究报告： -->
        <el-menu-item index="research-report">
          <i class="el-icon-document"></i>
          <span>研究报告</span>
        </el-menu-item>

        <!-- 10. 结项材料： -->
        <el-menu-item index="project-materials">
          <i class="el-icon-folder-opened"></i>
          <span>结项材料</span>
        </el-menu-item>

        <!-- 11. 成果发布： -->
        <el-menu-item index="project-results">
          <i class="el-icon-trophy"></i>
          <span>成果发布</span>
        </el-menu-item>

      </el-menu>
    </div>

    <!-- 右侧主要内容区域 -->
    <div class="detail-main">
      <!-- 查看详情 -->
      <div v-if="activeMenu === 'view'" class="content-section">
        <!-- 课题基本信息 -->
        <el-card style="margin-bottom: 20px;">
          <div slot="header" class="card-header">
            <span>📋 课题基本信息</span>
            <div class="header-actions">
              <el-button type="primary" size="small" icon="el-icon-edit" @click="openEditBasicInfoDialog">
                编辑基本信息
              </el-button>
            </div>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="课题名称">{{ subjectData.subjectName }}</el-descriptions-item>
            <el-descriptions-item label="课题状态">
              <el-tag :type="getStatusType(subjectData.status)">
                {{ getStatusText(subjectData.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="课题来源">{{ subjectData.subjectSource }}</el-descriptions-item>
            <el-descriptions-item label="负责人">{{ subjectData.leader }}</el-descriptions-item>
            <el-descriptions-item label="研究方法">{{ subjectData.researchMethod }}</el-descriptions-item>
            <el-descriptions-item label="开始时间">{{ subjectData.startTime }}</el-descriptions-item>
            <el-descriptions-item label="结束时间">{{ subjectData.endTime }}</el-descriptions-item>
            <el-descriptions-item label="研究计划状态">
              <el-tag :type="getResearchPlanStatusType(subjectData.id)">
                {{ getResearchPlanStatusText(subjectData.id) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="主要内容" :span="2">
              <div style="white-space: pre-wrap;">{{ subjectData.mainContent }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="预期成果" :span="2">
              <div style="white-space: pre-wrap;">{{ subjectData.expectedResults }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 预立项审批流程详情（仅在审批中或审批完成时显示） -->
        <el-card v-if="subjectData.status === '1' || subjectData.status === '2'" style="margin-bottom: 20px;">
          <div slot="header">
            <span>🔄 预立项审批流程详情</span>
          </div>

          <!-- 预立项基本信息 -->
          <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
            <h4 style="margin: 0 0 10px 0; color: #606266;">预立项信息</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <p><strong>项目名称：</strong>{{ getPreProjectInfo().projectName }}</p>
              </el-col>
              <el-col :span="8">
                <p><strong>委托单位：</strong>{{ getPreProjectInfo().delegateUnit }}</p>
              </el-col>
              <el-col :span="8">
                <p><strong>项目编号：</strong>{{ getPreProjectInfo().projectNumber }}</p>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <p><strong>项目负责人：</strong>{{ getPreProjectInfo().projectLeader }}</p>
              </el-col>
              <el-col :span="8">
                <p><strong>立项时间：</strong>{{ getPreProjectInfo().projectStartTime }}</p>
              </el-col>
              <el-col :span="8">
                <p><strong>预估收入：</strong>{{ getPreProjectInfo().estimatedIncome }}万元</p>
              </el-col>
            </el-row>
          </div>

          <!-- 审批流程进度 -->
          <div>
            <h4 style="margin-bottom: 15px; color: #606266;">审批流程进度</h4>
            <div v-if="subjectData.status === '1'">
              <!-- 审批通过的完整流程 -->
              <el-steps :active="3" direction="vertical" finish-status="success">
                <el-step title="部门负责人审批">
                  <div slot="description">
                    <p>审批人：张三</p>
                    <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
                    <p style="color: #909399; font-size: 12px;">审批意见：同意该课题立项，技术方案可行。</p>
                  </div>
                </el-step>
                <el-step title="技术总监审批">
                  <div slot="description">
                    <p>审批人：李四</p>
                    <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-16 14:20)</p>
                    <p style="color: #909399; font-size: 12px;">审批意见：技术路线清晰，预算合理，同意立项。</p>
                  </div>
                </el-step>
                <el-step title="总经理审批">
                  <div slot="description">
                    <p>审批人：王五</p>
                    <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-17 09:15)</p>
                    <p style="color: #909399; font-size: 12px;">审批意见：项目具有良好的市场前景，批准立项。</p>
                  </div>
                </el-step>
              </el-steps>
            </div>
            <div v-else-if="subjectData.status === '2'">
              <!-- 审批中的流程 -->
              <el-steps :active="1" direction="vertical" process-status="process">
                <el-step title="部门负责人审批" status="finish">
                  <div slot="description">
                    <p>审批人：张三</p>
                    <p style="color: #67C23A; font-size: 12px;">✓ 已通过 (2024-01-15 10:30)</p>
                    <p style="color: #909399; font-size: 12px;">审批意见：同意该课题立项，技术方案可行。</p>
                  </div>
                </el-step>
                <el-step title="技术总监审批" status="process">
                  <div slot="description">
                    <p>审批人：李四</p>
                    <p style="color: #E6A23C; font-size: 12px;">⏳ 审批中...</p>
                    <p style="color: #909399; font-size: 12px;">等待技术总监审批</p>
                  </div>
                </el-step>
                <el-step title="总经理审批" status="wait">
                  <div slot="description">
                    <p>审批人：王五</p>
                    <p style="color: #909399; font-size: 12px;">⏸ 等待中...</p>
                  </div>
                </el-step>
              </el-steps>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 编辑信息 -->
      <div v-if="activeMenu === 'edit'" class="content-section">
        <el-card>
          <div slot="header">
            <span>📝 编辑课题信息</span>
          </div>
          <el-form :model="formData" label-width="120px" :rules="formRules" ref="editForm">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="课题名称" prop="subjectName">
                  <el-input v-model="formData.subjectName" placeholder="请输入课题名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="课题来源" prop="subjectSource">
                  <el-select v-model="formData.subjectSource" placeholder="请选择课题来源" style="width: 100%">
                    <el-option label="国家自然科学基金" value="国家自然科学基金"></el-option>
                    <el-option label="企业委托" value="企业委托"></el-option>
                    <el-option label="省部级项目" value="省部级项目"></el-option>
                    <el-option label="校内项目" value="校内项目"></el-option>
                    <el-option label="其他" value="其他"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="负责人" prop="leader">
                  <el-input v-model="formData.leader" placeholder="请输入负责人姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="研究方法" prop="researchMethod">
                  <el-select v-model="formData.researchMethod" placeholder="请选择研究方法" style="width: 100%">
                    <el-option label="理论研究" value="理论研究"></el-option>
                    <el-option label="实验研究" value="实验研究"></el-option>
                    <el-option label="应用研究" value="应用研究"></el-option>
                    <el-option label="调研分析" value="调研分析"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开始时间" prop="startTime">
                  <el-date-picker v-model="formData.startTime" type="date" placeholder="选择开始时间"
                    style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" prop="endTime">
                  <el-date-picker v-model="formData.endTime" type="date" placeholder="选择结束时间"
                    style="width: 100%"></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="主要内容" prop="mainContent">
              <el-input v-model="formData.mainContent" type="textarea" :rows="4" placeholder="请输入课题的主要研究内容"></el-input>
            </el-form-item>

            <el-form-item label="预期成果" prop="expectedResults">
              <el-input v-model="formData.expectedResults" type="textarea" :rows="4" placeholder="请输入预期研究成果"></el-input>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveEdit">保存修改</el-button>
              <el-button @click="cancelEdit">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>
        <!-- 编辑经费信息 -->
        <el-card v-if="!budgetEditMode" style="margin-bottom: 20px;">
          <div slot="header" class="card-header">
            <span>✏️ 编辑经费预算</span>
            <div class="header-actions">
              <el-button @click="cancelBudgetEdit" size="small">取消</el-button>
              <el-button type="primary" @click="saveBudgetEdit" size="small" :loading="budgetSaving">保存</el-button>
            </div>
          </div>

          <div class="budget-edit-container">
            <el-form ref="budgetEditForm" :model="budgetEditData" :rules="budgetEditRules" label-width="160px">

              <!-- 项目基本信息 -->
              <div class="budget-edit-section">
                <h3 class="section-title">
                  <i class="el-icon-wallet"></i>
                  项目基本信息
                </h3>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="项目收入" prop="projectIncome">
                      <el-input v-model="budgetEditData.projectIncome" placeholder="请输入项目收入">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="经费预算总额" prop="economicBudgetTotal">
                      <el-input v-model="budgetEditData.economicBudgetTotal" placeholder="请输入经费预算总额">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目负责人" prop="projectManager">
                      <el-input v-model="budgetEditData.projectManager" placeholder="请输入项目负责人"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 费用明细 -->
              <div class="budget-edit-section">
                <h3 class="section-title">
                  <i class="el-icon-document"></i>
                  费用明细
                </h3>

                <!-- 第一行费用 -->
                <el-row :gutter="20" style="margin-bottom: 15px;">
                  <el-col :span="6">
                    <el-form-item label="会议费/差旅费/国际合作与交流费">
                      <el-input v-model="budgetEditData.meetingTravelFee">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="数据采集费">
                      <el-input v-model="budgetEditData.dataCollectionFee">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="资料费">
                      <el-input v-model="budgetEditData.materialsFee">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="劳务费">
                      <el-input v-model="budgetEditData.laborFee">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第二行费用 -->
                <el-row :gutter="20" style="margin-bottom: 15px;">
                  <el-col :span="6">
                    <el-form-item label="专家费">
                      <el-input v-model="budgetEditData.expertFee">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="中介机构费">
                      <el-input v-model="budgetEditData.intermediaryFee">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="印刷出版费">
                      <el-input v-model="budgetEditData.printingFee">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="税费">
                      <el-input v-model="budgetEditData.taxFee">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第三行：其他费用和合计 -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="其他费用">
                      <el-input v-model="budgetEditData.otherFee" placeholder="请输入其他费用">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="费用合计">
                      <el-input :value="calculateEditTotalFee()" :disabled="true" class="total-input">
                        <template slot="append">万元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-form>
          </div>
        </el-card>
      </div>



      <!-- 研究计划 -->
      <div v-if="activeMenu === 'research-plan'" class="content-section">
        <ResearchPlan :project-info="subjectData" />
      </div>

      <!-- 预立项审批 -->
      <div v-if="activeMenu === 'submit-approval'" class="content-section">
        <PreProjectApproval :project-info="subjectData" @update-status="handleUpdateStatus" />
      </div>

      <!-- 立项审批 -->
      <div v-if="activeMenu === 'project-process'" class="content-section">
        <ProjectApproval :project-info="projectInfo" @update-status="handleUpdateStatus" />
      </div>

      <!-- 实施方案 -->
      <div v-if="activeMenu === 'implementation-plan'" class="content-section">
        <ImplementationPlan :project-info="projectInfo" />
      </div>

      <!-- 项目里程碑管理 -->
      <div v-if="activeMenu === 'milestone-management'" class="content-section">
        <MilestoneManagement :project-info="projectInfo" />
      </div>

      <!-- 项目调研 -->
      <div v-if="activeMenu === 'project-research'" class="content-section">
        <ProjectResearch :project-info="projectInfo" />
      </div>

      <!-- 研究报告 -->
      <div v-if="activeMenu === 'research-report'" class="content-section">
        <ResearchReport :project-info="projectInfo" />
      </div>

      <!-- 项目合同 -->
      <div v-if="activeMenu === 'project-contract'" class="content-section">
        <ProjectContract :project-info="projectInfo" />
      </div>

      <!-- 结项材料 -->
      <div v-if="activeMenu === 'project-materials'" class="content-section">
        <ProjectClosureMaterials :project-info="projectInfo" />
      </div>

      <!-- 成果发布 -->
      <div v-if="activeMenu === 'project-results'" class="content-section">
        <ProjectResultsPublication :project-info="projectInfo" />
      </div>
    </div>
  </div>

  <!-- 编辑基本信息弹框 -->
  <el-dialog title="编辑基本信息" :visible.sync="editBasicInfoDialogVisible" width="70%" :close-on-click-modal="false">
    <el-form :model="editBasicInfoForm" :rules="editBasicInfoRules" ref="editBasicInfoForm" label-width="140px">
      <!-- 基本信息编辑 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          课题基本信息
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="课题名称" prop="subjectName">
              <el-input v-model="editBasicInfoForm.subjectName" placeholder="请输入课题名称" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位" prop="delegateUnit">
              <el-input v-model="editBasicInfoForm.delegateUnit" placeholder="请输入委托单位" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="课题编号" prop="subjectNumber">
              <el-input v-model="editBasicInfoForm.subjectNumber" placeholder="请输入课题编号" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人" prop="projectLeader">
              <el-input v-model="editBasicInfoForm.projectLeader" placeholder="请输入项目负责人" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker v-model="editBasicInfoForm.startTime" type="date" placeholder="选择开始时间"
                style="width: 100%"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker v-model="editBasicInfoForm.endTime" type="date" placeholder="选择结束时间"
                style="width: 100%"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 课题需求信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-edit-outline"></i>
          课题需求信息
        </h3>
        <el-form-item label="预期内容" prop="expectedContent">
          <el-input v-model="editBasicInfoForm.expectedContent" type="textarea" :rows="4" placeholder="请输入预期内容"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="预期成果" prop="expectedResults">
          <el-input v-model="editBasicInfoForm.expectedResults" type="textarea" :rows="4" placeholder="请输入预期成果"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="研究内容" prop="researchContent">
          <el-input v-model="editBasicInfoForm.researchContent" type="textarea" :rows="4" placeholder="请输入研究内容"
            clearable>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="editBasicInfoDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveBasicInfo" :loading="savingBasicInfo">保存</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
import ResearchPlan from './components/ResearchPlan.vue'
import PreProjectApproval from './components/PreProjectApproval.vue'
import ImplementationPlan from './components/ImplementationPlan.vue'
import MilestoneManagement from './components/MilestoneManagement.vue'
import ProjectResearch from './components/ProjectResearch.vue'
import ResearchReport from './components/ResearchReport.vue'
import ProjectContract from './components/ProjectContract.vue'
import ProjectClosureMaterials from './components/ProjectClosureMaterials.vue'
import ProjectResultsPublication from './components/ProjectResultsPublication.vue'
import ProjectApproval from './components/ProjectApproval.vue'
import { getTableDataByPage } from '../api/api'

export default {
  name: 'ProcessDetail',
  components: {
    ResearchPlan,
    PreProjectApproval,
    ImplementationPlan,
    MilestoneManagement,
    ProjectResearch,
    ResearchReport,
    ProjectContract,
    ProjectClosureMaterials,
    ProjectResultsPublication,
    ProjectApproval
  },
  data() {
    return {
      activeMenu: 'view',
      projectInfo: {},
      editForm: {},
      isEditing: false,

      // 编辑相关
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      editId: null,
      formData: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        status: '',
        mainContent: '',
        expectedResults: '',
        startTime: '',
        endTime: ''
      },
      subjectData: {},
      // 研究计划相关
      researchPlanDialogVisible: false,
      researchPlanFormData: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        researchObjective: '',
        researchContent: '',
        researchPlan: '',
        expectedResults: '',
        riskAssessment: '',
        budgetPlan: '',
        timeline: ''
      },
      researchPlanStatus: {
        // 这里应该从API获取实际的研究计划状态
        // 暂时设置为空对象，实际使用时应该调用API获取
      },

      // 经费编辑相关
      budgetEditMode: false, // 经费编辑模式
      budgetSaving: false, // 保存状态

      // 编辑基本信息相关
      editBasicInfoDialogVisible: false,
      savingBasicInfo: false,
      editBasicInfoForm: {
        subjectName: '',
        delegateUnit: '',
        subjectNumber: '',
        projectLeader: '',
        startTime: '',
        endTime: '',
        expectedContent: '',
        expectedResults: '',
        researchContent: ''
      },
      editBasicInfoRules: {
        subjectName: [
          { required: true, message: '请输入课题名称', trigger: 'blur' }
        ],
        delegateUnit: [
          { required: true, message: '请输入委托单位', trigger: 'blur' }
        ],
        subjectNumber: [
          { required: true, message: '请输入课题编号', trigger: 'blur' }
        ],
        projectLeader: [
          { required: true, message: '请输入项目负责人', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        expectedContent: [
          { required: true, message: '请输入预期内容', trigger: 'blur' }
        ],
        expectedResults: [
          { required: true, message: '请输入预期成果', trigger: 'blur' }
        ],
        researchContent: [
          { required: true, message: '请输入研究内容', trigger: 'blur' }
        ]
      },
      budgetEditData: {
        projectIncome: '',
        economicBudgetTotal: '',
        projectManager: '',
        meetingTravelFee: '',
        dataCollectionFee: '',
        materialsFee: '',
        expertFee: '',
        intermediaryFee: '',
        laborFee: '',
        printingFee: '',
        taxFee: '',
        otherFee: ''
      },
      budgetEditRules: {
        projectIncome: [
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        economicBudgetTotal: [
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ]
      },

      // 预立项审批相关
      submitDialogVisible: false,
      progressDialogVisible: false,
      currentProjectData: {},
      submitFormData: {
        projectName: '',
        delegateUnit: '',
        projectNumber: '',
        projectLeader: '',
        projectStartTime: '',
        estimatedIncome: '',
        projectDescription: '',
        technicalRequirements: '',
        deliverables: '',
        timeline: '',
        budgetBreakdown: '',
        riskAssessment: ''
      },

      // 表单验证规则
      formRules: {
        subjectName: [
          { required: true, message: '请输入课题名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        subjectSource: [
          { required: true, message: '请选择课题来源', trigger: 'change' }
        ],
        leader: [
          { required: true, message: '请输入负责人姓名', trigger: 'blur' }
        ],
        researchMethod: [
          { required: true, message: '请选择研究方法', trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        mainContent: [
          { required: true, message: '请输入主要内容', trigger: 'blur' },
          { min: 10, message: '主要内容至少10个字符', trigger: 'blur' }
        ],
        expectedResults: [
          { required: true, message: '请输入预期成果', trigger: 'blur' },
          { min: 10, message: '预期成果至少10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loadProjectInfo()
  },
  methods: {
    // 返回列表页
    goBack() {
      this.$router.go(-1)
    },

    // 菜单选择
    handleMenuSelect(index) {
      this.activeMenu = index
    },

    // 加载项目信息
    async loadProjectInfo() {
      const projectId = this.$route.params.id

      // 从路由查询参数中获取项目信息
      const queryData = this.$route.query
      // const response = await getProjectBaseInfoByName(queryData.subjectName)
      const subjectRes = await getTableDataByPage(1,
        10,
        { subjectName: queryData.projectName })
      this.subjectData = {
        ...subjectRes.data[0]
      }
      // 构建项目信息对象
      this.projectInfo = {
        id: projectId,
        projectName: queryData.projectName || '',
        delegateUnit: queryData.delegateUnit || '',
        projectNumber: queryData.projectNumber || '',
        trustee: queryData.trustee || '',
        projectLeader: queryData.projectLeader || '',
        expectedContent: queryData.expectedContent || '0',
        expectedResults: queryData.expectedResults || '',
        status: queryData.status || '',
      }

      // 如果查询参数中没有足够的数据，可以在这里调用API获取完整数据
      // this.fetchProjectDetails(projectId)

      // 初始化编辑表单
      this.editForm = { ...this.subjectData }
      this.formData = { ...this.subjectData }
    },

    // 可选：从API获取项目详细信息
    async fetchProjectDetails(projectId) {
      try {
        console.log('获取项目详情:', projectId)
      } catch (error) {
        console.error('获取项目详情失败:', error)
        this.$message.error('获取项目详情失败')
      }
    },

    // 保存编辑
    saveEdit() {
      // 检查是否有新的编辑表单引用
      if (this.$refs.editForm && this.activeMenu === 'edit') {
        this.$refs.editForm.validate((valid) => {
          if (valid) {
            this.saving = true

            // 模拟保存
            setTimeout(() => {
              // 更新主数据
              this.subjectData.subjectName = this.editFormData.projectName
              this.subjectData.delegateUnit = this.editFormData.delegateUnit
              this.subjectData.subjectNumber = this.editFormData.projectNumber
              this.subjectData.projectLeader = this.editFormData.projectLeader
              this.subjectData.expectedContent = this.editFormData.expectedContent
              this.subjectData.expectedResults = this.editFormData.expectedResults
              this.subjectData.researchContent = this.editFormData.researchContent

              this.saving = false
              this.activeMenu = 'view'
              this.$message.success('课题信息保存成功')
            }, 1500)
          }
        })
      } else {
        // 原有的保存逻辑（兼容旧的编辑表单）
        this.$refs.editForm.validate((valid) => {
          if (valid) {
            // 模拟保存到后端
            this.subjectData = { ...this.formData }
            this.$message.success('课题信息保存成功')
            this.activeMenu = 'view'
          } else {
            this.$message.error('请检查表单填写是否正确')
            return false
          }
        })
      }
    },

    // 取消编辑
    cancelEdit() {
      this.activeMenu = 'view'
      if (this.editFormData) {
        this.initEditFormData()
      } else {
        this.formData = { ...this.subjectData }
      }
      this.$message.info('已取消编辑')
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '0': 'info',
        '1': 'success',
        '2': 'warning',
        '3': 'warning',
        '4': 'info',
        '5': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '草稿',
        '1': '审批通过',
        '2': '审批中',
        '3': '审批中',
        '4': '已填写未提交',
        '5': '未填写'
      }
      return statusMap[status] || '未知'
    },

    // 计算费用合计
    calculateTotalFee() {
      if (!this.subjectData) return '0.00'

      const fees = [
        this.subjectData.meetingTravelFee,
        this.subjectData.dataCollectionFee,
        this.subjectData.materialsFee,
        this.subjectData.expertFee,
        this.subjectData.intermediaryFee,
        this.subjectData.laborFee,
        this.subjectData.printingFee,
        this.subjectData.taxFee,
        this.subjectData.otherFee
      ]

      const total = fees.reduce((sum, fee) => {
        const num = parseFloat(fee) || 0
        return sum + num
      }, 0)

      return total.toFixed(2)
    },

    // 计算编辑状态下的费用合计
    calculateEditTotalFee() {
      const fees = [
        this.budgetEditData.meetingTravelFee,
        this.budgetEditData.dataCollectionFee,
        this.budgetEditData.materialsFee,
        this.budgetEditData.expertFee,
        this.budgetEditData.intermediaryFee,
        this.budgetEditData.laborFee,
        this.budgetEditData.printingFee,
        this.budgetEditData.taxFee,
        this.budgetEditData.otherFee
      ]

      const total = fees.reduce((sum, fee) => {
        const num = parseFloat(fee) || 0
        return sum + num
      }, 0)

      return total.toFixed(2)
    },

    // // 编辑经费预算
    // editBudget() {
    //   // 将当前数据复制到编辑数据中
    //   this.budgetEditData = {
    //     projectIncome: this.subjectData.projectIncome || '',
    //     economicBudgetTotal: this.subjectData.economicBudgetTotal || '',
    //     projectManager: this.subjectData.projectManager || this.subjectData.projectLeader || '',
    //     meetingTravelFee: this.subjectData.meetingTravelFee || '',
    //     dataCollectionFee: this.subjectData.dataCollectionFee || '',
    //     materialsFee: this.subjectData.materialsFee || '',
    //     expertFee: this.subjectData.expertFee || '',
    //     intermediaryFee: this.subjectData.intermediaryFee || '',
    //     laborFee: this.subjectData.laborFee || '',
    //     printingFee: this.subjectData.printingFee || '',
    //     taxFee: this.subjectData.taxFee || '',
    //     otherFee: this.subjectData.otherFee || ''
    //   }
    //   this.budgetEditMode = true
    // },

    // 取消编辑经费预算
    cancelBudgetEdit() {
      this.budgetEditMode = false
      this.budgetEditData = {
        projectIncome: '',
        economicBudgetTotal: '',
        projectManager: '',
        meetingTravelFee: '',
        dataCollectionFee: '',
        materialsFee: '',
        expertFee: '',
        intermediaryFee: '',
        laborFee: '',
        printingFee: '',
        taxFee: '',
        otherFee: ''
      }
    },

    // 保存经费预算
    async saveBudgetEdit() {
      try {
        // 验证表单
        await this.$refs.budgetEditForm.validate()

        this.budgetSaving = true

        // 更新主数据
        Object.assign(this.subjectData, this.budgetEditData)

        // 这里应该调用API保存数据
        // await updateBudgetData(this.subjectData.id, this.budgetEditData)

        this.$message.success('经费预算保存成功')
        this.budgetEditMode = false

      } catch (error) {
        console.error('保存经费预算失败:', error)
        this.$message.error('保存失败，请检查输入数据')
      } finally {
        this.budgetSaving = false
      }
    },

    // 删除经费预算
    deleteBudget() {
      this.$confirm('确定要删除经费预算吗？删除后将无法恢复！', '警告', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: '<div style="color: #f56c6c;"><i class="el-icon-warning"></i> 此操作将永久删除经费预算数据，请谨慎操作！</div>'
      }).then(async () => {
        try {
          // 清空经费预算数据
          this.subjectData.projectIncome = ''
          this.subjectData.economicBudgetTotal = ''
          this.subjectData.projectManager = ''
          this.subjectData.meetingTravelFee = ''
          this.subjectData.dataCollectionFee = ''
          this.subjectData.materialsFee = ''
          this.subjectData.expertFee = ''
          this.subjectData.intermediaryFee = ''
          this.subjectData.laborFee = ''
          this.subjectData.printingFee = ''
          this.subjectData.taxFee = ''
          this.subjectData.otherFee = ''

          // 这里应该调用API删除数据
          // await deleteBudgetData(this.subjectData.id)

          this.$message.success('经费预算删除成功')

          // 如果正在编辑模式，退出编辑模式
          if (this.budgetEditMode) {
            this.budgetEditMode = false
          }

        } catch (error) {
          console.error('删除经费预算失败:', error)
          this.$message.error('删除失败，请重试')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 获取研究计划状态类型
    getResearchPlanStatusType(projectId) {
      const status = this.researchPlanStatus[projectId] || 0
      const typeMap = {
        0: 'info',     // 未填写
        1: 'warning',  // 已填写未提交
        2: 'success'   // 已填写已提交
      }
      return typeMap[status] || 'info'
    },

    // 获取研究计划状态文本
    getResearchPlanStatusText(projectId) {
      const status = this.researchPlanStatus[projectId] || 0
      const textMap = {
        0: '未填写',
        1: '已填写未提交',
        2: '已填写已提交'
      }
      return textMap[status] || '未知'
    },

    // 获取预立项信息
    getPreProjectInfo() {
      // 这里应该从实际的数据源获取预立项信息
      // 可以从 projectInfo 中获取，或者调用API

      // 如果项目已经提交预立项申请，返回相关信息
      if (['1', '2'].includes(this.subjectData.status)) {
        return {
          projectName: this.subjectData.subjectName || '未知项目',
          delegateUnit: this.subjectData.delegateUnit || '未知单位',
          projectNumber: this.subjectData.projectNumber || '未知编号',
          projectLeader: this.subjectData.leader || '未知负责人',
          projectStartTime: this.subjectData.startTime || '未知时间',
          estimatedIncome: this.subjectData.estimatedIncome || '0'
        }
      }

      // 默认返回空信息
      return {
        projectName: this.subjectData.subjectName || '未知项目',
        delegateUnit: '未知单位',
        projectNumber: '未知编号',
        projectLeader: this.subjectData.leader || '未知负责人',
        projectStartTime: '未知时间',
        estimatedIncome: '0'
      }
    },

    // 处理状态更新
    handleUpdateStatus(newStatus) {
      this.subjectData.status = newStatus
      this.$message.success('项目状态已更新')
    },

    // 打开编辑基本信息弹框
    openEditBasicInfoDialog() {
      this.editBasicInfoForm = {
        subjectName: this.subjectData.subjectName || '',
        delegateUnit: this.subjectData.delegateUnit || '',
        subjectNumber: this.subjectData.subjectNumber || '',
        projectLeader: this.subjectData.projectLeader || '',
        startTime: this.subjectData.startTime || '',
        endTime: this.subjectData.endTime || '',
        expectedContent: this.subjectData.expectedContent || '',
        expectedResults: this.subjectData.expectedResults || '',
        researchContent: this.subjectData.researchContent || ''
      }
      this.editBasicInfoDialogVisible = true
    },

    // 保存基本信息
    saveBasicInfo() {
      this.$refs.editBasicInfoForm.validate((valid) => {
        if (valid) {
          this.savingBasicInfo = true

          // 模拟保存
          setTimeout(() => {
            // 更新主数据
            this.subjectData.subjectName = this.editBasicInfoForm.subjectName
            this.subjectData.delegateUnit = this.editBasicInfoForm.delegateUnit
            this.subjectData.subjectNumber = this.editBasicInfoForm.subjectNumber
            this.subjectData.projectLeader = this.editBasicInfoForm.projectLeader
            this.subjectData.startTime = this.editBasicInfoForm.startTime
            this.subjectData.endTime = this.editBasicInfoForm.endTime
            this.subjectData.expectedContent = this.editBasicInfoForm.expectedContent
            this.subjectData.expectedResults = this.editBasicInfoForm.expectedResults
            this.subjectData.researchContent = this.editBasicInfoForm.researchContent

            this.savingBasicInfo = false
            this.editBasicInfoDialogVisible = false
            this.$message.success('基本信息保存成功')
          }, 1500)
        }
      })
    },

    // 初始化编辑表单数据
    initEditFormData() {
      this.editFormData = {
        projectName: this.subjectData.subjectName || '',
        delegateUnit: this.subjectData.delegateUnit || '',
        projectNumber: this.subjectData.subjectNumber || '',
        projectLeader: this.subjectData.projectLeader || '',
        expectedContent: this.subjectData.expectedContent || '',
        expectedResults: this.subjectData.expectedResults || '',
        researchContent: this.subjectData.researchContent || ''
      }
    }
  },

  mounted() {
    this.initEditFormData()
  }
}
</script>

<style scoped>
.process-detail-container {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 8px 20px;
  border-radius: 4px;
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}

.detail-header h2 {
  margin: 0 0 0 15px;
  color: #303133;
}

.detail-content {
  display: flex;
  gap: 20px;
  min-height: calc(100vh - 140px);
  max-width: 100%;
}

.detail-sidebar {
  min-width: 200px;
  max-width: 250px;
  flex-shrink: 0;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.sidebar-menu {
  border: none;
  width: 100%;
}

.detail-main {
  flex: 1;
  min-width: 0;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-section {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
}

/* 经费预算表样式 */
.budget-section {
  margin-bottom: 30px;
}

.budget-section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 2px solid #e8f4fd;
  padding-bottom: 10px;
}

.budget-section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.budget-row {
  margin-bottom: 20px;
}

.budget-item {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  height: 100%;
  border: 1px solid #e9ecef;
  transition: all 0.3s;
}

.budget-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.budget-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
  line-height: 1.4;
}

.budget-value {
  display: flex;
  align-items: baseline;
  justify-content: flex-end;
}

.budget-amount {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-right: 4px;
}

.budget-amount.highlight {
  color: #409eff;
  font-size: 20px;
}

.budget-unit {
  font-size: 12px;
  color: #909399;
}

/* 合计项特殊样式 */
.total-item {
  background-color: #e8f4fd;
  border-color: #409eff;
}

.total-label {
  color: #409eff;
  font-weight: bold;
  font-size: 15px;
}

.total-value .budget-amount {
  color: #409eff;
  font-size: 20px;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detail-content {
    flex-direction: column;
    gap: 15px;
  }

  .detail-sidebar {
    min-width: 100%;
    max-width: 100%;
    order: 2;
  }

  .detail-main {
    order: 1;
  }

  .budget-row .el-col {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .process-detail-container {
    padding: 10px;
  }

  .detail-content {
    min-height: calc(100vh - 100px);
    gap: 10px;
  }

  .content-section {
    padding: 15px;
  }

  .budget-item {
    padding: 12px;
  }

  .budget-amount {
    font-size: 16px;
  }

  .budget-amount.highlight {
    font-size: 18px;
  }

  .total-value .budget-amount {
    font-size: 18px;
  }

  .detail-header {
    padding: 8px 15px;
    margin-bottom: 15px;
  }

  .detail-header h2 {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .process-detail-container {
    padding: 5px;
  }

  .content-section {
    padding: 10px;
  }

  .detail-header {
    padding: 8px 10px;
    margin-bottom: 10px;
  }

  .detail-header h2 {
    font-size: 16px;
  }
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 编辑表单样式 */
.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 2px solid #e1e8ed;
  padding-bottom: 10px;
}

.section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.header-actions .el-button {
  margin-left: 0;
}

/* 经费编辑区域样式 */
.budget-edit-container {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
}

.budget-edit-section {
  margin-bottom: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.budget-edit-section .section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e8f4fd;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.budget-edit-section .section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

/* 编辑表单输入框样式 */
.budget-edit-container .el-input__inner {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.budget-edit-container .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.budget-edit-container .el-input-group__append {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #909399;
  font-weight: 500;
}

.budget-edit-container .total-input .el-input__inner {
  background-color: #e9ecef;
  font-weight: bold;
  color: #495057;
  text-align: center;
}

/* 编辑区域响应式 */
@media (max-width: 1200px) {
  .budget-edit-container .el-col {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .budget-edit-container {
    padding: 15px;
  }

  .budget-edit-section {
    padding: 15px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 全局组件自适应样式 */
.el-card {
  width: 100%;
  margin-bottom: 20px;
}

.el-table {
  width: 100%;
}

.el-descriptions {
  width: 100%;
}

.el-form {
  width: 100%;
}

.el-dialog {
  width: 90% !important;
  max-width: 1200px;
  margin: 0 auto;
}

/* 表格响应式 */
@media (max-width: 768px) {
  .el-table .el-table__cell {
    padding: 8px 4px;
    font-size: 12px;
  }

  .el-descriptions-item__label {
    font-size: 12px;
  }

  .el-descriptions-item__content {
    font-size: 12px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 20px auto;
  }

  /* 减少间距 */
  .el-card {
    margin-bottom: 15px !important;
  }

  .budget-section,
  .form-section,
  .budget-edit-section {
    margin-bottom: 20px !important;
  }

  .budget-section-title,
  .section-title {
    margin-bottom: 15px !important;
  }

  .budget-row {
    margin-bottom: 15px !important;
  }
}

@media (max-width: 480px) {
  .el-table .el-table__cell {
    padding: 6px 2px;
    font-size: 11px;
  }

  .el-descriptions {
    font-size: 11px;
  }

  .el-dialog {
    width: 98% !important;
    margin: 10px auto;
  }

  .el-form-item__label {
    font-size: 12px;
  }

  .el-input__inner {
    font-size: 12px;
  }

  .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }

  .el-button--small {
    padding: 6px 10px;
    font-size: 11px;
  }
}

/* 确保所有内容都不会溢出 */
* {
  box-sizing: border-box;
}

.el-table__body-wrapper {
  overflow-x: auto;
}

.el-descriptions__body {
  overflow-x: auto;
}
</style>
