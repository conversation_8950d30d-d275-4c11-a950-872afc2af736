<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width,initial-scale=1.0" name="viewport" />
    <link href="<%= BASE_URL %>favicon.ico" rel="icon" />
    <title>vue-admin-better官网、首页、文档和下载 - 前端开发框架</title>
    <meta
      content="vab,vab官网,后台管理框架,vue后台管理框架,vue-admin-better-pro源码分享,vue-admin-better-pro源码,vue-admin-beautiful官网,vue-admin-beautiful文档,vue-element-admin,vue-element-admin官网,vue-element-admin文档,vue-admin,vue-admin官网,vue-admin文档"
      name="keywords"
    />
    <meta
      content="<%= VUE_APP_TITLE %>官网与文档基于vue-admin-beautiful构建，简称vab（是一款超棒的vue+element中后台前端快速开发框架），QQ群972435319，作者：<%= VUE_APP_AUTHOR %>"
      name="description"
    />
    <meta content="<%= VUE_APP_AUTHOR %>" name="author" />
    <link href="<%= BASE_URL %>static/css/loading.css" rel="stylesheet" />
  </head>
  <body>
    <noscript>非常抱歉鉴于安全考量,您无法查看<%= VUE_APP_TITLE %> 源代码，该系统基于vue-admin-better开发</noscript>
    <div id="vue-admin-better">
      <div class="first-loading-wrp">
        <div class="loading-wrp">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
        </div>
        <h1><%= VUE_APP_TITLE %></h1>
      </div>
    </div>
    <script>
      ;/^http(s*):\/\//.test(location.href) || alert('基于vue-admin-beautiful-pro开源版开发的项目需要部署到服务器下访问')
    </script>
    <script>
      if (window.location.hostname !== 'localhost') {
        var _hmt = _hmt || []
        ;(function () {
          var hm = document.createElement('script')
          hm.src = 'https://hm.baidu.com/hm.js?7174bade1219f9cc272e7978f9523fc8'
          var s = document.getElementsByTagName('script')[0]
          s.parentNode.insertBefore(hm, s)
        })()
      }
    </script>
  </body>
</html>
