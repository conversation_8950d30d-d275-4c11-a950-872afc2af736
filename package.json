{"name": "vue-admin-better", "version": "2.6.0", "author": "zxwk1998", "participants": [], "homepage": "https://vuejs-core.cn", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "serve:node20": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:node20": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "serve:mac": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:mac": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "lint": "vue-cli-service lint --fix", "lint:eslint": "eslint {src,mock,library}/**/*.{vue,js} --fix", "lint:prettier": "prettier {src,mock,library}/**/*.{html,vue,css,sass,scss,js,ts,md} --write", "clear": "rimraf node_modules&&npm install  --registry=--registry=https://registry.npmmirror.com", "update": "ncu -u --reject vue-echarts,webpack,eslint-plugin-prettier,@vue/eslint-config-prettier,prettier,layouts,sass-loader,sass,screenfull,eslint,chalk,vue,vue-template-compiler,vue-router,vuex,@vue/cli-plugin-babel,@vue/cli-plugin-eslint,@vue/cli-service,eslint-plugin-vue --registry=https://registry.npmmirror.com&&pnpm i", "push": "start ./push.sh"}, "repository": {"type": "git", "url": "git+https://github.com/zxwk1998/vue-admin-better.git"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "dependencies": {"axios": "^1.10.0", "caniuse-lite": "^1.0.30001723", "clipboard": "^2.0.11", "core-js": "^3.43.0", "dayjs": "^1.11.13", "echarts": "5.6.0", "element-ui": "^2.15.14", "jsencrypt": "^3.3.2", "layouts": "file:layouts", "lodash": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "qs": "^6.14.0", "screenfull": "^5.2.0", "sortablejs": "^1.15.6", "vab-icon": "file:vab-icon", "vue": "~2.7.14", "vue-echarts": "6.7.3", "vue-router": "^3.6.5", "vue-template-compiler": "~2.7.14", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-service": "^4.5.19", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "body-parser": "^2.2.0", "chalk": "^4.1.2", "chokidar": "^4.0.3", "eslint": "^6.8.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^9.1.1", "image-webpack-loader": "^8.1.0", "install": "^0.13.0", "less": "^3.13.1", "less-loader": "^7.3.0", "lint-staged": "^16.1.2", "prettier": "^2.8.8", "sass": "~1.32.13", "sass-loader": "^10.1.1", "stylelint": "^16.20.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^7.1.0", "svg-sprite-loader": "^6.0.11", "webpack": "4.46.0", "webpackbar": "^7.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "vue-admin", "element-admin", "boilerplate", "admin-template", "management-system"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}}