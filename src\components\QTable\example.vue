<template>
  <div class="qtable-example">
    <h2>QTable 组件使用示例</h2>
    
    <!-- 基础表格 -->
    <div class="example-section">
      <h3>1. 基础表格</h3>
      <QTable
        :data="basicTableData"
        :columns="basicColumns"
        :config="basicConfig"
      />
    </div>

    <!-- 带状态标签的表格 -->
    <div class="example-section">
      <h3>2. 带状态标签的表格</h3>
      <QTable
        :data="statusTableData"
        :columns="statusColumns"
        :config="tableConfig"
        :status-config="statusConfig"
        :tag-config="tagConfig"
      />
    </div>

    <!-- 带选择功能的表格 -->
    <div class="example-section">
      <h3>3. 带选择功能的表格</h3>
      <QTable
        :data="selectionTableData"
        :columns="selectionColumns"
        :config="tableConfig"
        @selection-change="handleSelectionChange"
      />
      <p v-if="selectedRow">当前选中：{{ selectedRow.name }}</p>
    </div>

    <!-- 带操作列的表格 -->
    <div class="example-section">
      <h3>4. 带操作列的表格</h3>
      <QTable
        :data="operationTableData"
        :columns="operationColumns"
        :config="tableConfig"
      >
        <template #operation>
          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </template>
      </QTable>
    </div>

    <!-- 综合示例 -->
    <div class="example-section">
      <h3>5. 综合示例（选择 + 状态 + 操作）</h3>
      <QTable
        :data="comprehensiveTableData"
        :columns="comprehensiveColumns"
        :config="tableConfig"
        :status-config="statusConfig"
        :tag-config="tagConfig"
        @selection-change="handleSelectionChange"
      >
        <template #operation>
          <el-table-column label="操作" width="200" align="center">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleView(scope.row)">查看</el-button>
              <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </template>
      </QTable>
    </div>
  </div>
</template>

<script>
import QTable from './index.vue'

export default {
  name: 'QTableExample',
  components: {
    QTable
  },
  data() {
    return {
      selectedRow: null,
      
      // 基础表格数据
      basicTableData: [
        { id: 1, name: '张三', age: 25, department: '技术部' },
        { id: 2, name: '李四', age: 30, department: '产品部' },
        { id: 3, name: '王五', age: 28, department: '设计部' }
      ],
      basicColumns: [
        { label: 'ID', prop: 'id', width: '80' },
        { label: '姓名', prop: 'name', width: '120' },
        { label: '年龄', prop: 'age', width: '80' },
        { label: '部门', prop: 'department', width: '120' }
      ],
      basicConfig: {
        stripe: true,
        border: true
      },

      // 状态表格数据
      statusTableData: [
        { id: 1, name: '项目A', status: 0, progress: '10%' },
        { id: 2, name: '项目B', status: 1, progress: '100%' },
        { id: 3, name: '项目C', status: 2, progress: '60%' }
      ],
      statusColumns: [
        { label: 'ID', prop: 'id', width: '80' },
        { label: '项目名称', prop: 'name', width: '200' },
        { label: '状态', type: 'tag', width: '100' },
        { label: '进度', prop: 'progress', width: '100' }
      ],

      // 选择表格数据
      selectionTableData: [
        { id: 1, name: '张三', age: 25, isSelected: false },
        { id: 2, name: '李四', age: 30, isSelected: false },
        { id: 3, name: '王五', age: 28, isSelected: false }
      ],
      selectionColumns: [
        { type: 'selection' },
        { label: 'ID', prop: 'id', width: '80' },
        { label: '姓名', prop: 'name', width: '120' },
        { label: '年龄', prop: 'age', width: '80' }
      ],

      // 操作表格数据
      operationTableData: [
        { id: 1, name: '数据A', type: '类型1', createTime: '2024-01-01' },
        { id: 2, name: '数据B', type: '类型2', createTime: '2024-01-02' },
        { id: 3, name: '数据C', type: '类型1', createTime: '2024-01-03' }
      ],
      operationColumns: [
        { label: 'ID', prop: 'id', width: '80' },
        { label: '名称', prop: 'name', width: '120' },
        { label: '类型', prop: 'type', width: '100' },
        { label: '创建时间', prop: 'createTime', width: '120' }
      ],

      // 综合示例数据
      comprehensiveTableData: [
        { id: 1, name: '任务A', status: 0, assignee: '张三', isSelected: false },
        { id: 2, name: '任务B', status: 1, assignee: '李四', isSelected: false },
        { id: 3, name: '任务C', status: 2, assignee: '王五', isSelected: false }
      ],
      comprehensiveColumns: [
        { type: 'selection' },
        { label: 'ID', prop: 'id', width: '80' },
        { label: '任务名称', prop: 'name', width: '150' },
        { label: '状态', type: 'tag', width: '100' },
        { label: '负责人', prop: 'assignee', width: '100' }
      ],

      // 通用配置
      tableConfig: {
        stripe: true,
        border: true,
        height: '300px'
      },

      // 状态配置
      statusConfig: ['info', 'success', 'warning'],
      tagConfig: ['待开始', '已完成', '进行中']
    }
  },
  methods: {
    handleSelectionChange(row) {
      this.selectedRow = row
      console.log('选中的行:', row)
    },
    
    handleView(row) {
      this.$message.info(`查看：${row.name}`)
    },
    
    handleEdit(row) {
      this.$message.info(`编辑：${row.name}`)
    },
    
    handleDelete(row) {
      this.$confirm(`确认删除 ${row.name}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    }
  }
}
</script>

<style scoped>
.qtable-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
}

.example-section h3 {
  margin-bottom: 15px;
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
}

.example-section p {
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  color: #409EFF;
}
</style>
