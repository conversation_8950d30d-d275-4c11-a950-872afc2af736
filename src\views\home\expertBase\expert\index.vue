<template>
  <div style="background-color: white; padding: 20px">
    <!-- 查询工具栏 -->
    <el-form ref="queryForm" :model="queryForm" :rules="formRules" label-width="80px">
      <!-- 第一行查询条件 -->
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="专家姓名">
            <el-input v-model="queryForm.subjectName" clearable placeholder="请输入专家姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="专家来源">
            <el-input v-model="queryForm.leader" clearable placeholder="请输入专家来源"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="专业领域">
            <el-input v-model="queryForm.researchMethod" clearable placeholder="请输入专业领域" filterable>
              <!-- <el-option label="人工智能" value="人工智能"></el-option>
              <el-option label="生物医学" value="生物医学"></el-option> -->
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="专家标签">
            <el-select v-model="queryForm.researchMethod" clearable placeholder="请选择研专家标签">
              <el-option label="人工智能" value="人工智能"></el-option>
              <el-option label="生物医学" value="生物医学"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 操作工具栏 -->
    <div style="display: flex; margin-bottom: 20px">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="primary" @click="importDialogVisible = true">导入</el-button>
      <el-button type="primary" @click="handleExport">导出</el-button>
      <el-button type="primary" @click="assigningTask">项目分配</el-button>

      <div style="margin-left: auto">
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
      </div>
    </div>


    <!-- 表格 -->
    <el-table :data="currentPageData" border stripe style="width: 100%" height="400px"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="id" label="序号" width="50"></el-table-column>
      <el-table-column prop="subjectName" label="专家姓名" show-overflow-tooltip width="150"></el-table-column>
      <el-table-column prop="subjectSource" label="专家职称" show-overflow-tooltip width="150"></el-table-column>
      <el-table-column prop="leader" label="专家来源" width="150"></el-table-column>
      <el-table-column prop="researchMethod" label="专业领域" width="150"></el-table-column>
      <el-table-column prop="mainContent" label="个人简介" show-overflow-tooltip width="150"></el-table-column>
      <!-- <el-table-column prop="researchPeriod" label="专家工作记录" width="200">
        <template slot-scope="{ row }">
          <span v-if="row.startTime && row.endTime">
            {{ calculateDuration(row.startTime, row.endTime) }}
          </span>
          <span v-else>--</span>
        </template>
</el-table-column> -->
      <!-- 添加任务时长列 -->
      <el-table-column prop="taskDuration" label="工作时长" width="120">
        <template #default="{ row }">
          <span v-if="row.assigned && (row.duration > 0 || row.taskStartTime && row.taskEndTime)">
            {{ getTaskDurationText(row) }}
          </span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <!-- 添加项目分配状态列 -->
      <el-table-column prop="assignmentStatus" label="项目分配状态" width="120">
        <template #default="{ row }">
          <el-tag :type="row.assigned ? 'success' : 'info'">
            {{ row.assigned ? '已分配' : '未分配' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="tag" label="标签" width="350">
        <template #default="{ row }">
          <el-tag v-for="(tag, index) in row.tags" :key="index" :type="tag.type">
            {{ tag.name }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="350 ">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleOpenTagDialog(scope.row)">专家标签</el-button>
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="primary" @click="grantMoney(scope.row)">经费发放</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div style="margin-top: 20px;float:right">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="totalCount">
      </el-pagination>
    </div>


    <!-- 导入对话框 -->
    <el-dialog title="导入数据" :visible.sync="importDialogVisible" width="60%">
      <!-- 下载模板区域 -->
      <div class="import-section download-section">
        <h4>下载模板</h4>
        <p>请先下载模板，按格式填写后再上传</p>
        <el-button type="success" @click="downloadTemplate" icon="el-icon-download" size="medium" class="import-button">
          下载模板
        </el-button>
      </div>

      <div style="margin: 20px 0;"></div>

      <!-- 上传文件区域 -->
      <div class="import-section upload-section">
        <h4>上传文件</h4>
        <p>只能上传 xls/xlsx 文件，且不超过 10MB</p>
        <el-upload class="custom-upload" action="https://jsonplaceholder.typicode.com/posts/"
          :on-preview="handlePreview" :on-remove="handleRemove" :before-upload="beforeUpload" :limit="1"
          accept=".xls,.xlsx">
          <el-button size="medium" type="primary" class="import-button">
            <i class="el-icon-upload2"></i> 点击上传
          </el-button>
        </el-upload>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitUpload">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 标签选择对话框 -->
    <el-dialog title="选择标签" :visible.sync="tagDialogVisible" width="40%">
      <el-tabs type="card">
        <el-tab-pane label="专长领域">
          <el-checkbox-group v-model="selectedTags.category1">
            <el-checkbox v-for="tag in allTags.category1" :key="tag.name" :label="tag.name">
              <el-tag :type="tag.type">{{ tag.name }}</el-tag>
            </el-checkbox>
          </el-checkbox-group>
        </el-tab-pane>

        <el-tab-pane label="可用性">
          <el-checkbox-group v-model="selectedTags.category2">
            <el-checkbox v-for="tag in allTags.category2" :key="tag.name" :label="tag.name">
              <el-tag :type="tag.type">{{ tag.name }}</el-tag>
            </el-checkbox>
          </el-checkbox-group>
        </el-tab-pane>

        <el-tab-pane label="其他标签">
          <el-checkbox-group v-model="selectedTags.category3">
            <el-checkbox v-for="tag in allTags.category3" :key="tag.name" :label="tag.name">
              <el-tag :type="tag.type">{{ tag.name }}</el-tag>
            </el-checkbox>

          </el-checkbox-group>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" class="dialog-footer">
        <div>
          <el-input v-model="customTagName" placeholder="输入自定义标签名称" style="width: 200px; " />
          <el-button type="primary" @click="addCustomTag">添加</el-button>
        </div>
        <div style="margin-left: 150px;">
          <el-button @click="tagDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmTags">确 定</el-button>
        </div>
      </span>

    </el-dialog>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" @close="resetForm">
      <el-form ref="dataForm" :model="formData" :rules="formRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="专家姓名" prop="subjectName">
              <el-input v-model="formData.subjectName" placeholder="请输入专家姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专家职称" prop="subjectSource">
              <el-select v-model="formData.subjectSource" placeholder="请输入专家职称">
                <el-option label="院士级" value="院士级"></el-option>
                <el-option label="中级" value="中级"></el-option>
                <el-option label="高级" value="高级"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专家来源" prop="leader">
              <el-input v-model="formData.leader" placeholder="请输入专家来源" style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="专业领域" prop="researchContent">
              <el-input v-model="formData.researchMethod" placeholder="请选择专业领域" style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
          <!-- 添加分配状态显示 -->
          <el-col :span="12">
            <el-form-item label="分配状态">
              <el-tag :type="formData.assigned ? 'success' : 'info'">
                {{ formData.assigned ? '已分配' : '未分配' }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 添加分配项目信息显示（仅在已分配时显示） -->
        <el-row v-if="formData.assigned" :gutter="20">
          <el-col :span="24">
            <el-form-item label="项目名称">
              <el-input v-model="formData.assignedProject" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 添加具体工作内容显示（仅在已分配时显示） -->
        <el-row v-if="formData.assigned" :gutter="20">
          <el-col :span="24">
            <el-form-item label="项目工作内容">
              <el-input type="textarea" v-model="formData.workContent" :rows="3" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 添加工作时长显示（仅在已分配时显示） -->
        <el-row v-if="formData.assigned" :gutter="20">
          <el-col :span="12">
            <el-form-item label="工作时长">
              <el-input :value="getDurationText(formData.duration, formData.timeUnit)" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="个人简介" prop="mainContent">
          <el-input v-model="formData.mainContent" type="textarea" :rows="4" placeholder="请输入个人简介"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 项目列表对话框 -->
    <el-dialog title="项目列表" :visible.sync="projectDialogVisible" width="80%" @close="handleProjectDialogClose">
      <!-- 项目查询条件 -->
      <el-form :inline="true" :model="projectQueryForm" ref="projectQueryForm" class="project-query-form">
        <el-form-item label="项目编号">
          <el-input v-model="projectQueryForm.projectNumber" placeholder="请输入项目编号" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleProjectQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="handleProjectReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 项目表格 -->
      <el-table :data="currentProjectPageData" border stripe style="width: 100%" height="400px"
        @selection-change="handleProjectSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="序号" width="60"></el-table-column>
        <el-table-column prop="projectNumber" label="项目编号" width="150"></el-table-column>
        <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="delegateUnit" label="委托单位" width="200"></el-table-column>
        <el-table-column prop="projectLeader" label="项目负责人" width="120"></el-table-column>
        <el-table-column prop="projectStartTime" label="立项时间" width="120"></el-table-column>
        <el-table-column prop="estimatedIncome" label="预估收入(万元)" width="150"></el-table-column>
      </el-table>

      <!-- 项目分页 -->
      <div style="margin-top: 20px; text-align: right; float:right">
        <el-pagination @size-change="handleProjectSizeChange" @current-change="handleProjectCurrentChange"
          :current-page="projectCurrentPage" :page-sizes="[10, 20, 50, 100]" :page-size="projectPageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="projectTotalCount">
        </el-pagination>
      </div>

      <!-- 项目分配表单 -->
      <div class="task-assignment-form">
        <h3>项目分配信息</h3>
        <el-form :model="taskForm" ref="taskForm" label-width="120px">
          <!-- 项目名称显示 -->
          <el-form-item label="项目名称">
            <el-input v-model="selectedProjectName" disabled placeholder="请先选择项目"></el-input>
          </el-form-item>

          <el-form-item label="具体工作内容" prop="workContent">
            <el-input type="textarea" v-model="taskForm.workContent" :rows="4" placeholder="请输入具体工作内容">
            </el-input>
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="工作时长" prop="duration">
                <div style="display: flex; align-items: center;">
                  <el-input-number v-model="taskForm.duration" :min="0" controls-position="right" size="medium"
                    class="duration-input">
                  </el-input-number>
                  <el-select v-model="taskForm.timeUnit" size="medium" class="unit-select" style="margin-left: 10px;">
                    <el-option label="天" value="day"></el-option>
                    <el-option label="小时" value="hour"></el-option>
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 项目列表对话框的底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="projectDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAssignment"
          :disabled="!selectedProjectRows || selectedProjectRows.length === 0">确认分配项目并发送短信</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
export default {
  name: 'PreProjectInitiation',
  components: {},
  data() {
    return {
      // 静态数据
      tableData: [
        {
          id: 1,
          subjectName: '张教授',
          subjectSource: '院士级',
          leader: '科技有限公司',
          researchMethod: '人工智能',
          mainContent: '56篇论文',
          expectedResults: '标签',
          startTime: '2025-07-17',
          endTime: '2025-12-17',
          mainContent: '56篇论文',
          researchPeriod: '2025/7/17----2025/12/17',
          tags: [
            { name: '人工智能', type: 'success' },
            { name: '算法', type: 'warning' },
            { name: '内部专家', type: 'primary' },
          ],
          status: '1',
          assigned: false,
          assignedProject: '人工智能算法优化项目',
          workContent: '',
          taskStartTime: '',
          taskEndTime: '',
          duration: 0,
          timeUnit: 'day'
        },
        {
          id: 2,
          subjectName: '李研究员',
          subjectSource: '中级',
          leader: '中国科学院',
          researchMethod: '计算机视觉',
          mainContent: '8项专利，42篇论文',
          expectedResults: '标签',
          mainContent: '8项专利，42篇论文',
          startTime: '2024-01-01',
          endTime: '2026-12-31',
          researchPeriod: '2024/1/1----2026/12/31',
          tags: [
            { name: '计算机', type: 'info' },
            { name: '算法', type: 'warning' },
            { name: '长期合作', type: 'success' },
          ],
          status: '1',
          assigned: true,
          assignedProject: '人工智能算法优化项目',
          workContent: '负责梳理数据，前期调研，并编写项目计划',
          taskStartTime: '2025-01-01',
          taskEndTime: '2025-06-01',
          duration: 30,
          timeUnit: 'day'
        },
        {
          id: 3,
          subjectName: '王院士',
          subjectSource: '高级',
          leader: '浙江大学',
          researchMethod: '生物医学',
          mainContent: '128篇论文',
          expectedResults: '标签',
          mainContent: '128篇论文',
          startTime: '2024-03-01',
          endTime: '2027-02-28',
          researchPeriod: '2024/3/1----2027/2/28',
          tags: [
            { name: '生物医学工程', type: 'success' },
            { name: '经验丰富', type: 'success' },
            { name: '风趣幽默', type: 'warning' },
          ],
          status: '1',
          assigned: true,
          assignedProject: '人工智能算法优化项目',
          workContent: '负责把控任务的进度和完成情况，以及负责人员安排',
          taskStartTime: '2025-07-01',
          taskEndTime: '2025-07-11',
          duration: 120,
          timeUnit: 'hour'
        },
        {
          id: 4,
          subjectName: '李院士',
          subjectSource: '院士级',
          leader: '西安建筑科技大学',
          researchMethod: '建筑工程',
          mainContent: '128篇论文',
          expectedResults: '标签',
          mainContent: '128篇论文',
          startTime: '2024-03-01',
          endTime: '2027-02-28',
          researchPeriod: '2024/3/1----2027/2/28',
          tags: [
            { name: '建筑工程', type: 'success' },
            { name: '长期合作', type: 'success' },
            { name: '实战派', type: 'success' },
          ],
          status: '1',
          assigned: false,
          assignedProject: '人工智能算法优化项目',
          workContent: '',
          taskStartTime: '',
          taskEndTime: '',
          duration: 0,
          timeUnit: 'day'
        },
        {
          id: 5,
          subjectName: '张博士',
          subjectSource: '中级',
          leader: '西安科技大学',
          researchMethod: '生物医学',
          mainContent: '128篇论文',
          expectedResults: '标签',
          mainContent: '128篇论文',
          startTime: '2024-03-01',
          endTime: '2027-02-28',
          researchPeriod: '2024/3/1----2027/2/28',
          tags: [
            { name: '生物医学工程', type: 'success' },
            { name: '紧急可用', type: 'danger' },
            { name: '外部专家', type: 'primary' },
          ],
          status: '1',
          assigned: false,
          assignedProject: '人工智能算法优化项目',
          workContent: '',
          taskStartTime: '',
          taskEndTime: '',
          duration: 0,
          timeUnit: 'day'
        }
      ],

      // 项目数据
      projectTableData: [
        {
          id: 1,
          projectNumber: 'PROJ2024001',
          projectName: '人工智能算法优化项目',
          delegateUnit: '华为技术有限公司',
          projectLeader: '张三',
          projectStartTime: '2024-01-15',
          estimatedIncome: '120.5'
        },
        {
          id: 2,
          projectNumber: 'PROJ2024002',
          projectName: '生物医学数据分析平台',
          delegateUnit: '中国科学院',
          projectLeader: '李四',
          projectStartTime: '2024-02-20',
          estimatedIncome: '85.0'
        },
        {
          id: 3,
          projectNumber: 'PROJ2024003',
          projectName: '智慧城市交通管理系统',
          delegateUnit: '北京市交通委员会',
          projectLeader: '王五',
          projectStartTime: '2024-03-10',
          estimatedIncome: '210.8'
        },
        {
          id: 4,
          projectNumber: 'PROJ2024004',
          projectName: '新能源材料研发',
          delegateUnit: '宁德时代新能源科技',
          projectLeader: '赵六',
          projectStartTime: '2024-04-05',
          estimatedIncome: '156.3'
        },
        {
          id: 5,
          projectNumber: 'PROJ2024005',
          projectName: '金融风控模型构建',
          delegateUnit: '蚂蚁金服',
          projectLeader: '钱七',
          projectStartTime: '2024-05-12',
          estimatedIncome: '98.7'
        },
        {
          id: 6,
          projectNumber: 'PROJ2024006',
          projectName: '大数据处理平台开发',
          delegateUnit: '腾讯科技',
          projectLeader: '孙八',
          projectStartTime: '2024-06-18',
          estimatedIncome: '188.2'
        },
        {
          id: 7,
          projectNumber: 'PROJ2024007',
          projectName: '物联网安全研究',
          delegateUnit: '阿里巴巴',
          projectLeader: '周九',
          projectStartTime: '2024-07-22',
          estimatedIncome: '135.6'
        }
      ],

      //标签
      tagDialogVisible: false,

      allTags: {
        category1: [
          { name: '人工智能', type: 'success' },
          { name: '机器学习', type: 'success' },
          { name: '深度学习', type: 'success' },
          { name: '自然语言处理', type: 'success' },
          { name: '计算机视觉', type: 'success' },
          { name: '生物医学工程', type: 'success' },
          { name: '数据科学', type: 'success' },
          { name: '智能制造', type: 'success' },
          { name: '自动化控制', type: 'success' },
          { name: '财务管理', type: 'success' },
          { name: '建筑工程', type: 'success' },
          { name: '心理学', type: 'success' },
          { name: '计算机', type: 'info' },
          { name: '算法', type: 'warning' }
        ],
        category2: [
          { name: '可用', type: 'success' },
          { name: '不可用', type: 'info' },
          { name: '出差中', type: 'warning' },
          { name: '休假中', type: 'warning' },
          { name: '紧急可用', type: 'danger' },
          { name: '长期合作', type: 'success' },
          { name: '短期合作', type: 'primary' },
          { name: '内部专家', type: 'primary' },
          { name: '外部专家', type: 'primary' }
        ],
        category3: [
          { name: '专业', type: '' },
          { name: '亲和力强', type: 'success' },
          { name: '逻辑强', type: 'info' },
          { name: '风趣幽默', type: 'warning' },
          { name: '深入浅出', type: 'danger' },
          { name: '内容详实', type: 'info' },
          { name: '经验丰富', type: 'success' },
          { name: '代码高手', type: 'danger' },
          { name: '讲师型', type: '' },
          { name: '学者型', type: '' },
          { name: '实战派', type: 'success' },
          { name: '理论派', type: 'info' }
        ]
      },
      selectedTags: {
        category1: [],
        category2: [],
        category3: []
      },

      // 分页相关
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,

      importDialogVisible: false, // 控制导入弹窗显示

      // 对话框相关
      dialogVisible: false,
      viewDialogVisible: false,
      dialogTitle: '新增专家',
      isEdit: false,
      editId: null,
      viewData: null,

      // 项目对话框相关
      projectDialogVisible: false,
      projectCurrentPage: 1,
      projectPageSize: 10,
      projectTotalCount: 0,
      projectQueryForm: {
        projectNumber: ''
      },
      projectOriginalTableData: [],
      selectedRows: [], // 选中的专家行
      selectedProjectRows: [], // 选中的项目行
      selectedProjectName: '', // 选中的项目名称显示

      // 选中的项目
      selectedProject: null,

      // 任务表单
      taskForm: {
        workContent: '',
        duration: '',
        timeUnit: 'day'
      },

      // 发送短信状态
      sendingMessage: false,

      // 表单数据
      formData: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        status: '',
        mainContent: '',
        expectedResults: '',
        tags: []
      },
      // 表单验证规则
      formRules: {
        subjectName: [
          { required: true, message: '请输入专家姓名', trigger: 'blur' }
        ],
        subjectSource: [
          { required: true, message: '请选择专家职称', trigger: 'change' }
        ],
        leader: [
          { required: true, message: '请输入专家来源', trigger: 'blur' }
        ],
        researchMethod: [
          { required: true, message: '请选择专业领域', trigger: 'change' }
        ],
        mainContent: [
          { required: true, message: '请输入个人简介', trigger: 'blur' }
        ]
      },
      queryForm: {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        mainContent: '',
        contentAndResults: '',
        approvalStatus: ''
      },
      // 原始数据备份，用于查询重置
      originalTableData: []
    }
  },

  computed: {
    // 当前页数据
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.tableData.slice(start, end)
    },

    // 当前项目页数据
    currentProjectPageData() {
      const start = (this.projectCurrentPage - 1) * this.projectPageSize
      const end = start + this.projectPageSize
      return this.projectTableData.slice(start, end)
    }
  },

  created() {
    // 备份原始数据
    this.originalTableData = JSON.parse(JSON.stringify(this.tableData))
    this.totalCount = this.tableData.length

    // 备份项目原始数据
    this.projectOriginalTableData = JSON.parse(JSON.stringify(this.projectTableData))
    this.projectTotalCount = this.projectTableData.length
  },

  methods: {
    // 表格选择变化
    handleSelectionChange(val) {
      this.selectedRows = val
    },

    // 项目表格选择变化
    handleProjectSelectionChange(val) {
      this.selectedProjectRows = val;

      // 更新显示的项目名称
      if (val && val.length > 0) {
        if (val.length === 1) {
          this.selectedProjectName = val[0].projectName;
        } else {
          this.selectedProjectName = `已选择${val.length}个项目`;
        }
      } else {
        this.selectedProjectName = '';
      }
    },

    // 计算时间差（年、月、天）
    calculateDuration(startDate, endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return '--';
      }

      let years = end.getFullYear() - start.getFullYear();
      let months = end.getMonth() - start.getMonth();
      let days = end.getDate() - start.getDate();

      if (days < 0) {
        months--;
        const prevMonth = new Date(end.getFullYear(), end.getMonth(), 0).getDate();
        days = prevMonth + days;
      }

      if (months < 0) {
        years--;
        months += 12;
      }

      let result = '';
      if (years > 0) result += `${years}年`;
      if (months > 0) result += `${months}个月`;
      if (days > 0 || (years === 0 && months === 0)) result += `${days}天`;

      return result || '--';
    },

    // 获取任务时长文本
    getTaskDurationText(row) {
      if (row.duration > 0) {
        return `${row.duration}${row.timeUnit === 'day' ? '天' : '小时'}`;
      }

      if (row.taskStartTime && row.taskEndTime) {
        const start = new Date(row.taskStartTime);
        const end = new Date(row.taskEndTime);
        if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
          const diffTime = Math.abs(end - start);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return `${diffDays}天`;
        }
      }

      return '--';
    },

    // 获取时长文本显示
    getDurationText(duration, timeUnit) {
      if (!duration || duration <= 0) return '未设置';
      return `${duration}${timeUnit === 'day' ? '天' : '小时'}`;
    },

    // 查询功能
    handleQuery() {
      let filteredData = JSON.parse(JSON.stringify(this.originalTableData))

      // 根据专家姓名筛选
      if (this.queryForm.subjectName && this.queryForm.subjectName.trim()) {
        filteredData = filteredData.filter(item =>
          item.subjectName.toLowerCase().includes(this.queryForm.subjectName.trim().toLowerCase())
        )
      }

      // 根据专家来源筛选
      if (this.queryForm.leader && this.queryForm.leader.trim()) {
        filteredData = filteredData.filter(item =>
          item.leader === this.queryForm.leader
        )
      }

      // 根据专业领域筛选
      if (this.queryForm.researchMethod && this.queryForm.researchMethod.trim()) {
        filteredData = filteredData.filter(item =>
          item.researchMethod === this.queryForm.researchMethod
        )
      }

      this.tableData = filteredData
      this.totalCount = filteredData.length
      this.currentPage = 1

      this.$message.success(`查询完成，共找到 ${filteredData.length} 条记录`)
    },

    // 重置功能
    handleReset() {
      // 重置查询表单
      this.queryForm = {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        contentAndResults: '',
        approvalStatus: ''
      }

      // 恢复原始数据
      this.tableData = JSON.parse(JSON.stringify(this.originalTableData))
      this.totalCount = this.originalTableData.length
      this.currentPage = 1

      // 重置表单验证（如果有）
      if (this.$refs.queryForm) {
        this.$refs.queryForm.resetFields()
      }

      this.$message.success('重置成功')
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    // 当前页变化
    handleCurrentChange(val) {
      this.currentPage = val
    },
    // 下载模板
    downloadTemplate() {
      // 替换为你模板的 URL 或本地路径
      const templateUrl = '/static/templates/expert_template.xlsx'
      const link = document.createElement('a')
      link.href = templateUrl
      link.download = '专家信息模板.xlsx' // 下载文件名
      link.click()
    },

    // 上传前校验
    beforeUpload(file) {
      const isValidType = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type)
      const isValidSize = file.size / 1024 / 1024 < 10 // 10MB

      if (!isValidType) {
        this.$message.error('只能上传 xls/xlsx 格式的文件')
        return false
      }

      if (!isValidSize) {
        this.$message.error('上传文件大小不能超过 10MB')
        return false
      }

      return true
    },

    // 处理预览
    handlePreview(file) {
      console.log('预览文件:', file)
    },

    // 处理移除
    handleRemove(file, fileList) {
      console.log('文件已移除', file, fileList)
    },

    // 确定上传
    submitUpload() {
      // 如果使用 el-upload 的 action，会自动上传
      // 如果需要手动上传，可以调用接口
      this.$message.success('文件上传成功')
      this.importDialogVisible = false
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增专家'
      this.isEdit = false
      this.editId = null
      this.resetForm()
      this.dialogVisible = true
    },
    grantMoney(row) {
      this.$message.success('发放成功')
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑专家';
      this.isEdit = true;
      this.editId = row.id;

      this.formData = {
        subjectName: row.subjectName,
        subjectSource: row.subjectSource,
        leader: row.leader,
        researchMethod: row.researchMethod,
        status: row.status,
        mainContent: row.mainContent,
        expectedResults: row.expectedResults,
        tags: row.tags || [],
        assigned: row.assigned || false,
        assignedProject: row.assignedProject || '',
        workContent: row.workContent || '',
        duration: row.duration || 0,
        timeUnit: row.timeUnit || 'day'
      };

      this.dialogVisible = true;
    },

    //专家标签
    handleOpenTagDialog(row) {
      this.currentRow = row;
      this.selectedTags = {
        category1: [],
        category2: [],
        category3: []
      };

      // 回显已选标签
      const currentTagNames = row.tags.map(tag => tag.name);
      this.selectedTags.category1 = this.allTags.category1
        .filter(tag => currentTagNames.includes(tag.name))
        .map(tag => tag.name);
      this.selectedTags.category2 = this.allTags.category2
        .filter(tag => currentTagNames.includes(tag.name))
        .map(tag => tag.name);
      this.selectedTags.category3 = this.allTags.category3
        .filter(tag => currentTagNames.includes(tag.name))
        .map(tag => tag.name);

      this.tagDialogVisible = true;
    },

    confirmTags() {
      const selectedNames = [
        ...this.selectedTags.category1,
        ...this.selectedTags.category2,
        ...this.selectedTags.category3
      ];

      // 从 allTags 中筛选出选中的标签对象
      const selectedTagObjects = Object.values(this.allTags).flat().filter(tag =>
        selectedNames.includes(tag.name)
      );

      this.currentRow.tags = selectedTagObjects;

      this.tagDialogVisible = false;

      // 强制更新表格
      this.tableData = [...this.tableData];
    },

    resetTagsSelection() {
      this.selectedTags.category1 = [];
      this.selectedTags.category2 = [];
      this.selectedTags.category3 = [];
    },

    handleClose(row, index) {
      row.tags.splice(index, 1);
      this.tableData = [...this.tableData]; // 强制更新表格
    },

    // 项目分配
    assigningTask() {
      if (!this.selectedRows || this.selectedRows.length === 0) {
        this.$message.warning('请先选择至少一个专家');
        return;
      }
      // 显示项目列表对话框
      this.projectDialogVisible = true;
    },

    // 确认分配任务
    confirmAssignment() {
      if (!this.selectedProjectRows || this.selectedProjectRows.length === 0) {
        this.$message.warning('请先选择至少一个项目');
        return;
      }

      // 检查任务表单信息是否完整
      if (!this.taskForm.workContent) {
        this.$message.warning('请输入具体工作内容');
        return;
      }

      if (!this.taskForm.duration || this.taskForm.duration <= 0) {
        this.$message.warning('请输入有效的工作时长');
        return;
      }

      this.$confirm(`确定为选中的${this.selectedRows.length}位专家分配选中的${this.selectedProjectRows.length}个项目并发送短信吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 更新专家分配状态
        this.selectedRows.forEach(expert => {
          const index = this.tableData.findIndex(item => item.id === expert.id);
          if (index > -1) {
            this.$set(this.tableData[index], 'assigned', true);
            // 如果分配多个项目，显示项目数量；如果分配一个项目，显示项目名称
            if (this.selectedProjectRows.length === 1) {
              this.$set(this.tableData[index], 'assignedProject', this.selectedProjectRows[0].projectName);
            } else {
              this.$set(this.tableData[index], 'assignedProject', `已分配${this.selectedProjectRows.length}个项目`);
            }
            // 保存工作内容和时间信息
            this.$set(this.tableData[index], 'workContent', this.taskForm.workContent);
            this.$set(this.tableData[index], 'duration', this.taskForm.duration);
            this.$set(this.tableData[index], 'timeUnit', this.taskForm.timeUnit);
          }

          const originalIndex = this.originalTableData.findIndex(item => item.id === expert.id);
          if (originalIndex > -1) {
            this.$set(this.originalTableData[originalIndex], 'assigned', true);
            if (this.selectedProjectRows.length === 1) {
              this.$set(this.originalTableData[originalIndex], 'assignedProject', this.selectedProjectRows[0].projectName);
            } else {
              this.$set(this.originalTableData[originalIndex], 'assignedProject', `已分配${this.selectedProjectRows.length}个项目`);
            }
            // 保存工作内容和时间信息
            this.$set(this.originalTableData[originalIndex], 'workContent', this.taskForm.workContent);
            this.$set(this.originalTableData[originalIndex], 'duration', this.taskForm.duration);
            this.$set(this.originalTableData[originalIndex], 'timeUnit', this.taskForm.timeUnit);
          }
        });

        // 自动发送短信
        this.autoSendTaskMessage();
      }).catch(() => {
        this.$message.info('已取消分配');
      });
    },

    // 自动发送任务短信
    autoSendTaskMessage() {
      if (!this.selectedProjectRows || this.selectedProjectRows.length === 0) {
        return;
      }

      this.sendingMessage = true;

      // 模拟发送短信的API调用
      setTimeout(() => {
        this.sendingMessage = false;
        this.$message.success(`任务短信已成功发送给${this.selectedRows.length}位专家，分配完成`);

        // 发送完成后关闭对话框
        this.projectDialogVisible = false;
      }, 1500);
    },

    // 项目查询功能
    handleProjectQuery() {
      let filteredData = JSON.parse(JSON.stringify(this.projectOriginalTableData))

      // 根据项目编号筛选
      if (this.projectQueryForm.projectNumber && this.projectQueryForm.projectNumber.trim()) {
        filteredData = filteredData.filter(item =>
          item.projectNumber.toLowerCase().includes(this.projectQueryForm.projectNumber.trim().toLowerCase())
        )
      }

      this.projectTableData = filteredData
      this.projectTotalCount = filteredData.length
      this.projectCurrentPage = 1

      this.$message.success(`查询完成，共找到 ${filteredData.length} 条记录`)
    },

    // 项目重置功能
    handleProjectReset() {
      // 重置项目查询表单
      this.projectQueryForm.projectNumber = ''

      // 恢复项目原始数据
      this.projectTableData = JSON.parse(JSON.stringify(this.projectOriginalTableData))
      this.projectTotalCount = this.projectOriginalTableData.length
      this.projectCurrentPage = 1
    },

    // 项目分页大小变化
    handleProjectSizeChange(val) {
      this.projectPageSize = val
      this.projectCurrentPage = 1
    },

    // 项目当前页变化
    handleProjectCurrentChange(val) {
      this.projectCurrentPage = val
    },

    // 项目对话框关闭时的处理
    handleProjectDialogClose() {
      // 重置项目查询条件
      this.handleProjectReset();
      // 清空选中的项目
      this.selectedProject = null;
      // 清空任务表单
      this.taskForm = {
        workContent: '',
        duration: '',
        timeUnit: 'day'
      };
    },

    // 查看详情
    handleView(row) {
      this.viewData = row
      this.viewDialogVisible = true
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该专家吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从当前显示数据中删除
        const index = this.tableData.findIndex(item => item.id === row.id)
        if (index > -1) {
          this.tableData.splice(index, 1)
        }

        // 从原始数据中删除
        const originalIndex = this.originalTableData.findIndex(item => item.id === row.id)
        if (originalIndex > -1) {
          this.originalTableData.splice(originalIndex, 1)
        }

        this.totalCount = this.tableData.length
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 提交表单
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            // 编辑当前显示数据
            const index = this.tableData.findIndex(item => item.id === this.editId);
            if (index > -1) {
              // 保留分配状态信息（如果存在）
              const assigned = this.tableData[index].assigned || this.formData.assigned || false;
              const assignedProject = this.tableData[index].assignedProject || this.formData.assignedProject || '';
              // 保留工作内容和时间信息
              const workContent = this.tableData[index].workContent || this.formData.workContent || '';
              const duration = this.tableData[index].duration || this.formData.duration || 0;
              const timeUnit = this.tableData[index].timeUnit || this.formData.timeUnit || 'day';

              this.tableData[index] = {
                ...this.tableData[index],
                ...this.formData,
                assigned,
                assignedProject,
                workContent,
                duration,
                timeUnit
              };
            }

            // 编辑原始数据
            const originalIndex = this.originalTableData.findIndex(item => item.id === this.editId);
            if (originalIndex > -1) {
              // 保留分配状态信息（如果存在）
              const assigned = this.originalTableData[originalIndex].assigned || this.formData.assigned || false;
              const assignedProject = this.originalTableData[originalIndex].assignedProject || this.formData.assignedProject || '';
              // 保留工作内容和时间信息
              const workContent = this.originalTableData[originalIndex].workContent || this.formData.workContent || '';
              const duration = this.originalTableData[originalIndex].duration || this.formData.duration || 0;
              const timeUnit = this.originalTableData[originalIndex].timeUnit || this.formData.timeUnit || 'day';

              this.originalTableData[originalIndex] = {
                ...this.originalTableData[originalIndex],
                ...this.formData,
                assigned,
                assignedProject,
                workContent,
                duration,
                timeUnit
              };
            }

            this.$message.success('编辑成功');
          } else {
            // 新增
            const newId = Math.max(...this.originalTableData.map(item => item.id)) + 1;
            const newItem = {
              id: newId,
              ...this.formData,
              tags: this.formData.tags || [],
              assigned: this.formData.assigned || false,
              assignedProject: this.formData.assignedProject || '',
              workContent: this.formData.workContent || '',
              duration: this.formData.duration || 0,
              timeUnit: this.formData.timeUnit || 'day'
            };

            this.originalTableData.push(newItem);
            this.tableData.push(newItem);

            this.totalCount = this.tableData.length;
            this.$message.success('新增成功');
          }

          this.dialogVisible = false;
        }
      });
    },

    // 重置表单
    resetForm() {
      this.formData = {
        subjectName: '',
        subjectSource: '',
        leader: '',
        researchMethod: '',
        status: '',
        mainContent: '',
        expectedResults: '',
        tags: [],
        assigned: false,
        assignedProject: '',
        workContent: '',
        duration: 0,
        timeUnit: 'day'
      }
      if (this.$refs.dataForm) {
        this.$refs.dataForm.resetFields()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.el-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  text-align: right;
}

// 查询工具栏样式
.el-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;

  .el-row {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-form-item {
    margin-bottom: 10px;
  }
}

// 查询按钮组样式
.el-button+.el-button {
  margin-left: 10px;
}

// 展开/收起按钮样式
.el-button--text {
  padding: 0;
  margin-left: 15px;

  i {
    margin-left: 5px;
  }
}

//导入
.import-button {
  padding: 10px 16px;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.import-button i {
  margin-right: 6px;
}

// 项目查询表单样式
.project-query-form {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

// 项目分配表单样式
.task-assignment-form {
  margin-top: 20px;
  padding: 20px;
  border-top: 1px solid #ebeef5;

  h3 {
    margin-bottom: 20px;
    color: #333;
  }

  .duration-input {
    width: 150px;
    margin-right: 10px;
  }

  .unit-select {
    width: 100px;
  }
}

// 新增/编辑对话框样式
.el-dialog {
  .el-form-item {
    .el-input-number {
      width: 100%;
    }
  }
}
</style>