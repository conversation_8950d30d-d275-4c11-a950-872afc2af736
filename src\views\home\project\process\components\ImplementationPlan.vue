<template>
  <div class="implementation-plan-container">
    <el-card>
      <div slot="header">
        <span>📋 实施方案管理</span>
        <el-button 
          v-if="!hasPlan" 
          style="float: right; padding: 3px 0" 
          type="text" 
          @click="createPlan">
          创建实施方案
        </el-button>
        <el-button 
          v-else 
          style="float: right; padding: 3px 0" 
          type="text" 
          @click="editPlan">
          编辑方案
        </el-button>
      </div>
      
      <!-- 方案状态显示 -->
      <div class="plan-status" style="margin-bottom: 20px;">
        <el-alert
          :title="getStatusTitle()"
          :type="getStatusType()"
          :description="getStatusDescription()"
          show-icon
          :closable="false">
        </el-alert>
      </div>
      
      <!-- 实施方案内容显示 -->
      <div v-if="hasPlan" class="plan-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="方案概述">
            <div style="white-space: pre-wrap;">{{ planData.overview }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="实施目标">
            <div style="white-space: pre-wrap;">{{ planData.objectives }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="实施步骤">
            <div style="white-space: pre-wrap;">{{ planData.steps }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="资源配置">
            <div style="white-space: pre-wrap;">{{ planData.resources }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="时间安排">
            <div style="white-space: pre-wrap;">{{ planData.timeline }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="风险控制">
            <div style="white-space: pre-wrap;">{{ planData.riskControl }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="质量保证">
            <div style="white-space: pre-wrap;">{{ planData.qualityAssurance }}</div>
          </el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px; text-align: right;">
          <el-button v-if="planStatus === 'draft'" type="primary" @click="submitPlan">提交方案</el-button>
          <el-button type="default" @click="editPlan">编辑方案</el-button>
          <el-button type="success" @click="downloadPlan">下载方案</el-button>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state" style="text-align: center; padding: 40px;">
        <i class="el-icon-document-add" style="font-size: 64px; color: #C0C4CC;"></i>
        <p style="color: #909399; margin-top: 16px;">暂无实施方案</p>
        <el-button type="primary" @click="createPlan">创建实施方案</el-button>
      </div>
    </el-card>
    
    <!-- 编辑实施方案对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="80%"
      :close-on-click-modal="false">
      
      <el-form :model="formData" :rules="formRules" ref="planForm" label-width="120px">
        <el-form-item label="方案概述" prop="overview">
          <el-input 
            v-model="formData.overview" 
            type="textarea" 
            :rows="3"
            placeholder="请简要描述实施方案的整体概况">
          </el-input>
        </el-form-item>
        
        <el-form-item label="实施目标" prop="objectives">
          <el-input 
            v-model="formData.objectives" 
            type="textarea" 
            :rows="4"
            placeholder="请详细描述项目的实施目标和预期效果">
          </el-input>
        </el-form-item>
        
        <el-form-item label="实施步骤" prop="steps">
          <el-input 
            v-model="formData.steps" 
            type="textarea" 
            :rows="5"
            placeholder="请详细描述项目的实施步骤和流程">
          </el-input>
        </el-form-item>
        
        <el-form-item label="资源配置" prop="resources">
          <el-input 
            v-model="formData.resources" 
            type="textarea" 
            :rows="4"
            placeholder="请描述人力、物力、财力等资源的配置方案">
          </el-input>
        </el-form-item>
        
        <el-form-item label="时间安排" prop="timeline">
          <el-input 
            v-model="formData.timeline" 
            type="textarea" 
            :rows="4"
            placeholder="请制定详细的时间进度安排">
          </el-input>
        </el-form-item>
        
        <el-form-item label="风险控制" prop="riskControl">
          <el-input 
            v-model="formData.riskControl" 
            type="textarea" 
            :rows="3"
            placeholder="请分析可能的风险并制定控制措施">
          </el-input>
        </el-form-item>
        
        <el-form-item label="质量保证" prop="qualityAssurance">
          <el-input 
            v-model="formData.qualityAssurance" 
            type="textarea" 
            :rows="3"
            placeholder="请描述质量保证措施和验收标准">
          </el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePlan">保存</el-button>
        <el-button v-if="planStatus === 'draft'" type="success" @click="saveAndSubmit">保存并提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ImplementationPlan',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      hasPlan: false,
      planStatus: 'none', // none: 无方案, draft: 草稿, submitted: 已提交, approved: 已批准
      planData: {},
      formData: {
        overview: '',
        objectives: '',
        steps: '',
        resources: '',
        timeline: '',
        riskControl: '',
        qualityAssurance: ''
      },
      formRules: {
        overview: [
          { required: true, message: '请输入方案概述', trigger: 'blur' },
          { min: 10, message: '方案概述至少10个字符', trigger: 'blur' }
        ],
        objectives: [
          { required: true, message: '请输入实施目标', trigger: 'blur' },
          { min: 20, message: '实施目标至少20个字符', trigger: 'blur' }
        ],
        steps: [
          { required: true, message: '请输入实施步骤', trigger: 'blur' },
          { min: 30, message: '实施步骤至少30个字符', trigger: 'blur' }
        ],
        resources: [
          { required: true, message: '请输入资源配置', trigger: 'blur' }
        ],
        timeline: [
          { required: true, message: '请输入时间安排', trigger: 'blur' }
        ],
        riskControl: [
          { required: true, message: '请输入风险控制措施', trigger: 'blur' }
        ],
        qualityAssurance: [
          { required: true, message: '请输入质量保证措施', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loadPlanData()
  },
  methods: {
    // 加载实施方案数据
    loadPlanData() {
      // 这里应该从API获取实施方案数据
      // 暂时设置为无方案状态
      this.hasPlan = false
      this.planStatus = 'none'
      
      // 可以调用API获取数据
      // this.fetchImplementationPlan()
    },
    
    // 从API获取实施方案
    async fetchImplementationPlan() {
      try {
        // const response = await this.$api.getImplementationPlan(this.projectInfo.id)
        // this.hasPlan = response.data.hasPlan
        // this.planStatus = response.data.status
        // this.planData = response.data.planData
        
        console.log('获取实施方案数据:', this.projectInfo.id)
      } catch (error) {
        console.error('获取实施方案失败:', error)
        this.$message.error('获取实施方案失败')
      }
    },
    
    // 创建方案
    createPlan() {
      this.dialogTitle = '创建实施方案'
      this.formData = {
        overview: '',
        objectives: '',
        steps: '',
        resources: '',
        timeline: '',
        riskControl: '',
        qualityAssurance: ''
      }
      this.dialogVisible = true
    },
    
    // 编辑方案
    editPlan() {
      this.dialogTitle = '编辑实施方案'
      this.formData = { ...this.planData }
      this.dialogVisible = true
    },
    
    // 保存方案
    savePlan() {
      this.$refs.planForm.validate((valid) => {
        if (valid) {
          this.planData = { ...this.formData }
          this.hasPlan = true
          this.planStatus = 'draft'
          this.dialogVisible = false
          this.$message.success('实施方案保存成功')
        }
      })
    },
    
    // 保存并提交
    saveAndSubmit() {
      this.$refs.planForm.validate((valid) => {
        if (valid) {
          this.planData = { ...this.formData }
          this.hasPlan = true
          this.planStatus = 'submitted'
          this.dialogVisible = false
          this.$message.success('实施方案提交成功')
        }
      })
    },
    
    // 提交方案
    submitPlan() {
      this.$confirm('确认提交实施方案吗？提交后将无法修改。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.planStatus = 'submitted'
        this.$message.success('实施方案提交成功')
      })
    },
    
    // 下载方案
    downloadPlan() {
      // 这里实现下载功能
      this.$message.success('方案下载功能开发中...')
    },
    
    // 获取状态标题
    getStatusTitle() {
      const titles = {
        none: '暂无实施方案',
        draft: '实施方案草稿',
        submitted: '实施方案已提交',
        approved: '实施方案已批准'
      }
      return titles[this.planStatus] || '未知状态'
    },
    
    // 获取状态类型
    getStatusType() {
      const types = {
        none: 'info',
        draft: 'warning',
        submitted: 'success',
        approved: 'success'
      }
      return types[this.planStatus] || 'info'
    },
    
    // 获取状态描述
    getStatusDescription() {
      const descriptions = {
        none: '请创建项目实施方案',
        draft: '实施方案已保存为草稿，可以继续编辑或提交',
        submitted: '实施方案已提交，等待审核',
        approved: '实施方案已通过审核'
      }
      return descriptions[this.planStatus] || ''
    }
  }
}
</script>

<style scoped>
.implementation-plan-container {
  height: 100%;
}

.plan-status {
  margin-bottom: 20px;
}

.plan-content {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  text-align: right;
}
</style>
