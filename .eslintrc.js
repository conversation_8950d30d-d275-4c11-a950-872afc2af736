/**
 * <AUTHOR> （不想保留author可删除）
 * @description .eslintrc.js
 */

module.exports = {
  root: true,
  env: {
    node: true,
    es6: true,
  },
  extends: [
    'plugin:vue/essential',
    'eslint:recommended',
    '@vue/prettier'
  ],
  rules: {
    // 关闭 Prettier 相关规则
    'prettier/prettier': 'off',
    // 关闭 Vue 组件名称多单词规则
    'vue/multi-word-component-names': 'off',
    // 关闭其他可能冲突的格式化规则
    'indent': 'off',
    'quotes': 'off',
    'semi': 'off',
    'comma-dangle': 'off',
    'space-before-function-paren': 'off',
    'object-curly-spacing': 'off',
    'array-bracket-spacing': 'off',
    'computed-property-spacing': 'off',
    'key-spacing': 'off',
    'no-trailing-spaces': 'off',
    'eol-last': 'off',
    'no-multiple-empty-lines': 'off'
  },
  parserOptions: {
    parser: 'babel-eslint',
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  overrides: [
    {
      files: ['**/__tests__/*.{j,t}s?(x)', '**/tests/unit/**/*.spec.{j,t}s?(x)'],
      env: {
        jest: true,
      },
    },
  ],
}
