<template>
<div class="project-closure-materials">
  <!-- 结项材料列表 -->
  <el-card>
    <div slot="header" class="card-header">
      <span>📋 结项材料数据</span>
      <el-button type="primary" @click="addMaterial">新增材料</el-button>
    </div>

    <div class="materials-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="材料总数" :value="materialList.length" suffix="项"></el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="已完成" :value="completedMaterials" suffix="项"></el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="进行中" :value="inProgressMaterials" suffix="项"></el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="待提交" :value="pendingMaterials" suffix="项"></el-statistic>
        </el-col>
      </el-row>
    </div>

    <div class="table-container">
      <el-table :data="materialList" style="width: 100%" border>
        <el-table-column prop="materialType" label="结项材料清单" min-width="150">
          <template slot-scope="scope">
            <el-tag :type="getMaterialTypeTagType(scope.row.materialType)">
              {{ getMaterialTypeText(scope.row.materialType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="materialDocument" label="结项材料文件" min-width="200"></el-table-column>
        <el-table-column prop="projectLeader" label="结项材料负责人" width="120"></el-table-column>
        <el-table-column prop="projectDetails" label="经费明细表" width="120">
          <template slot-scope="scope">
            <el-tag size="small" type="warning">{{ scope.row.projectDetails }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="projectAmount" label="项目（预）立项表" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.projectAmount }}万元</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120"></el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="mini" @click="viewMaterial(scope.row)">查看</el-button>
            <el-button size="mini" @click="editMaterial(scope.row)">编辑</el-button>
            <!-- <el-button size="mini" type="success" @click="downloadMaterial(scope.row)">下载</el-button> -->
            <el-button size="mini" type="danger" @click="deleteMaterial(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-card>

  <!-- 查看材料详情对话框 -->
  <el-dialog title="结项材料详情" :visible.sync="viewDialogVisible" width="80%">
    <el-descriptions v-if="currentMaterial" :column="2" border>
      <el-descriptions-item label="材料类型">
        <el-tag :type="getMaterialTypeTagType(currentMaterial.materialType)">
          {{ getMaterialTypeText(currentMaterial.materialType) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="材料负责人">{{ currentMaterial.projectLeader }}</el-descriptions-item>
      <el-descriptions-item label="材料文件" :span="2">{{ currentMaterial.materialDocument }}</el-descriptions-item>
      <el-descriptions-item label="经费明细表">{{ currentMaterial.projectDetails }}</el-descriptions-item>
      <el-descriptions-item label="项目立项表">{{ currentMaterial.projectAmount }}万元</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="getStatusTagType(currentMaterial.status)">
          {{ getStatusText(currentMaterial.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ currentMaterial.createTime }}</el-descriptions-item>
      <el-descriptions-item label="项目实施方案数据" :span="2">
        <div style="white-space: pre-wrap;">{{ currentMaterial.implementationData }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="项目调研数据" :span="2">
        <div style="white-space: pre-wrap;">{{ currentMaterial.researchData }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="研究报告数据" :span="2">
        <div style="white-space: pre-wrap;">{{ currentMaterial.reportData }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="审核意见数据" :span="2">
        <div style="white-space: pre-wrap;">{{ currentMaterial.auditData }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="相关合同/协议" :span="2">
        <div style="white-space: pre-wrap;">{{ currentMaterial.contractData }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="附件" :span="2">
        <div v-if="currentMaterial.attachments && currentMaterial.attachments.length > 0">
          <el-tag v-for="file in currentMaterial.attachments" :key="file.name" style="margin-right: 5px;">
            {{ file.name }}
          </el-tag>
        </div>
        <span v-else>暂无附件</span>
      </el-descriptions-item>
      <el-descriptions-item v-if="currentMaterial.notes" label="备注" :span="2">
        <div style="white-space: pre-wrap;">{{ currentMaterial.notes }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <div slot="footer" class="dialog-footer">
      <el-button @click="viewDialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="editMaterial(currentMaterial)">编辑</el-button>
      <el-button type="success" @click="downloadMaterial(currentMaterial)">下载</el-button>
    </div>
  </el-dialog>

  <!-- 添加/编辑材料对话框 -->
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false">
    <el-form :model="formData" :rules="formRules" ref="materialForm" label-width="140px">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          材料基本信息
        </h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="材料类型" prop="materialType">
              <el-select v-model="formData.materialType" placeholder="请选择材料类型" style="width: 100%">
                <el-option label="结项材料清单" value="checklist"></el-option>
                <el-option label="结项材料文件" value="document"></el-option>
                <el-option label="其他材料" value="other"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="材料负责人" prop="projectLeader">
              <el-input v-model="formData.projectLeader" placeholder="请输入材料负责人" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="材料文件名称" prop="materialDocument">
              <el-input v-model="formData.materialDocument" placeholder="请输入材料文件名称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经费明细表" prop="projectDetails">
              <el-input v-model="formData.projectDetails" placeholder="请输入经费明细表信息" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目立项表" prop="projectAmount">
              <el-input v-model="formData.projectAmount" placeholder="请输入项目金额" clearable>
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 项目数据 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-data-line"></i>
          项目相关数据
        </h3>
        <el-form-item label="项目实施方案数据" prop="implementationData">
          <el-input v-model="formData.implementationData" type="textarea" :rows="3" placeholder="请输入项目实施方案数据" clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="项目调研数据" prop="researchData">
          <el-input v-model="formData.researchData" type="textarea" :rows="3" placeholder="请输入项目调研数据" clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="研究报告数据" prop="reportData">
          <el-input v-model="formData.reportData" type="textarea" :rows="3" placeholder="请输入研究报告数据" clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="审核意见数据" prop="auditData">
          <el-input v-model="formData.auditData" type="textarea" :rows="3" placeholder="请输入审核意见数据" clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="相关合同/协议" prop="contractData">
          <el-input v-model="formData.contractData" type="textarea" :rows="3" placeholder="请输入相关合同/协议信息" clearable>
          </el-input>
        </el-form-item>
      </div>

      <!-- 附件上传 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-folder-opened"></i>
          附件上传
        </h3>
        <el-form-item label="结项材料清单内容上传" prop="attachments">
          <el-upload class="upload-demo" :action="uploadUrl" :on-success="handleAttachmentSuccess"
            :on-remove="handleAttachmentRemove" :file-list="attachmentFileList" :limit="10"
            accept=".pdf,.doc,.docx,.xls,.xlsx">
            <el-button size="small" type="primary" icon="el-icon-upload">上传结项材料</el-button>
            <div slot="tip" class="el-upload__tip">支持 PDF、Word、Excel 格式，最多10个文件</div>
          </el-upload>
        </el-form-item>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-more"></i>
          其他信息
        </h3>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" type="textarea" :rows="3" placeholder="请输入其他备注信息" clearable>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveMaterial">保存</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
export default {
  name: 'ProjectClosureMaterials',
  props: {
    projectInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      viewDialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      editId: null,
      currentMaterial: null,
      materialList: [],
      formData: {
        materialType: '',           // 材料类型
        projectLeader: '',          // 材料负责人
        materialDocument: '',       // 材料文件名称
        projectDetails: '',         // 经费明细表
        projectAmount: '',          // 项目立项表金额
        implementationData: '',     // 项目实施方案数据
        researchData: '',           // 项目调研数据
        reportData: '',             // 研究报告数据
        auditData: '',              // 审核意见数据
        contractData: '',           // 相关合同/协议
        attachments: [],            // 附件
        notes: ''                   // 备注
      },

      // 文件上传相关
      uploadUrl: '/api/upload',
      attachmentFileList: [],

      formRules: {
        materialType: [
          { required: true, message: '请选择材料类型', trigger: 'change' }
        ],
        projectLeader: [
          { required: true, message: '请输入材料负责人', trigger: 'blur' }
        ],
        materialDocument: [
          { required: true, message: '请输入材料文件名称', trigger: 'blur' }
        ],
        projectDetails: [
          { required: true, message: '请输入经费明细表信息', trigger: 'blur' }
        ],
        projectAmount: [
          { required: true, message: '请输入项目金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    completedMaterials() {
      return this.materialList.filter(material => material.status === 'completed').length
    },
    inProgressMaterials() {
      return this.materialList.filter(material => material.status === 'in-progress').length
    },
    pendingMaterials() {
      return this.materialList.filter(material => material.status === 'pending').length
    }
  },
  mounted() {
    this.loadMaterialList()
  },
  methods: {
    // 加载材料列表
    loadMaterialList() {
      // 模拟数据
      this.materialList = [
        {
          id: 1,
          materialType: 'checklist',
          projectLeader: '田野',
          materialDocument: '盆地低渗透油气田含油污水回用处理技术结项材料',
          projectDetails: '经费明细表-2024',
          projectAmount: '50.0',
          implementationData: '项目实施方案包括技术路线、实施步骤、质量控制等内容...',
          researchData: '项目调研数据包括市场调研、技术调研、用户需求调研等...',
          reportData: '研究报告数据包括技术报告、测试报告、验收报告等...',
          auditData: '审核意见数据包括专家评审意见、技术审核意见等...',
          contractData: '相关合同包括技术开发合同、设备采购合同等...',
          status: 'completed',
          createTime: '2024-01-15',
          attachments: [
            { name: '结项材料清单.pdf', url: '/files/closure1.pdf' }
          ]
        }
      ]
    },

    // 新增材料
    addMaterial() {
      this.dialogTitle = '新增结项材料'
      this.isEdit = false
      this.editId = null
      this.formData = {
        materialType: '',
        projectLeader: '',
        materialDocument: '',
        projectDetails: '',
        projectAmount: '',
        implementationData: '',
        researchData: '',
        reportData: '',
        auditData: '',
        contractData: '',
        attachments: [],
        notes: ''
      }
      this.attachmentFileList = []
      this.dialogVisible = true
    },

    // 编辑材料
    editMaterial(material) {
      this.dialogTitle = '编辑结项材料'
      this.isEdit = true
      this.editId = material.id
      this.formData = { ...material }
      this.attachmentFileList = material.attachments || []
      this.dialogVisible = true
      this.viewDialogVisible = false
    },

    // 查看材料
    viewMaterial(material) {
      this.currentMaterial = material
      this.viewDialogVisible = true
    },

    // 保存材料
    saveMaterial() {
      this.$refs.materialForm.validate((valid) => {
        if (valid) {
          const materialData = {
            ...this.formData,
            status: 'draft',
            createTime: new Date().toISOString().split('T')[0]
          }

          if (this.isEdit) {
            // 编辑现有材料
            const index = this.materialList.findIndex(item => item.id === this.editId)
            if (index > -1) {
              this.materialList.splice(index, 1, { ...materialData, id: this.editId })
            }
            this.$message.success('材料更新成功')
          } else {
            // 新增材料
            materialData.id = Date.now()
            this.materialList.push(materialData)
            this.$message.success('材料创建成功')
          }

          this.dialogVisible = false
        }
      })
    },

    // 下载材料
    downloadMaterial(material) {
      this.$message.success(`正在下载材料：${material.materialDocument}`)
    },

    // 删除材料
    deleteMaterial(material) {
      this.$confirm(`确定要删除材料"${material.materialDocument}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.materialList.findIndex(item => item.id === material.id)
        if (index > -1) {
          this.materialList.splice(index, 1)
          this.$message.success('删除成功')
        }
      })
    },

    // 附件上传成功
    handleAttachmentSuccess(response, file, fileList) {
      this.attachmentFileList = fileList
      this.formData.attachments = fileList.map(f => ({
        name: f.name,
        url: f.response?.url || f.url
      }))
      this.$message.success('附件上传成功')
    },

    // 附件移除
    handleAttachmentRemove(file, fileList) {
      this.attachmentFileList = fileList
      this.formData.attachments = fileList.map(f => ({
        name: f.name,
        url: f.response?.url || f.url
      }))
    },

    // 获取材料类型标签类型
    getMaterialTypeTagType(type) {
      const typeMap = {
        'checklist': 'primary',
        'document': 'success',
        'other': 'info'
      }
      return typeMap[type] || 'info'
    },

    // 获取材料类型文本
    getMaterialTypeText(type) {
      const textMap = {
        'checklist': '结项材料清单',
        'document': '结项材料文件',
        'other': '其他材料'
      }
      return textMap[type] || '未知'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'draft': 'info',
        'in-progress': 'warning',
        'pending': 'primary',
        'completed': 'success'
      }
      return typeMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'draft': '草稿',
        'in-progress': '进行中',
        'pending': '待提交',
        'completed': '已完成'
      }
      return textMap[status] || '未知'
    }
  }
}
</script>

<style scoped lang="scss">
.project-closure-materials {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .materials-stats {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .table-container {
    margin-top: 20px;
  }

  .dialog-footer {
    text-align: right;
  }

  /* 表单区域样式 */
  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fafbfc;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    border-bottom: 2px solid #e1e8ed;
    padding-bottom: 10px;
  }

  .section-title i {
    margin-right: 8px;
    font-size: 18px;
    color: #409eff;
  }

  /* 上传组件样式 */
  .upload-demo {
    width: 100%;
  }

  .upload-demo .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 5px;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .form-section {
      padding: 15px;
      margin-bottom: 20px;
    }

    .materials-stats {
      padding: 15px;
    }
  }
}
</style>
