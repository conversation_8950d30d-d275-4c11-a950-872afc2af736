/**
 * <AUTHOR> （不想保留author可删除）
 * @description 全局样式
 */

@charset "utf-8";

@import './normalize.scss';
@import './transition.scss';
@import './loading.scss';

$base: '.vab';

@mixin scrollbar {
  max-height: 88vh;
  margin-bottom: 0.5vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, 0.3);
  }
}

@mixin base-scrollbar {
  &::-webkit-scrollbar {
    width: 13px;
    height: 13px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4);
    background-clip: padding-box;
    border: 3px solid transparent;
    border-radius: 7px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-track:hover {
    background-color: #f8fafc;
  }
}

img {
  object-fit: cover;
}

a {
  color: $base-color-blue;
  text-decoration: none;
  cursor: pointer;
}

* {
  transition: $base-transition;
}

svg {
  transition: none;

  * {
    transition: none;
  }
}

html {
  body {
    position: relative;
    height: 100vh;
    padding: 0;
    margin: 0;
    font-family: Avenir, Helvetica, Arial, sans-serif;
    font-size: $base-font-size-default;
    color: #2c3e50;
    background: #f6f8f9;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    @include base-scrollbar;

    div {
      @include base-scrollbar;
    }

    svg,
    i {
      &:hover {
        opacity: 0.8;
      }
    }

    .v-modal {
      backdrop-filter: blur(10px);
    }

    .el-tag + .el-tag {
      margin-left: 10px;
    }

    .editor-toolbar {
      .no-mobile,
      .fa-question-circle {
        display: none;
      }
    }

    .el-divider--horizontal {
      margin: 10px 0 25px 0;

      .el-divider__text {
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }

    .el-image-viewer {
      &__close {
        .el-icon-circle-close {
          color: $base-color-white;
        }
      }
    }

    .vue-admin-better-wrapper {
      .app-main-container {
        @include base-scrollbar;

        > [class*='-container'] {
          * {
            transition: none;
          }

          padding: $base-padding;
          background: $base-color-white;
        }
      }
    }

    /* 进度条开始 */
    #nprogress {
      position: fixed;
      z-index: $base-z-index;

      .bar {
        background: $base-color-blue !important;
      }

      .peg {
        box-shadow: 0 0 10px $base-color-blue, 0 0 5px $base-color-blue !important;
      }
    }

    .el-table {
      .el-table__body-wrapper {
        @include base-scrollbar;
      }

      th {
        background: #f5f7fa;
      }

      td,
      th {
        position: relative;
        box-sizing: border-box;
        padding: 7.5px 0;

        .cell {
          font-size: $base-font-size-default;
          font-weight: normal;
          color: #606266;

          .el-image {
            width: 50px;
            height: 50px;
            border-radius: $base-border-radius;
          }
        }
      }
    }

    .el-pagination {
      padding: 2px 5px;
      margin: 15px 0 0 0;
      font-weight: normal;
      color: $base-color-black;
      text-align: center;
    }

    .el-menu.el-menu--popup.el-menu--popup-right-start {
      @include scrollbar;
    }

    .el-menu.el-menu--popup.el-menu--popup-bottom-start {
      @include scrollbar;
    }

    .el-submenu__title i {
      color: $base-color-white;
    }

    .el-dialog,
    .el-message-box {
      &__body {
        border-top: 1px solid $base-border-color;

        .el-form {
          padding-right: 30px;
        }
      }

      &__footer {
        padding: $base-padding;
        text-align: right;
        border-top: 1px solid $base-border-color;
      }

      &__content {
        padding: 20px 20px 20px 20px;
      }
    }

    .el-card {
      margin-bottom: 15px;
      border-radius: 12px;
      border: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

      &__body {
        padding: $base-padding;
      }
    }

    /* VabPageHeader 全局样式 - 高优先级 */
    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      border-radius: 12px !important;
      padding: 30px !important;
      margin-bottom: 24px !important;
      color: white !important;
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3) !important;
      border: none !important;

      .header-content {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;

        .header-left {
          .page-title {
            font-size: 2rem !important;
            font-weight: 700 !important;
            margin: 0 0 8px 0 !important;
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
            color: white !important;

            .vab-icon {
              font-size: 1.8rem !important;
              color: white !important;
            }
          }

          .page-description {
            font-size: 1rem !important;
            opacity: 0.9 !important;
            margin: 0 !important;
            color: white !important;
          }
        }

        .header-right {
          display: flex !important;
          align-items: center !important;
          gap: 8px !important;
          font-size: 1.1rem !important;
          font-weight: 600 !important;
          color: white !important;

          .vab-icon {
            font-size: 1.3rem !important;
            color: white !important;
          }
        }
      }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .page-header {
        padding: 20px !important;

        .header-content {
          flex-direction: column !important;
          gap: 16px !important;
          text-align: center !important;

          .header-left {
            .page-title {
              font-size: 1.5rem !important;
            }
          }
        }
      }
    }

    .select-tree-popper {
      .el-scrollbar {
        .el-scrollbar__view {
          .el-select-dropdown__item {
            height: auto;
            max-height: 274px;
            padding: 0;
            overflow-y: auto;
            line-height: 26px;
          }
        }
      }
    }

    /* 扩展 el-divider 样式 */
    .el-divider {
      position: relative;
      border: none;
      background: linear-gradient(90deg, transparent, #e4e7ed, transparent);

      &--horizontal {
        height: 1px;
        margin: 20px 0 30px 0;

        .el-divider__text {
          position: relative;
          padding: 0 16px;
          font-size: 14px;
          font-weight: 500;
          color: #606266;
          background: #fff;
          border-radius: 4px;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -8px;
            width: 4px;
            height: 4px;
            margin-top: -2px;
            background: #409eff;
            border-radius: 50%;
          }
        }
      }

      &--vertical {
        width: 1px;
        height: 1em;
        margin: 0 16px;
        background: #e4e7ed;
      }

      /* 主题变体 */
      &--primary {
        background: linear-gradient(90deg, transparent, #409eff, transparent);

        .el-divider__text {
          color: #409eff;

          &::before {
            background: #409eff;
          }
        }
      }

      &--success {
        background: linear-gradient(90deg, transparent, #67c23a, transparent);

        .el-divider__text {
          color: #67c23a;

          &::before {
            background: #67c23a;
          }
        }
      }

      &--warning {
        background: linear-gradient(90deg, transparent, #e6a23c, transparent);

        .el-divider__text {
          color: #e6a23c;

          &::before {
            background: #e6a23c;
          }
        }
      }

      &--danger {
        background: linear-gradient(90deg, transparent, #f56c6c, transparent);

        .el-divider__text {
          color: #f56c6c;

          &::before {
            background: #f56c6c;
          }
        }
      }

      /* 虚线样式 */
      &--dashed {
        background: none;
        border-top: 1px dashed #e4e7ed;

        .el-divider__text {
          background: #fff;
        }
      }

      /* 粗线样式 */
      &--thick {
        height: 2px;
        background: linear-gradient(90deg, transparent, #409eff, transparent);
      }
    }
  }

  .side-container {
    .el-menu-item,
    .el-submenu {
      margin: 7px !important;
      border-radius: 5px !important;

      &:hover {
        border-radius: 5px !important;
      }

      &.is-active {
        background: $base-color-default !important;
      }
    }
  }
}
