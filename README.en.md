[简体中文](./README.md) | English

<div align="center"><img width="200" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/logo/vab.png"/>
<h1> vue-admin-better（element-ui） </h1>
<p>The flying snow all over the sky is a flying note, playing out expectations with blessings. May the epidemic dissipate as soon as possible, may you no longer have regrets next year, may you be warm in winter, may you not be cold in spring, and may you have lights in the dark and an umbrella in the rain.
</p>
</div>

[![stars](https://img.shields.io/github/stars/zxwk1998/vue-admin-better?style=flat-square&logo=GitHub)](https://github.com/zxwk1998/vue-admin-better)
[![star](https://gitee.com/chu1204505056/vue-admin-better/badge/star.svg?theme=gray)](https://gitee.com/chu1204505056/vue-admin-better)
[![license](https://img.shields.io/github/license/zxwk1998/vue-admin-better?style=flat-square)](https://en.wikipedia.org/wiki/MIT_License)

---

# 🎉 Characteristic

- 💪 40 + high quality single page
- 💅 RBAC model + JWT permission control
- 🌍 100000 + practical application of the project
- 👏 Good type definition
- 🥳 The open source version supports free commercial use
- 🚀 Cross platform PC, mobile terminal and tablet
- 📦 Back end route dynamic rendering

## 🌐 Address

- [🎉 Vue2. X + element UI (free commercial, PC, tablet and mobile phone supported)](https://vuejs-core.cn/vue-admin-better/)

<!-- - [⚡️ Vue3. X + element plus (alpha version, free commercial, supporting PC, tablet and mobile phone)](https://vuejs-core.cn/vue-admin-better-plus/) -->

<!-- - [⚡️ Vue3. X + ant design Vue (beta version, free commercial, supporting PC, tablet and mobile phone)](https://vuejs-core.cn/vue-admin-better-antdv/) -->

- [⚡️ vue3.x + vite + vue-admin-arco](https://vuejs-core.cn/vue-admin-arco/)

- [🚀 Admin Pro demo address (vue2.x paid version, supporting PC, tablet and mobile phone)](https://vuejs-core.cn/admin-pro/)

- [🚀 Admin plus demo address (vue3.x paid version, supporting PC, tablet and mobile phone)](https://vuejs-core.cn/admin-plus/)

- [📌 Pro and plus purchase address authorization](https://vuejs-core.cn/authorization/)

- [🌐 Github Warehouse address](https://github.com/zxwk1998/vue-admin-better/)

- [🌐 Gitee Warehouse address](https://gitee.com/chu1204505056/vue-admin-better/)

- Recently, the VAB official website has been frequently attacked by DDoS. We have taken relevant preventive measures.
  If the website cannot be accessed, please visit the backup address

## 📦️ Desktop applications

- [Admin Pro](https://gitee.com/chu1204505056/microsoft-store/raw/master/AdminPlus.zip)
- [Admin Plus](https://gitee.com/chu1204505056/microsoft-store/raw/master/AdminPlus.zip)

<!-- ## 🌱 Vue3.x vue3.0-antdv [Click switch branch](https://github.com/zxwk1998/vue-admin-better/tree/vue3.0-antdv)

```bash
git clone -b vue3.0-antdv https://github.com/zxwk1998/vue-admin-better.git
npm i --registry=http://mirrors.cloud.tencent.com/npm/
npm run serve
``` -->

## 🌱 Vue2.xmain [Click switch branch](https://github.com/zxwk1998/vue-admin-better/tree/master)

```bash
git clone -b master https://github.com/zxwk1998/vue-admin-better.git
npm i --registry=http://mirrors.cloud.tencent.com/npm/
npm run serve
```

## 🍻 Front end discussion QQ group

- Let's have a cup of coffee, and then contact QQ 783963206 to invite you to the discussion group. Because of the large
  number of users, if you haven't passed a friend's request, please contact Alipay on the Alipay payment page, whether
  you invite or not, you can enjoy the open source code, thank you for your support and trust, and provide the
  vue-admin-better basic version in the group. Automatic configuration tutorial of development tools and project
  development documents.

<table>
<tr>
<!-- <td>
<img width="200px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/qq_group/hbm.jpg">
</td> -->
<td>
<img width="200px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/zfb_kf.jpg">
</td>
<td>
<img width="200px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/qq_group/vab-2.jpg">
</td>
<td>
<img width="200px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/qq_group/vab-3.jpg">
</td>
</tr>
</table>

## 🙈 We promise to sponsor open source projects regularly (thank giant)

<a title="vue" href="https://opencollective.com/vuejs" target="_blank">
<img width="64px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/vue.png"/>
</a>
<a title="element-plus" href="https://opencollective.com/element-plus" target="_blank">
<img width="64px" src="https://gcore.jsdelivr.net/gh/zxwk1998/image/element-plus.png"/>
</a>
<a title="ant-design-vue" href="https://opencollective.com/ant-design-vue" target="_blank">
<img width="64px" src="https://images.opencollective.com/ant-design-vue/2ec179b/logo/256.png"/>
</a>

## 🎨 Acknowledge

| Project                                                          |
| ---------------------------------------------------------------- |
| [vue](https://github.com/vuejs/vue)                              |
| [element-ui](https://github.com/ElemeFE/element)                 |
| [element-plus](https://github.com/element-plus/element-plus)     |
| [ant-design-vue](https://github.com/vueComponent/ant-design-vue) |
| [mock](https://github.com/nuysoft/Mock)                          |
| [axios](https://github.com/axios/axios)                          |

## 👷 Outstanding contributors to the framework (in no order)

<a href="https://github.com/buuing" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/36689704?s=50"/>
</a>
<a href="https://github.com/hipi" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/22478003?s=50"/>
</a>
<a href="https://github.com/fwfmiao" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/29328241?s=50"/>
</a>
<a href="https://github.com/hdtopku" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/********?s=50"/>
</a>
<a href="https://github.com/shaonialife" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/********?s=50"/>
</a>

## 📌 Advantages and precautions

```
Compared with other open source admin frameworks, it has the following advantages:
1. Support the front-end control routing permission intelligence and the back-end control routing permission all mode
2. It is known that the open source Vue admin framework is the first to support the automatic generation and export function of mock
3. More than 50 global fine configurations are provided
4. Support SCSS automatic sorting and eslint automatic repair
5. Axios fine encapsulation supports multiple data sources, multiple successful code arrays, and application / JSON; charset=UTF-8、application/x-www-form-urlencoded; Charset = UTF-8 multiple parameter transfer modes
6. Support login RSA encryption
7. Support packaging to automatically generate 7z compressed packages
8. Support errorlog error interception
9. Support multi theme and multi layout switching
Precautions for use:
1. The project uses lf line feed instead of CRLF line feed by default. When creating a new file, please pay attention to selecting the file line feed
2. The project uses the strictest eslint verification specification (plugin: Vue / recommended) by default. Before using it, it is recommended to configure the development tool to realize automatic repair (vscode development is recommended)
3. The project uses the MIT open source agreement with the broadest requirements, and the MIT open source agreement can be used for free

```

## 💚 Suitable for people

- I am developing and want to use element UI / element plus, with 1 year of front-end development experience +.
- Familiar with vue.js technology stack and developed several practical projects with it.
- Students who are interested in principle and technology and want to improve.

## 🎉 Function map

![img](https://gcore.jsdelivr.net/gh/zxwk1998/image/vip/flow.drawio.png)

## 🗃️ design sketch

The following is a screenshot of the pro version:

<table>
<tr>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/2.png">
</td>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/6.png">
</td>
</tr>
<tr>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/8.png">
</td>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/9.png">
</td>
</tr>
<tr>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/3.png">
</td>
<td>
<img src="https://gcore.jsdelivr.net/gh/zxwk1998/image/5.png">
</td>
</tr>
</table>

## 📄 Commercial considerations

This project can be used for commercial purposes free of charge. Please abide by the MIT agreement and keep the author's
technical support statement. For customized source code copyright information, please contact customer service QQ 783963206.

<!-- ## 严正声明

近期发现不少游手好闲之人有组织有预谋的利用码云、知乎、掘金等网站可用国外非法网站提供的匿名手机号注册的账号 bug 冒充 vab 去攻击 vue-element-admin，iview-admin，若依，d2-admin，ant-design-vue 的行为，恶意制造对立，试图让其他开源作者卷入其中，对各位开源作者造成的影响我们深表歉意，我们欢迎 vab 的用户去体验其他更优秀的框架，vue-admin-beautiful 走到今天实属不易，被人冒充，被人发帖诋毁，被人故意发布错误言论假装发帖表扬实则为我们招骂，无意动任何人的奶酪，从 2020 年至今坚持全职维护已过一年时间，说实在的我们靠技术生存并不丢人吧，一年来感谢 vab 的用户对我们不离不弃，也希望大家越来越好，加油！ -->
