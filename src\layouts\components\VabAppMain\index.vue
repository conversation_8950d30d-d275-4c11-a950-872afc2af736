<template>
<div v-if="routerView" class="app-main-container">
  <!-- <vab-github-corner /> -->
  <el-col :span="3" style="height: calc(100vh - 200px);">
    <el-menu :default-active="activeMenu" class="el-menu-vertical-demo" @select="handleMenuSelect"
      background-color="#ffffff" text-color="#606266" active-text-color="#0b5be2">
      <template v-for="(child, index) in menuRouter">
        <!-- 如果有children，显示为子菜单 -->
        <el-submenu v-if="child.children && getVisibleChildren(child.children).length > 0" :key="child.path"
          :index="getChildPath(currentTab, child.path)">
          <template slot="title">
            <span>{{ child.meta.title }}</span>
          </template>
          <el-menu-item v-for="subChild in getVisibleChildren(child.children)" :key="subChild.path"
            :index="'/' + child.path + '/' + subChild.path">
            <span>{{ subChild.meta.title }}</span>
          </el-menu-item>
        </el-submenu>
        <!-- 如果没有children，显示为普通菜单项 -->
        <el-menu-item v-else :key="child.path" :index="getChildPath(currentTab, child.path)">
          <span>{{ child.meta.title }}</span>
        </el-menu-item>
      </template>
    </el-menu>
  </el-col>
  <el-col :span="21" class="vab-tab-content">
    <div class="dashboard" style="width: 100%;margin-top: -10px;margin-bottom: 10px;">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index"
          :to="index === breadcrumbList.length - 1 ? null : { path: item.path }">
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div>
      <transition mode="out-in" name="fade-transform">
        <keep-alive :include="cachedRoutes" :max="keepAliveMaxNum">
          <router-view :key="key" class="app-main-height" />
        </keep-alive>
      </transition>
    </div>
  </el-col>
</div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { copyright, footerCopyright, keepAliveMaxNum, title } from '@/config'

export default {
  name: 'VabAppMain',
  data() {
    return {
      show: false,
      fullYear: new Date().getFullYear(),
      copyright,
      title,
      keepAliveMaxNum,
      routerView: true,
      footerCopyright,
      menuRouter: [],
    }
  },
  computed: {
    ...mapGetters({
      visitedRoutes: 'tabsBar/visitedRoutes',
      device: 'settings/device',
      routes: 'routes/routes',
      currentTab: 'routes/currentTab',
      currentTabChildren: 'routes/currentTabChildren',
    }),
    cachedRoutes() {
      const cachedRoutesArr = []
      this.visitedRoutes.forEach((item) => {
        if (!item.meta.noKeepAlive) {
          cachedRoutesArr.push(item.name)
        }
      })
      return cachedRoutesArr
    },
    key() {
      return this.$route.path
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // 如果设置了activeMenu，则使用activeMenu
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    // 面包屑导航列表
    breadcrumbList() {
      const route = this.$route
      const pathArray = route.path.split('/').filter(item => item)
      const breadcrumbs = []

      // 如果当前路径是根路径，不显示面包屑
      if (pathArray.length === 0 || (pathArray.length === 1 && pathArray[0] === 'index')) {
        return []
      }

      // 根据路径构建面包屑
      let currentPath = ''
      pathArray.forEach((path, index) => {
        currentPath += '/' + path

        // 查找对应的路由配置
        const routeConfig = this.findRouteByPath(currentPath)
        if (routeConfig && routeConfig.meta && routeConfig.meta.title) {
          breadcrumbs.push({
            title: routeConfig.meta.title,
            path: currentPath
          })
        }
      })

      return breadcrumbs
    },
  },
  watch: {
    $route: {
      handler(route) {
        if ('mobile' === this.device) this.foldSideBar()
      },
      immediate: true,
    },
    currentTabChildren: {
      handler(newChildren) {
        this.menuRouter = newChildren
      },
      immediate: true,
    },
    '$route.path': {
      handler(newPath) {
      },
      immediate: true,
    },
  },
  created() {
    // 初始化时设置默认tab
    const parentRoutes = this.routes.filter((item) => item.meta)
    if (parentRoutes.length > 0 && !this.currentTab) {
      this.$store.dispatch('routes/setCurrentTab', parentRoutes[0].path)
    }
    this.menuRouter = this.currentTabChildren

    const handleReloadRouterView = () => {
      this.routerView = false
      this.$nextTick(() => {
        this.routerView = true
      })
    }

    //重载所有路由
    this.$baseEventBus.$on('reload-router-view', handleReloadRouterView)

    this.$once('hook:beforeDestroy', () => {
      this.$baseEventBus.$off('reload-router-view', handleReloadRouterView)
    })
  },
  mounted() { },
  methods: {
    ...mapActions({
      foldSideBar: 'settings/foldSideBar',
    }),
    handleMenuSelect(index) {
      // 处理菜单选择，导航到对应路由
      if (index && index !== this.$route.path) {
        this.$router.push(index)
      }
    },
    getChildPath(parentPath, childPath) {
      // 如果子路径是绝对路径，直接返回
      if (childPath.startsWith('/')) {
        return childPath
      }
      // 如果父路径是根路径，直接拼接
      if (parentPath === '/') {
        return '/' + childPath
      }
      // 否则拼接父路径和子路径
      return parentPath + '/' + childPath
    },
    // 根据路径查找路由配置
    findRouteByPath(targetPath) {
      const findRoute = (routes, basePath = '') => {
        for (const route of routes) {
          const fullPath = basePath + '/' + route.path
          const normalizedPath = fullPath.replace(/\/+/g, '/') // 去除多余的斜杠

          if (normalizedPath === targetPath) {
            return route
          }

          if (route.children && route.children.length > 0) {
            const found = findRoute(route.children, normalizedPath)
            if (found) return found
          }
        }
        return null
      }

      return findRoute(this.routes)
    },

    // 获取可见的子路由（过滤掉隐藏的路由）
    getVisibleChildren(children) {
      if (!children || !Array.isArray(children)) {
        return []
      }

      return children.filter(child => {
        // 过滤掉设置了 hidden: true 的路由
        return !child.meta || !child.meta.hidden
      })
    },
  },
}
</script>

<style lang="scss" scoped>
/* 设置展开图标颜色为黑色 */
::v-deep .el-submenu__icon-arrow {
  color: #000000 !important;
}

/* 鼠标悬停时也保持黑色 */
::v-deep .el-submenu:hover .el-submenu__icon-arrow {
  color: #000000 !important;
}

/* 展开状态时也保持黑色 */
::v-deep .el-submenu.is-opened .el-submenu__icon-arrow {
  color: #000000 !important;
}

.app-main-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  display: flex;

  .vab-tab-content {
    padding: 20px;
    background-color: #f5f7fa;
    // min-height: 100vh;

    // 确保padding生效，覆盖el-row的负margin
    ::v-deep .el-row {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    // 为内容区域添加额外的内边距
    ::v-deep .index-container {
      padding: 0;
    }
  }

  .vab-keel {
    margin: $base-padding;
  }

  .app-main-height {
    min-height: $base-app-main-height;
  }

  .footer-copyright {
    min-height: 55px;
    line-height: 55px;
    color: rgba(0, 0, 0, 0.45);
    text-align: center;
    border-top: 1px dashed $base-border-color;
  }

  // 自定义菜单激活状态
  .el-menu-vertical-demo {
    ::v-deep .el-menu-item {
      transition: all 0.3s ease;

      &:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }

      &.is-active {
        background: linear-gradient(135deg, #fff 0%, #cfdff9 100%) !important;
        color: #0b5be2 !important;
        border-right: 5px solid #0b5be2 !important;
        font-weight: 600;

        i {
          color: #0b5be2 !important;
        }
      }
    }
  }

  // 强制覆盖栅格布局的固定宽度
  .el-col-3 {
    width: auto !important;
    flex: 0 0 auto !important;
    max-width: none !important;
    min-width: 200px;
    max-width: 400px;
  }

  .el-menu-vertical-demo {
    // height: calc(100vh - 35px);
    width: auto !important;
    min-width: 200px;

    // 子菜单弹出层自适应宽度
    ::v-deep .el-menu--popup {
      min-width: auto !important;
      width: auto !important;
    }
  }
}
</style>
