<template>
<div class="top-container">
  <div class="vab-main">
    <el-row>
      <el-col :lg="7" :md="7" :sm="7" :xl="7" :xs="7">
        <vab-logo />
      </el-col>
      <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
        <el-tabs v-model="activeName" @tab-click="handleTabClick" class="custom-tabs">
          <el-tab-pane v-for="route in parentRouter" :key="route.path" :label="route.meta.title" :name="route.path">
          </el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :lg="5" :md="5" :sm="5" :xl="5" :xs="5">
        <div class="right-panel">
          <vab-error-log />
          <div class="right-menu">
            <vab-full-screen @refresh="refreshRoute" />
            <vab-theme class="hidden-md-and-down" />
          </div>
          <vab-icon :icon="['fas', 'redo']" :pulse="pulse" title="重载路由" @click="refreshRoute" />
          <vab-avatar />
        </div>
      </el-col>
    </el-row>
  </div>
</div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'VabTop',
  data() {
    return {
      parentRouter: [],
      activeName: '',
      pulse: false,
    }
  },
  computed: {
    ...mapGetters({
      routes: 'routes/routes',
    }),
  },
  created() {
    this.parentRouter = this.routes.filter((item) => item.meta)

    // 设置默认激活的tab
    if (this.parentRouter.length > 0) {
      this.activeName = this.parentRouter[0].path
    }
  },
  methods: {
    async refreshRoute() {
      this.$baseEventBus.$emit('reload-router-view')
      this.pulse = true
      this.timeOutID = setTimeout(() => {
        this.pulse = false
      }, 1000)
    },
    handleTabClick(tab) {
      // 处理tab点击，导航到对应路由
      // 将tab.name存入到vuex中
      this.$store.dispatch('routes/setCurrentTab', tab.name)
      const selectedRoute = this.parentRouter.find((route) => route.path === tab.name)
      if (selectedRoute && selectedRoute.children && selectedRoute.children.length > 0) {
        // 导航到第一个子路由
        const firstChild = selectedRoute.children[0]
        const childPath = selectedRoute.path === '/' ? `/${firstChild.path}` : `${selectedRoute.path}/${firstChild.path}`
        this.$router.push(childPath)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.top-container {
  height: $base-top-bar-height;
  background: $base-menu-background;

  .vab-main {
    background: $base-menu-background;

    // 自定义tabs样式
    .custom-tabs {
      height: 100%;

      ::v-deep .el-tabs__header {
        margin: 0;
        border-bottom: none;
      }
    }

    ::v-deep {
      .el-tabs {
        .el-tabs__header {
          margin: 0;
        }

        .el-tabs__nav {
          border: 0;
          background: transparent;
        }

        .el-tabs__active-bar {
          display: none; // 隐藏默认的激活条
        }

        .el-tabs__item {
          height: $base-top-bar-height;
          line-height: $base-top-bar-height;
          padding: 0 15px;
          color: white;
          font-size: 1rem;
          transition: all 0.3s ease;
          border-radius: 8px 8px 0 0;
          margin-right: 4px;

          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #ffffff;
          }

          &.is-active {
            background-color: #0f4bba;

            color: #ffffff !important;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
            border-bottom: 3px solid #ffffff;
          }
        }

        .el-tabs__content {
          display: none;
        }
      }
    }
  }

  .right-panel {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: $base-top-bar-height;

    ::v-deep {

      .username,
      .user-role {
        color: rgba($base-color-white, 0.9);
      }

      .username+i {
        color: rgba($base-color-white, 0.9);
      }

      svg {
        width: 1em;
        height: 1em;
        margin-right: 15px;
        font-size: $base-font-size-big;
        color: rgba($base-color-white, 0.9);
        cursor: pointer;
        fill: rgba($base-color-white, 0.9);
      }

      button {
        svg {
          margin-right: 0;
          color: rgba($base-color-white, 0.9);
          cursor: pointer;
          fill: rgba($base-color-white, 0.9);
        }
      }

      .el-badge {
        margin-right: 15px;
      }
    }
  }
}
</style>
