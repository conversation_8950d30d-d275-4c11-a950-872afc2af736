<template>
<div class="index-container">
  <el-row :gutter="20">
    <div style="
      margin-left: 20px;
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 10px;
      border-bottom: 1px solid #ccc;
      background-color: #abeef6;
      padding: 5px;
      border-radius: 5px;
      border-left: 5px solid #88C5FF;
    ">
      课题管理
    </div>
    <el-row :gutter="20">
      <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
        <el-col :lg="6" :md="24" :sm="24" style="margin-bottom: 10px" :xl="24" :xs="24">
          <div style="display: flex; align-items: center; gap: 10px">
            <span style="font-size: 16px">统计年份</span>
            <el-select v-model="yearSelect" style="flex: 1">
              <el-option label="2020" value="2020" />
              <el-option label="2021" value="2021" />
              <el-option label="2022" value="2022" />
              <el-option label="2023" value="2023" />
            </el-select>
          </div>
        </el-col>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col v-for="(item, index) in iconList" :key="index" :lg="4" :md="6" :sm="6" :xl="3" :xs="12">
        <el-card class="icon-card" shadow="never" :style="{ background: linear(item.color) }">
          <div class="icon-container">
            <div class="icon-header">
              <div class="icon-wrapper" :style="{ background: item.color }">
                <vab-icon :icon="['fas', item.icon]" />
              </div>
            </div>
            <div class="count-content">
              <div class="icon-title">{{ item.title }}</div>
              <div class="count-description">
                {{ item.count }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :lg="8" :md="12" :sm="24" :xl="6" :xs="24">
        <el-card shadow="never">
          <div slot="header">
            <div style="font-size: 1rem">经费使用情况</div>
          </div>
          <div class="chart-container">
            <div class="chart-content">
              <vab-chart autoresize :option="fwl" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :lg="16" :md="12" :sm="24" :xl="6" :xs="24">
        <el-card shadow="never">
          <div slot="header">
            <div style="font-size: 1rem">成果转化情况</div>
          </div>
          <div class="chart-container">
            <div class="chart-content">
              <vab-chart autoresize :option="sqs" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div style="
      margin-left: 20px;
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 10px;
      border-bottom: 1px solid #ccc;
      background-color: #abeef6;
      padding: 5px;
      border-radius: 5px;
      border-left: 5px solid #88C5FF;
    ">
      专家信息
    </div>
    <el-row :gutter="20">
      <el-col :lg="12" :md="12" :sm="24" :xl="6" :xs="24">
        <el-card shadow="never">
          <div slot="header">
            <div style="font-size: 1rem">专家所属领域</div>
          </div>
          <div class="chart-container">
            <div class="chart-content">
              <vab-chart autoresize :option="filed" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :lg="12" :md="12" :sm="24" :xl="6" :xs="24">
        <el-card shadow="never">
          <div slot="header">
            <div style="font-size: 1rem">专家来源</div>
          </div>
          <div class="chart-container">
            <div class="chart-content">
              <vab-chart autoresize :option="origin" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </el-row>
</div>
</template>

<script>
import VabChart from '@/plugins/echarts'

export default {
  name: 'Index',
  components: {
    VabChart,
  },
  data() {
    return {
      yearSelect: '2023',
      // 经费使用情况
      fwl: {
        tooltip: {
          trigger: 'item',
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          top: '0%',
        },
        series: [
          {
            itemStyle: {
              normal: {
                color: function (params) {
                  return ['#4874CB', '#EE822F'][params.dataIndex]
                },
              },
            },
            type: 'pie',
            center: '50%',
            radius: '100%',
            label: {
              normal: {
                position: 'inner',
                color: '#000',
                fontSize: 18,
                formatter: (params) => {
                  return `${params.name}\n${params.value}`
                },
              },
            },
            data: [
              { value: 12.1, name: '已支出经费' },
              { value: 45.5, name: '待使用经费' },
            ],
            emphasis: {
              disabled: true, // 禁用悬浮高亮效果
            },
          },
        ],
      },
      sqs: {
        tooltip: {
          trigger: 'item',
        },
        backgroundColor: 'transparent',
        grid: {
          top: '15%',
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: ['课题结项数量', '输出报告数量', '研究成果数量'],
            axisTick: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                color: '#E4E7ED',
              },
            },
            axisLabel: {
              color: '#909399',
              fontSize: 18,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              color: '#909399',
              fontSize: 11,
            },
            splitLine: {
              lineStyle: {
                color: '#F5F7FA',
                type: 'dashed',
              },
            },
          },
        ],
        series: [
          {
            name: '成果转化情况',
            type: 'bar',
            barWidth: '30%',
            data: [5, 16, 17],
            itemStyle: {
              color: '#4874CB',
            },
            label: {
              show: true,
              position: 'top',
              color: '#333',
              fontSize: 12,
              fontWeight: 'bold',
            },
          },
        ],
      },
      filed: {
        tooltip: {
          trigger: 'item',
        },
        title: {
          text: '专家数量\n17个',
          x: 'center',
          y: '35%',
          textStyle: {
            fontSize: 18,
          },
        },
        legend: {
          show: true,
          bottom: 0,
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '40%'],
            radius: ['40%', '80%'],
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: true,
              position: 'inside',
              formatter: '{c}',
              fontSize: 16,
              fontWeight: 'bold',
            },
            data: [
              {
                name: '汽车制造',
                value: 7,
              },
              {
                name: '财务管理',
                value: 2,
              },
              {
                name: '生物医药',
                value: 1,
              },
              {
                name: '新材料',
                value: 3,
              },
              {
                name: '先进制造',
                value: 4,
              },
            ],
          },
        ],
      },
      origin: {
        tooltip: {
          trigger: 'item',
        },

        legend: {
          bottom: 0,
        },
        series: [
          {
            name: '专家来源',
            type: 'pie',
            radius: '80%',
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: true,
              position: 'inside',
              formatter: '{c}',
              fontSize: 16,
              fontWeight: 'bold',
            },
            data: [
              { value: 5, name: '西安交通大学' },
              { value: 4, name: '中国石油' },
              { value: 2, name: '国资委研究院' },
              { value: 2, name: '陕汽' },
              { value: 4, name: '西安财经大学' },
            ],
          },
        ],
      },
      //卡片图标
      iconList: [
        {
          icon: 'book',
          title: '总课题数量',
          color: '#ffc069',
          count: '29',
        },
        {
          icon: 'book',
          title: '重大课题数量',
          color: '#5cdbd3',
          count: '9',
        },
        {
          icon: 'book',
          title: '进行中课题数量',
          color: '#b37feb',
          count: '6',
        },
        {
          icon: 'book',
          title: '已完成课题数量',
          color: '#69c0ff',
          count: '2',
        },
        {
          icon: 'book',
          title: '内部课题数量',
          color: '#ffc069',
          count: '12',
        },
        {
          icon: 'book',
          title: '委托课题数量',
          color: '#5cdbd3',
          count: '17',
        },
        {
          icon: 'book',
          title: '输出报告数量',
          color: '#b37feb',
          count: '16',
        },
        {
          icon: 'book',
          title: '研究成果数量',
          color: '#69c0ff',
          count: '17',
        },
      ],
    }
  },
  methods: {
    linear(color) {
      return `linear-gradient(to right, #A18DF9 0%, ${color} 100%)`
    },
  },
}
</script>
<style lang="scss" scoped>
.index-container {
  padding: 0 !important;
  margin: 0 !important;
  background: #f5f7f8 !important;

  ::v-deep {
    .el-alert {
      padding: $base-padding;

      &--info.is-light {
        min-height: 82px;
        padding: $base-padding;
        margin-bottom: 15px;
        color: #909399;
        background-color: $base-color-white;
        border: 1px solid #ebeef5;
        font-size: 14px !important;

        * {
          font-size: 14px !important;
        }
      }
    }

    .el-card__body {
      .echarts {
        width: 100%;
        height: 115px;
      }
    }
  }

  .card {
    height: 600px;
    display: flex;
    flex-direction: column;

    ::v-deep {
      .el-card__body {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .echarts {
          width: 100%;
          height: 305px;
        }
      }
    }

    .dependency-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow-y: auto;

      .dependency-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 25px;
        flex-shrink: 0;

        .dependency-item {
          display: flex;
          align-items: center;
          padding: 15px;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            background: #ffffff;
          }

          .dependency-info {
            flex: 1;
            text-align: center;

            .dependency-name {
              font-size: 0.9rem;
              color: #6c757d;
              margin-bottom: 6px;
              font-weight: 500;
            }

            .dependency-version {
              font-size: 1.1rem;
              color: #2c3e50;
              font-weight: 600;
            }
          }
        }
      }

      .system-info {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        flex-shrink: 0;

        .info-header {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
          padding-bottom: 10px;
          border-bottom: 2px solid #dee2e6;

          .vab-icon {
            color: #677ae4;
            margin-right: 3px;
            font-size: 1.1rem;
          }

          span {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
          }
        }

        .info-content {
          .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;

            &:last-child {
              border-bottom: none;
            }

            .info-label {
              color: #6c757d;
              font-weight: 500;
              font-size: 0.9rem;
            }

            .info-value {
              color: #2c3e50;
              font-weight: 600;
              font-size: 0.9rem;
              max-width: 60%;
              text-align: right;
              word-break: break-all;
            }
          }
        }
      }
    }
  }

  .bottom {
    padding-top: 20px;
    margin-top: 5px;
    color: #595959;
    text-align: left;
    border-top: 1px solid $base-border-color;
  }

  .table {
    width: 100%;
    color: #666;
    border-collapse: collapse;
    background-color: #fff;

    td {
      position: relative;
      min-height: 20px;
      padding: 9px 15px;
      font-size: 14px;
      line-height: 20px;
      border: 1px solid #e6e6e6;

      &:nth-child(odd) {
        width: 20%;
        text-align: right;
        background-color: #f7f7f7;
      }
    }
  }

  .icon-panel {
    height: 117px;
    text-align: center;
    cursor: pointer;

    svg {
      font-size: 40px;
    }

    p {
      margin-top: 10px;
    }
  }

  .bottom-btn {
    button {
      margin: 5px 10px 15px 0;
    }
  }

  // 高级信息卡片样式
  .advanced-info-card {
    height: 600px;
    overflow: hidden;

    .advanced-content {
      height: 100%;
      padding: 30px 25px;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      overflow-y: auto;

      .logo-section {
        text-align: center;
        margin-bottom: 30px;
        flex-shrink: 0;

        .logo-container {
          position: relative;
          display: inline-block;
          margin-bottom: 20px;

          .advanced-logo {
            font-size: 120px;
            position: relative;
            z-index: 2;
          }

          .logo-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 140px;
            height: 140px;
            background: radial-gradient(circle, rgba(64, 158, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
          }
        }

        .project-title {
          font-size: 2rem;
          font-weight: 700;
          color: #2c3e50;
          margin: 0 0 10px 0;
        }

        .project-description {
          color: #7f8c8d;
          font-size: 1rem;
          margin: 0;
        }
      }

      .stats-section {
        display: flex;
        justify-content: space-around;
        margin-bottom: 30px;
        padding: 20px;
        background: #f8f9fa;
        flex-shrink: 0;

        .stat-card {
          text-align: center;
          padding: 15px;
          transition: all 0.3s ease;

          .stat-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #677ae4, #8b5cf6);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            color: white;
            font-size: 1.2rem;
          }

          .stat-info {
            .stat-number {
              font-size: 1.5rem;
              font-weight: 700;
              color: #2c3e50;
              margin-bottom: 5px;
            }

            .stat-label {
              font-size: 0.9rem;
              color: #7f8c8d;
            }
          }
        }
      }

      .action-section {
        flex: 1;
        overflow-y: auto;

        .action-group {
          margin-bottom: 25px;

          &:last-child {
            margin-bottom: 0;
          }

          .action-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #ecf0f1;
          }

          .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;

            .action-btn {
              flex: 1;
              min-width: 200px;
              height: 45px;
              border-radius: 10px;
              font-weight: 600;
              transition: all 0.3s ease;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
              }

              &.primary-btn {
                background: #677ae4;
                border: none;
                color: white;

                &:hover {
                  background: #5a67d8;
                }
              }

              &.premium-btn {
                background: linear-gradient(45deg, #f093fb, #f5576c);
                border: none;
                color: white;

                &:hover {
                  background: linear-gradient(45deg, #e085e7, #e54b5f);
                }
              }
            }

            .premium-card {
              flex: 1;
              min-width: 180px;
              background: linear-gradient(135deg, #677ae4 0%, #8b5cf6 100%);
              border-radius: 15px;
              padding: 20px;
              cursor: pointer;
              transition: all 0.3s ease;
              position: relative;
              overflow: hidden;
              color: white;
              border: none;

              &:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(103, 122, 228, 0.3);
              }

              &.featured {
                background: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);

                &:hover {
                  box-shadow: 0 10px 25px rgba(230, 162, 60, 0.3);
                }

                .premium-badge {
                  background: rgba(255, 255, 255, 0.2);
                  color: #fff;
                }
              }

              .premium-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;

                .premium-icon {
                  font-size: 1.5rem;
                  color: rgba(255, 255, 255, 0.9);
                }

                .premium-badge {
                  background: rgba(255, 255, 255, 0.2);
                  color: rgba(255, 255, 255, 0.9);
                  padding: 4px 8px;
                  border-radius: 12px;
                  font-size: 0.7rem;
                  font-weight: 600;
                  letter-spacing: 0.5px;
                }
              }

              .premium-content {
                .premium-title {
                  font-size: 1.1rem;
                  font-weight: 700;
                  margin-bottom: 8px;
                  color: white;
                }

                .premium-price {
                  font-size: 1.3rem;
                  font-weight: 800;
                  margin-bottom: 5px;
                  color: white;
                }
              }
            }
          }
        }
      }
    }
  }

  // 图表容器高级样式
  .chart-container {
    height: 400px;
    display: flex;
    flex-direction: column;

    .chart-content {
      flex: 1;
      margin-bottom: 15px;
      min-height: 0;

      .echarts {
        height: 100% !important;
        min-height: 120px;
      }
    }
  }

  // 图表卡片固定高度
  .el-card {
    height: 400px;
    display: flex;
    flex-direction: column;

    ::v-deep {
      .el-card__body {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }

  // 高级信息卡片和依赖信息卡片不受影响
  .advanced-info-card,
  .card {
    height: auto !important;
  }

  // 图标卡片高级样式
  .icon-card {
    height: 130px;
    display: flex;
    flex-direction: column;

    .icon-container {
      height: 100%;
      display: flex;
      cursor: pointer;
      transition: all 0.3s ease;
      align-items: center;
      justify-content: space-around;

      &:hover {
        transform: translateY(-3px);
      }

      .icon-header {
        text-align: center;
        flex-shrink: 0;

        .icon-wrapper {
          width: 60px;
          height: 60px;
          border-radius: 15px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 12px;
          color: white;
          font-size: 1.5rem;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.1);
          }
        }
      }

      .count-content {
        min-height: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .count-description {
          font-size: 2rem;
          color: #000;
          text-align: center;
          line-height: 1.4;
          padding: 0 10px;
          height: 100%;
        }

        .icon-title {
          font-size: 1rem;
          // font-weight: 600;
          color: #2c3e50;
          margin: 0;
        }
      }

      .icon-footer {
        flex-shrink: 0;

        .icon-stats {
          display: flex;
          justify-content: space-around;
          align-items: center;
          padding: 10px 0;
          border-top: 1px solid #f0f0f0;

          .stat-item {
            display: flex;
            align-items: center;
            font-size: 0.8rem;
            color: #7f8c8d;

            .vab-icon {
              margin-right: 3px;
              font-size: 0.7rem;
            }

            span {
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}

// 动画
@keyframes pulse {

  0%,
  100% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.1);
  }
}
</style>
