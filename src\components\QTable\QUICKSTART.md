# QTable 快速开始指南

## 🚀 5分钟上手 QTable

### 第一步：导入组件

```javascript
import QTable from '@/components/QTable'

export default {
  components: {
    QTable
  }
}
```

### 第二步：准备数据

```javascript
data() {
  return {
    // 表格数据
    tableData: [
      { id: 1, name: '张三', age: 25, status: 0 },
      { id: 2, name: '李四', age: 30, status: 1 }
    ],
    
    // 列配置
    columns: [
      { label: 'ID', prop: 'id', width: '80' },
      { label: '姓名', prop: 'name', width: '120' },
      { label: '年龄', prop: 'age', width: '80' }
    ]
  }
}
```

### 第三步：使用组件

```vue
<template>
  <QTable
    :data="tableData"
    :columns="columns"
    :config="{ stripe: true, border: true }"
  />
</template>
```

## 🎯 常用场景

### 场景1：数据列表展示

```vue
<QTable
  :data="userList"
  :columns="[
    { label: 'ID', prop: 'id', width: '80' },
    { label: '用户名', prop: 'username', width: '120' },
    { label: '邮箱', prop: 'email', width: '200', showOverflowTooltip: true },
    { label: '创建时间', prop: 'createTime', width: '150' }
  ]"
  :config="{ stripe: true, border: true }"
/>
```

### 场景2：状态管理表格

```vue
<QTable
  :data="taskList"
  :columns="[
    { label: '任务名', prop: 'name', width: '200' },
    { label: '状态', type: 'tag', width: '100' },
    { label: '负责人', prop: 'assignee', width: '120' }
  ]"
  :status-config="['info', 'success', 'warning']"
  :tag-config="['待处理', '已完成', '进行中']"
  :config="{ stripe: true }"
/>
```

### 场景3：可选择的数据表格

```vue
<QTable
  :data="selectableData"
  :columns="[
    { type: 'selection' },
    { label: '名称', prop: 'name', width: '150' },
    { label: '类型', prop: 'type', width: '100' }
  ]"
  @selection-change="handleSelection"
/>
```

### 场景4：带操作的管理表格

```vue
<QTable
  :data="managementData"
  :columns="[
    { label: 'ID', prop: 'id', width: '80' },
    { label: '名称', prop: 'name', width: '150' }
  ]"
>
  <template #operation>
    <el-table-column label="操作" width="150">
      <template slot-scope="scope">
        <el-button size="mini" @click="edit(scope.row)">编辑</el-button>
        <el-button size="mini" type="danger" @click="remove(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </template>
</QTable>
```

## 📝 配置速查表

### 列类型 (type)
- `'selection'` - 选择列
- `'tag'` - 状态标签列
- 不设置 - 普通文本列

### 表格配置 (config)
```javascript
{
  stripe: true,        // 斑马纹
  border: true,        // 边框
  height: '400px',     // 固定高度
  style: {}           // 自定义样式
}
```

### 状态标签配置
```javascript
// Element UI 标签类型
statusConfig: ['info', 'success', 'warning', 'danger']

// 对应显示文本
tagConfig: ['默认', '成功', '警告', '危险']
```

## ⚡ 性能提示

1. **大数据量**：设置固定高度启用虚拟滚动
```javascript
config: { height: '400px' }
```

2. **长文本**：启用溢出提示
```javascript
{ label: '描述', prop: 'desc', showOverflowTooltip: true }
```

3. **避免模板计算**：在数据处理时预格式化
```javascript
// ❌ 避免
{{ formatDate(scope.row.time) }}

// ✅ 推荐
computed: {
  formattedData() {
    return this.rawData.map(item => ({
      ...item,
      formattedTime: this.formatDate(item.time)
    }))
  }
}
```

## 🔧 调试技巧

### 1. 检查数据结构
```javascript
console.log('表格数据:', this.tableData)
console.log('列配置:', this.columns)
```

### 2. 验证状态配置
```javascript
// 确保索引对应
console.log('状态值:', item.status)
console.log('标签类型:', this.statusConfig[item.status])
console.log('标签文本:', this.tagConfig[item.status])
```

### 3. 监听选择事件
```javascript
handleSelectionChange(row) {
  console.log('选中行:', row)
  console.log('选中状态:', row.isSelected)
}
```

## 🆘 常见错误

### 错误1：状态标签不显示
**原因**：statusConfig 或 tagConfig 配置错误
**解决**：检查数组长度和索引对应关系

### 错误2：选择功能无效
**原因**：数据缺少 isSelected 字段
**解决**：为每个数据项添加 `isSelected: false`

### 错误3：操作列不显示
**原因**：插槽使用错误
**解决**：使用 `<template #operation>` 包裹操作列

## 📚 更多资源

- [完整文档](./README.md)
- [示例代码](./example.vue)
- [Element UI 表格文档](https://element.eleme.cn/#/zh-CN/component/table)
